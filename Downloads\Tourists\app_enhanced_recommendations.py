"""
Enhanced Tourist Recommendation & Rating Prediction System
Combines rating prediction with comprehensive recommendation engine
"""

import streamlit as st
import pandas as pd
import numpy as np
import joblib
from textblob import TextBlob
import plotly.express as px
import plotly.graph_objects as go
from recommendation_system import TouristRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Smart Tourist Recommendation System",
    page_icon="🏖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Clean UI styling with improved colors
st.markdown("""
<style>
    /* Subtle improvements to match the clean theme */
    .stButton > button {
        background-color: #1f77b4 !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }

    .stButton > button:hover {
        background-color: #1565c0 !important;
        box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3) !important;
    }

    /* Success/Info messages */
    .stSuccess {
        background-color: #d4edda !important;
        border-color: #c3e6cb !important;
        color: #155724 !important;
    }

    .stInfo {
        background-color: #d1ecf1 !important;
        border-color: #bee5eb !important;
        color: #0c5460 !important;
    }

    /* Metric improvements */
    [data-testid="metric-container"] {
        background-color: #f8f9fa !important;
        border: 1px solid #e9ecef !important;
        padding: 1rem !important;
        border-radius: 8px !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    }

    /* Header styling */
    .main-header {
        color: #1f77b4 !important;
        text-align: center !important;
        font-weight: 600 !important;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'rec_system' not in st.session_state:
    st.session_state.rec_system = None
    st.session_state.models_loaded = False

# Load models
@st.cache_resource
def load_prediction_model():
    """Load the trained XGBoost model"""
    try:
        model = joblib.load('xgboost_model.pkl')
        feature_names = joblib.load('feature_names.pkl')
        return model, feature_names
    except FileNotFoundError:
        return None, None

@st.cache_resource
def load_recommendation_system():
    """Load and train the recommendation system"""
    rec_system = TouristRecommendationSystem()
    if rec_system.train_all_models():
        return rec_system
    return None

# Load data
@st.cache_data
def load_data():
    try:
        df = pd.read_csv('cleaned_data.csv')
        return df
    except FileNotFoundError:
        st.error("❌ cleaned_data.csv file not found.")
        return None

# Sentiment analysis
def get_sentiment_score(text):
    if pd.isna(text) or text == "":
        return 0.0
    blob = TextBlob(str(text))
    return blob.sentiment.polarity

# Feature engineering
def prepare_features(state, dest_type, best_time, popularity, gender, preferences, 
                    num_adults, num_children, experience_rating, review_text):
    
    # Selected features from training
    selected_features = [
        'Type_City', 'Type_Beach', 'State_Uttar Pradesh', 'Gender_Male', 
        'Type_Historical', 'State_Rajasthan', 'State_Goa', 
        'Preferences_City, Historical', 'State_Kerala', 
        'Preferences_Nature, Adventure', 'State_Jammu and Kashmir', 
        'Preferences_Beaches, Historical', 'NumberOfAdults', 
        'SentimentScore', 'Gender_Female', 'NumberOfChildren', 'Popularity'
    ]
    
    # Initialize feature vector
    features = {}
    
    # One-hot encode categorical features
    features['Type_City'] = 1 if dest_type == 'City' else 0
    features['Type_Beach'] = 1 if dest_type == 'Beach' else 0
    features['Type_Historical'] = 1 if dest_type == 'Historical' else 0
    
    features['State_Uttar Pradesh'] = 1 if state == 'Uttar Pradesh' else 0
    features['State_Rajasthan'] = 1 if state == 'Rajasthan' else 0
    features['State_Goa'] = 1 if state == 'Goa' else 0
    features['State_Kerala'] = 1 if state == 'Kerala' else 0
    features['State_Jammu and Kashmir'] = 1 if state == 'Jammu and Kashmir' else 0
    
    features['Gender_Male'] = 1 if gender == 'Male' else 0
    features['Gender_Female'] = 1 if gender == 'Female' else 0
    
    features['Preferences_City, Historical'] = 1 if preferences == 'City, Historical' else 0
    features['Preferences_Nature, Adventure'] = 1 if preferences == 'Nature, Adventure' else 0
    features['Preferences_Beaches, Historical'] = 1 if preferences == 'Beaches, Historical' else 0
    
    # Numerical features
    features['NumberOfAdults'] = num_adults
    features['NumberOfChildren'] = num_children
    features['Popularity'] = popularity
    features['SentimentScore'] = get_sentiment_score(review_text)
    
    # Create feature vector with only selected features
    feature_vector = [features.get(feature, 0) for feature in selected_features]
    
    return np.array(feature_vector).reshape(1, -1)

def predict_rating(features, model):
    """Predict rating using the trained XGBoost model"""
    try:
        prediction = model.predict(features) + 1
        return int(prediction[0])
    except Exception as e:
        st.error(f"Error making prediction: {str(e)}")
        return 3

# Main app
def main():
    # Clean header matching the theme
    st.markdown("""
    <div style="text-align: center; padding: 1rem 0 2rem 0;">
        <h1 style="color: #1f77b4; font-size: 2.5rem; margin: 0; font-weight: 600;">
            🏖️ Smart Tourist Recommendation System
        </h1>
        <p style="color: #666; font-size: 1.1rem; margin: 0.5rem 0 0 0;">
            Advanced ML-powered tourist recommendations and rating predictions
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Load models
    model, feature_names = load_prediction_model()
    
    if model is None:
        st.error("❌ Prediction model not found. Please train the model first.")
        return
    
    # Load recommendation system
    if not st.session_state.models_loaded:
        with st.spinner("🔄 Loading recommendation models..."):
            st.session_state.rec_system = load_recommendation_system()
            st.session_state.models_loaded = True
    
    if st.session_state.rec_system is None:
        st.warning("⚠️ Recommendation system not available. Only rating prediction will work.")
    
    # Sidebar for navigation
    st.sidebar.title("🎯 Navigation")
    page = st.sidebar.selectbox(
        "Choose a feature:",
        ["🔮 Rating Prediction", "🎯 Get Recommendations", "📊 System Analytics"]
    )
    
    if page == "🔮 Rating Prediction":
        show_rating_prediction(model, feature_names)
    elif page == "🎯 Get Recommendations":
        show_recommendations()
    elif page == "📊 System Analytics":
        show_analytics()

def show_rating_prediction(model, feature_names):
    """Show rating prediction interface"""
    st.markdown('<h2 class="sub-header">🔮 Tourist Rating Prediction</h2>', unsafe_allow_html=True)
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("### 🏖️ Destination Information")
        state = st.selectbox("State:", ['Goa', 'Kerala', 'Rajasthan', 'Uttar Pradesh', 'Jammu and Kashmir'])
        dest_type = st.selectbox("Destination Type:", ['City', 'Beach', 'Historical', 'Nature', 'Adventure'])
        best_time = st.selectbox("Best Time to Visit:", ['Nov-Mar', 'Oct-Mar', 'Sep-Mar', 'Nov-Feb', 'Apr-Jun'])
        popularity = st.slider("Destination Popularity:", 7.5, 9.5, 8.5, 0.1)
        
        st.markdown("### 👤 User Information")
        gender = st.selectbox("Gender:", ['Male', 'Female'])
        preferences = st.selectbox("Travel Preferences:", 
                                 ['City, Historical', 'Nature, Adventure', 'Beaches, Historical'])
        num_adults = st.number_input("Number of Adults:", 1, 10, 2)
        num_children = st.number_input("Number of Children:", 0, 10, 1)
        
        st.markdown("### 📝 Experience")
        experience_rating = st.slider("Previous Experience Rating:", 1, 5, 4)
        review_text = st.text_area("Review Text (for sentiment analysis):", 
                                 "Amazing place with beautiful scenery!")
    
    with col2:
        st.markdown("### 📊 Feature Analysis")
        
        # Prepare features
        features = prepare_features(state, dest_type, best_time, popularity, gender, 
                                  preferences, num_adults, num_children, 
                                  experience_rating, review_text)
        
        # Display feature summary
        sentiment_score = get_sentiment_score(review_text)
        
        feature_data = {
            'Feature': ['Gender_Male', 'Type_Historical', 'State_Rajasthan', 'State_Goa', 
                       'Preferences_City, Historical', 'State_Kerala', 'Preferences_Nature, Adventure',
                       'State_Jammu and Kashmir', 'Preferences_Beaches, Historical', 
                       'NumberOfAdults', 'SentimentScore', 'NumberOfChildren', 'Popularity'],
            'Value': [1 if gender == 'Male' else 0,
                     1 if dest_type == 'Historical' else 0,
                     1 if state == 'Rajasthan' else 0,
                     1 if state == 'Goa' else 0,
                     1 if preferences == 'City, Historical' else 0,
                     1 if state == 'Kerala' else 0,
                     1 if preferences == 'Nature, Adventure' else 0,
                     1 if state == 'Jammu and Kashmir' else 0,
                     1 if preferences == 'Beaches, Historical' else 0,
                     num_adults, sentiment_score, num_children, popularity]
        }
        
        feature_df = pd.DataFrame(feature_data)
        st.dataframe(feature_df, use_container_width=True)
        
        # Sentiment analysis
        if sentiment_score > 0:
            st.success(f"😊 Positive sentiment detected! Score: {sentiment_score:.2f}")
        elif sentiment_score < 0:
            st.error(f"😞 Negative sentiment detected! Score: {sentiment_score:.2f}")
        else:
            st.info("😐 Neutral sentiment")
        
        # Prediction
        if st.button("🔮 Predict Rating", type="primary", use_container_width=True):
            predicted_rating = predict_rating(features, model)

            # Clean prediction box matching the theme
            rating_colors = {
                1: "#dc3545",  # Red
                2: "#fd7e14",  # Orange
                3: "#ffc107",  # Yellow
                4: "#28a745",  # Green
                5: "#007bff"   # Blue
            }
            color = rating_colors.get(predicted_rating, rating_colors[3])

            st.markdown(f"""
            <div style="background-color: #f8f9fa;
                       padding: 2rem; border-radius: 10px; margin: 1rem 0;
                       border-left: 5px solid {color};
                       box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                       text-align: center;">
                <h3 style="margin: 0 0 1rem 0; color: #495057;">
                    🎯 Prediction Result
                </h3>
                <div style="font-size: 3rem; margin: 1rem 0; color: {color}; font-weight: bold;">
                    {predicted_rating} ⭐
                </div>
                <p style="font-size: 1.1rem; margin: 0; color: #6c757d;">
                    Predicted Rating
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            # Rating interpretation
            rating_meanings = {
                1: "⭐ Poor - Significant improvements needed",
                2: "⭐⭐ Fair - Below expectations", 
                3: "⭐⭐⭐ Good - Meets expectations",
                4: "⭐⭐⭐⭐ Very Good - Exceeds expectations",
                5: "⭐⭐⭐⭐⭐ Excellent - Outstanding experience!"
            }
            
            st.info(rating_meanings[predicted_rating])

def show_recommendations():
    """Show recommendation interface"""
    st.markdown('<h2 class="sub-header">🎯 Smart Tourist Recommendations</h2>', unsafe_allow_html=True)
    
    if st.session_state.rec_system is None:
        st.error("❌ Recommendation system not available. Please check data files.")
        return
    
    # Recommendation type selection
    rec_type = st.selectbox(
        "Choose recommendation type:",
        ["🔄 Hybrid Recommendations", "📝 Content-Based", "👥 Collaborative Filtering", "🎯 Cluster-Based"]
    )
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("### 🎯 Your Preferences")
        
        if rec_type in ["🔄 Hybrid Recommendations", "🎯 Cluster-Based"]:
            user_state = st.selectbox("Preferred State:", 
                                    ['Goa', 'Kerala', 'Rajasthan', 'Uttar Pradesh', 'Jammu and Kashmir'])
            user_type = st.selectbox("Preferred Type:", 
                                   ['City', 'Beach', 'Historical', 'Nature', 'Adventure'])
            
        if rec_type in ["🔄 Hybrid Recommendations", "📝 Content-Based"]:
            destination_id = st.number_input("Reference Destination ID (optional):", 
                                           min_value=1, max_value=100, value=1)
            
        if rec_type in ["🔄 Hybrid Recommendations", "👥 Collaborative Filtering"]:
            user_id = st.number_input("User ID (for collaborative filtering):", 
                                    min_value=1, max_value=1000, value=1)
        
        num_recommendations = st.slider("Number of recommendations:", 1, 10, 5)
        
        if st.button("🎯 Get Recommendations", type="primary"):
            with st.spinner("🔄 Generating recommendations..."):
                recommendations = []
                
                if rec_type == "🔄 Hybrid Recommendations":
                    user_prefs = {'state': user_state, 'type': user_type}
                    recommendations = st.session_state.rec_system.get_hybrid_recommendations(
                        user_id=user_id,
                        destination_id=destination_id,
                        user_preferences=user_prefs,
                        n_recommendations=num_recommendations
                    )
                elif rec_type == "📝 Content-Based":
                    recommendations = st.session_state.rec_system.get_content_recommendations(
                        destination_id, num_recommendations
                    )
                elif rec_type == "👥 Collaborative Filtering":
                    recommendations = st.session_state.rec_system.get_collaborative_recommendations(
                        user_id, num_recommendations
                    )
                elif rec_type == "🎯 Cluster-Based":
                    user_prefs = {'state': user_state, 'type': user_type}
                    recommendations = st.session_state.rec_system.get_cluster_recommendations(
                        user_prefs, num_recommendations
                    )
                
                # Store recommendations in session state
                st.session_state.current_recommendations = recommendations
    
    with col2:
        st.markdown("### 🎯 Recommendations")
        
        if hasattr(st.session_state, 'current_recommendations') and st.session_state.current_recommendations:
            for i, rec in enumerate(st.session_state.current_recommendations, 1):
                score_info = ""
                if 'similarity_score' in rec:
                    score_info = f"Similarity: {rec['similarity_score']:.3f}"
                elif 'predicted_rating' in rec:
                    score_info = f"Predicted Rating: {rec['predicted_rating']:.2f}"
                elif 'score' in rec:
                    score_info = f"Score: {rec['score']:.3f}"

                # Clean color coding based on source
                colors = {
                    'content': '#007bff',      # Blue
                    'collaborative': '#28a745', # Green
                    'cluster': '#ffc107',      # Yellow
                    'hybrid': '#dc3545'        # Red
                }
                border_color = colors.get(rec.get('source', 'content'), colors['content'])

                st.markdown(f"""
                <div style="background-color: #f8f9fa;
                           padding: 1.5rem; border-radius: 8px; margin: 1rem 0;
                           border-left: 4px solid {border_color};
                           box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="margin: 0 0 1rem 0; color: #495057;">#{i} {rec.get('Name', 'Unknown')}</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <p style="margin: 0.5rem 0; color: #6c757d;">
                                <strong>📍 Location:</strong> {rec.get('State', 'N/A')}
                            </p>
                            <p style="margin: 0.5rem 0; color: #6c757d;">
                                <strong>🏷️ Type:</strong> {rec.get('Type', 'N/A')}
                            </p>
                        </div>
                        <div>
                            <p style="margin: 0.5rem 0; color: #6c757d;">
                                <strong>⭐ Popularity:</strong> {rec.get('Popularity', 'N/A')}
                            </p>
                            <p style="margin: 0.5rem 0; color: #6c757d;">
                                <strong>📊 {score_info}</strong>
                            </p>
                        </div>
                    </div>
                    {f'<p style="margin: 1rem 0 0 0; color: {border_color}; font-weight: 500;"><strong>🔍 Source:</strong> {rec.get("source", "N/A").title()}</p>' if 'source' in rec else ""}
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("👆 Click 'Get Recommendations' to see personalized suggestions!")

def show_analytics():
    """Show system analytics and performance metrics"""
    st.markdown('<h2 class="sub-header">📊 System Analytics</h2>', unsafe_allow_html=True)
    
    # Load data for analytics
    df = load_data()
    if df is None:
        st.error("❌ Cannot load data for analytics")
        return
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div style="background: linear-gradient(45deg, #667eea, #764ba2);
                   padding: 1.5rem; border-radius: 15px; text-align: center;
                   color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
            <h3 style="margin: 0 0 1rem 0; color: white;">🎯 Model Accuracy</h3>
            <h2 style="margin: 0 0 0.5rem 0; font-size: 2.5rem; color: white;">54.77%</h2>
            <p style="margin: 0; color: rgba(255,255,255,0.8);">XGBoost Performance</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div style="background: linear-gradient(45deg, #f093fb, #f5576c);
                   padding: 1.5rem; border-radius: 15px; text-align: center;
                   color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
            <h3 style="margin: 0 0 1rem 0; color: white;">📊 Total Records</h3>
            <h2 style="margin: 0 0 0.5rem 0; font-size: 2.5rem; color: white;">{len(df)}</h2>
            <p style="margin: 0; color: rgba(255,255,255,0.8);">Training Data Size</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div style="background: linear-gradient(45deg, #4facfe, #00f2fe);
                   padding: 1.5rem; border-radius: 15px; text-align: center;
                   color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
            <h3 style="margin: 0 0 1rem 0; color: white;">🔧 Features Used</h3>
            <h2 style="margin: 0 0 0.5rem 0; font-size: 2.5rem; color: white;">17</h2>
            <p style="margin: 0; color: rgba(255,255,255,0.8);">Selected Features</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div style="background: linear-gradient(45deg, #43e97b, #38f9d7);
                   padding: 1.5rem; border-radius: 15px; text-align: center;
                   color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
            <h3 style="margin: 0 0 1rem 0; color: white;">🤖 Models</h3>
            <h2 style="margin: 0 0 0.5rem 0; font-size: 2.5rem; color: white;">4</h2>
            <p style="margin: 0; color: rgba(255,255,255,0.8);">Recommendation Types</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Visualizations
    st.markdown("### 📈 Data Distribution")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Rating distribution
        if 'Rating' in df.columns:
            fig = px.histogram(df, x='Rating', title="Rating Distribution", 
                             color_discrete_sequence=['#1f77b4'])
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # State distribution
        if 'State' in df.columns:
            state_counts = df['State'].value_counts()
            fig = px.pie(values=state_counts.values, names=state_counts.index, 
                        title="Destinations by State")
            st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    main()
