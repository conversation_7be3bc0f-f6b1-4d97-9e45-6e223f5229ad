import{r as s,E as p,_ as u,n as d,B as b,j as n,C as h,z as w,bs as g,b7 as x,b2 as E,bm as T,bn as y,aC as B}from"./index.BYo0ywlm.js";var v=s.forwardRef(function(t,e){var o={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return s.createElement(p,u({iconAttrs:o,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:e}),s.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),s.createElement("path",{d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"}))});v.displayName="Fullscreen";var f=s.forwardRef(function(t,e){var o={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return s.createElement(p,u({iconAttrs:o,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:e}),s.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),s.createElement("path",{d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"}))});f.displayName="FullscreenExit";const c="-2.65rem",z=d("div",{target:"et0utro0"})(({theme:t,locked:e,target:o})=>({padding:`${t.spacing.sm} 0 ${t.spacing.sm} ${t.spacing.sm}`,position:"absolute",top:e?c:"-1rem",right:t.spacing.none,transition:"none",...!e&&{opacity:0,"&:active, &:focus-visible, &:hover":{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:c},...o&&{[`${o}:hover &, ${o}:active &, ${o}:focus-visible &`]:{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:c}}}})),C=d("div",{target:"et0utro1"})(({theme:t})=>({color:b(t)?t.colors.fadedText60:t.colors.bodyText,display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-end",boxShadow:"1px 2px 8px rgba(0, 0, 0, 0.08)",borderRadius:t.radii.default,backgroundColor:t.colors.lightenedBg05,width:"fit-content",zIndex:t.zIndices.sidebar+1,padding:t.spacing.twoXS})),M=d("div",{target:"et0utro2"})(({width:t,height:e,useContainerWidth:o,topCentered:a})=>({position:"relative",height:o&&e?e:"fit-content",width:o?t:"fit-content",maxWidth:"100%",...a?{display:"flex",justifyContent:"center"}:{}}));function m({label:t,show_label:e,icon:o,onClick:a}){const l=w(),i=e?t:"";return n("div",{"data-testid":"stElementToolbarButton",children:n(g,{content:n(B,{source:t,allowHTML:!1,style:{fontSize:l.fontSizes.sm}}),placement:x.TOP,onMouseEnterDelay:1e3,inline:!0,children:h(y,{onClick:r=>{a&&a(),r.stopPropagation()},kind:E.ELEMENT_TOOLBAR,"aria-label":t,children:[o&&n(T,{content:o,size:"md",testid:"stElementToolbarButtonIcon"}),i&&n("span",{children:i})]})})})}const A=({onExpand:t,onCollapse:e,isFullScreen:o,locked:a,children:l,target:i,disableFullscreenMode:r})=>n(z,{className:"stElementToolbar","data-testid":"stElementToolbar",locked:a||o,target:i,children:h(C,{"data-testid":"stElementToolbarButtonContainer",children:[l,t&&!r&&!o&&n(m,{label:"Fullscreen",icon:v,onClick:()=>t()}),e&&!r&&o&&n(m,{label:"Close fullscreen",icon:f,onClick:()=>e()})]})});export{M as S,A as T,m as a};
