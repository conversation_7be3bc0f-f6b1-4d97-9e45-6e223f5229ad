#!/usr/bin/env python3
"""
Startup script for the Tourist Recommendation System
Handles environment setup and launches the enhanced app
"""

import sys
import subprocess
import os

def check_and_install_requirements():
    """Check if required packages are installed and install if needed"""
    try:
        import streamlit
        import pandas
        import numpy
        import sklearn
        import xgboost
        import textblob
        import plotly
        import joblib
        print("✅ All required packages are available")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Installing required packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ Packages installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install packages")
            return False

def run_app():
    """Run the enhanced recommendation app"""
    if check_and_install_requirements():
        print("🚀 Starting Tourist Recommendation System...")
        try:
            subprocess.run([sys.executable, "-m", "streamlit", "run", "app_enhanced_recommendations.py"])
        except KeyboardInterrupt:
            print("\n👋 App stopped by user")
        except Exception as e:
            print(f"❌ Error running app: {e}")
    else:
        print("❌ Cannot start app due to missing dependencies")

if __name__ == "__main__":
    run_app()
