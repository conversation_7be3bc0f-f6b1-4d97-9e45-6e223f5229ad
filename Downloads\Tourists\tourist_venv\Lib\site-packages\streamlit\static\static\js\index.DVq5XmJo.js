const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./data-grid-overlay-editor.CUwpDfvI.js","./index.BYo0ywlm.js","../css/index.CJVRHjQZ.css","./FormClearHelper.CsFEiTNN.js","./withFullScreenWrapper.CiQ10ByU.js","./Toolbar.gXKw7ANv.js","./checkbox.BUm2vnNv.js","./mergeWith.DzwwH6AG.js","./sprintf.D7DtBTRn.js","./createDownloadLinkElement.DZMwyjvU.js","./toConsumableArray.DUmnaVWV.js","./possibleConstructorReturn.CVfSu9Ws.js","./createSuper.KD4RuZ-W.js","./FileDownload.esm.BZQHC61b.js","./number-overlay-editor.Dx0XqCkD.js","./es6.Dlcvh_r0.js"])))=>i.map(i=>d[i]);
var dh=Object.defineProperty;var fh=(e,t,n)=>t in e?dh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var dt=(e,t,n)=>fh(e,typeof t!="symbol"?t+"":t,n);import{r as d,E as _s,_ as Ke,c as hh,g as wr,R as At,i as Ya,d as go,e as gh,f as mh,h as ph,m as Tc,o as Dc,p as vh,q as Oc,s as bh,t as wh,u as yh,v as Ch,w as Sh,x as xh,a as lr,y as Fs,n as xi,z as Gr,j as lt,F as Pc,A as Ca,T as Lc,B as Sa,C as _n,D as gr,G as xa,H as Wn,I as _c,J as gi,K as kh,L as Mh,M as $e,N as Fc,O as pt,Q as Zi,S as qr,U as Xa,V as Rh,W as Eh,X as Ih,Y as Ol,Z as Th,$ as Ac,a0 as Dh,a1 as Oh,a2 as Ss,a3 as Hc,a4 as zc,a5 as Ph,a6 as Lh,a7 as _h,a8 as Fh,a9 as Ah,aa as Hh,ab as zh,ac as Vh,ad as Nh,ae as Vc,af as $h,ag as Bh,ah as Wh,ai as Uh,aj as qh,ak as Yh,al as Xh,am as jh,an as Gh,ao as Kh,ap as Zh,aq as Jh,ar as Qh,as as Mn,l as As,at as Ji,au as yr,av as Ne,aw as eg,b as Nc,ax as $c,k as tg,ay as ng,az as rg,aA as ja,aB as ig,aC as og,aD as ag,aE as sg,aF as lg}from"./index.BYo0ywlm.js";import{u as ug}from"./FormClearHelper.CsFEiTNN.js";import{w as cg,E as dg}from"./withFullScreenWrapper.CiQ10ByU.js";import{T as fg,a as di}from"./Toolbar.gXKw7ANv.js";import{L as hg,S as gg,a as mg}from"./checkbox.BUm2vnNv.js";import{m as pg}from"./mergeWith.DzwwH6AG.js";import{s as vg}from"./sprintf.D7DtBTRn.js";import{c as bg}from"./createDownloadLinkElement.DZMwyjvU.js";import{_ as pr,a as Hs,C as wg}from"./toConsumableArray.DUmnaVWV.js";import{_ as yg,a as Cg,b as Sg}from"./possibleConstructorReturn.CVfSu9Ws.js";import{_ as xg}from"./createSuper.KD4RuZ-W.js";import{D as kg,F as Mg}from"./FileDownload.esm.BZQHC61b.js";var Bc=d.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(_s,Ke({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))});Bc.displayName="Add";var Wc=d.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(_s,Ke({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))});Wc.displayName="Search";var Uc=d.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(_s,Ke({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M12 6a9.77 9.77 0 018.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0112 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 010 5 2.5 2.5 0 010-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"}))});Uc.displayName="Visibility";function qc(e="This should not happen"){throw new Error(e)}function Fn(e,t="Assertion failed"){if(!e)return qc(t)}function ao(e,t){return qc(t??"Hell froze over")}function Rg(e,t){try{return e()}catch{return t}}const Pl=Object.prototype.hasOwnProperty;function Ci(e,t){let n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&Ci(e[r],t[r]););return r===-1}if(!n||typeof e=="object"){r=0;for(n in e)if(Pl.call(e,n)&&++r&&!Pl.call(t,n)||!(n in t)||!Ci(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}var Ga,Ll;function Eg(){if(Ll)return Ga;Ll=1;var e=Object.prototype,t=e.hasOwnProperty;function n(r,i){return r!=null&&t.call(r,i)}return Ga=n,Ga}var Ka,_l;function Ig(){if(_l)return Ka;_l=1;var e=Eg(),t=hh();function n(r,i){return r!=null&&t(r,i,e)}return Ka=n,Ka}var Tg=Ig();const Dg=wr(Tg),ia=null,zs=void 0;var te;(function(e){e.Uri="uri",e.Text="text",e.Image="image",e.RowID="row-id",e.Number="number",e.Bubble="bubble",e.Boolean="boolean",e.Loading="loading",e.Markdown="markdown",e.Drilldown="drilldown",e.Protected="protected",e.Custom="custom"})(te||(te={}));var Fl;(function(e){e.HeaderRowID="headerRowID",e.HeaderCode="headerCode",e.HeaderNumber="headerNumber",e.HeaderString="headerString",e.HeaderBoolean="headerBoolean",e.HeaderAudioUri="headerAudioUri",e.HeaderVideoUri="headerVideoUri",e.HeaderEmoji="headerEmoji",e.HeaderImage="headerImage",e.HeaderUri="headerUri",e.HeaderPhone="headerPhone",e.HeaderMarkdown="headerMarkdown",e.HeaderDate="headerDate",e.HeaderTime="headerTime",e.HeaderEmail="headerEmail",e.HeaderReference="headerReference",e.HeaderIfThenElse="headerIfThenElse",e.HeaderSingleValue="headerSingleValue",e.HeaderLookup="headerLookup",e.HeaderTextTemplate="headerTextTemplate",e.HeaderMath="headerMath",e.HeaderRollup="headerRollup",e.HeaderJoinStrings="headerJoinStrings",e.HeaderSplitString="headerSplitString",e.HeaderGeoDistance="headerGeoDistance",e.HeaderArray="headerArray",e.RowOwnerOverlay="rowOwnerOverlay",e.ProtectedColumnOverlay="protectedColumnOverlay"})(Fl||(Fl={}));var oa;(function(e){e.Triangle="triangle",e.Dots="dots"})(oa||(oa={}));function Ho(e){return"width"in e&&typeof e.width=="number"}async function Al(e){return typeof e=="object"?e:await e()}function mi(e){return!(e.kind===te.Loading||e.kind===te.Bubble||e.kind===te.RowID||e.kind===te.Protected||e.kind===te.Drilldown)}function vi(e){return e.kind===Yn.Marker||e.kind===Yn.NewRow}function Qi(e){if(!mi(e)||e.kind===te.Image)return!1;if(e.kind===te.Text||e.kind===te.Number||e.kind===te.Markdown||e.kind===te.Uri||e.kind===te.Custom||e.kind===te.Boolean)return e.readonly!==!0;ao(e,"A cell was passed with an invalid kind")}function Og(e){return Dg(e,"editor")}function Vs(e){return!(e.readonly??!1)}var Yn;(function(e){e.NewRow="new-row",e.Marker="marker"})(Yn||(Yn={}));function Pg(e){if(e.length===0)return[];const t=[...e],n=[];t.sort(function(r,i){return r[0]-i[0]}),n.push([...t[0]]);for(const r of t.slice(1)){const i=n[n.length-1];i[1]<r[0]?n.push([...r]):i[1]<r[1]&&(i[1]=r[1])}return n}let Hl;const mr=class mr{constructor(t){dt(this,"items");this.items=t}offset(t){if(t===0)return this;const n=this.items.map(r=>[r[0]+t,r[1]+t]);return new mr(n)}add(t){const n=typeof t=="number"?[t,t+1]:t,r=Pg([...this.items,n]);return new mr(r)}remove(t){const n=[...this.items],r=typeof t=="number"?t:t[0],i=typeof t=="number"?t+1:t[1];for(const[o,a]of n.entries()){const[l,s]=a;if(l<=i&&r<=s){const u=[];l<r&&u.push([l,r]),i<s&&u.push([i,s]),n.splice(o,1,...u)}}return new mr(n)}first(){if(this.items.length!==0)return this.items[0][0]}last(){if(this.items.length!==0)return this.items.slice(-1)[0][1]-1}hasIndex(t){for(let n=0;n<this.items.length;n++){const[r,i]=this.items[n];if(t>=r&&t<i)return!0}return!1}hasAll(t){for(let n=t[0];n<t[1];n++)if(!this.hasIndex(n))return!1;return!0}some(t){for(const n of this)if(t(n))return!0;return!1}equals(t){if(t===this)return!0;if(t.items.length!==this.items.length)return!1;for(let n=0;n<this.items.length;n++){const r=t.items[n],i=this.items[n];if(r[0]!==i[0]||r[1]!==i[1])return!1}return!0}toArray(){const t=[];for(const[n,r]of this.items)for(let i=n;i<r;i++)t.push(i);return t}get length(){let t=0;for(const[n,r]of this.items)t+=r-n;return t}*[Symbol.iterator](){for(const[t,n]of this.items)for(let r=t;r<n;r++)yield r}};dt(mr,"empty",()=>Hl??(Hl=new mr([]))),dt(mr,"fromSingleSelection",t=>mr.empty().add(t));let mt=mr;var Lg=function(){const t=Array.prototype.slice.call(arguments).filter(Boolean),n={},r=[];t.forEach(o=>{(o?o.split(" "):[]).forEach(l=>{if(l.startsWith("atm_")){const[,s]=l.split("_");n[s]=l}else r.push(l)})});const i=[];for(const o in n)Object.prototype.hasOwnProperty.call(n,o)&&i.push(n[o]);return i.push(...r),i.join(" ")},zl=Lg,_g=e=>e.toUpperCase()===e,Fg=e=>t=>e.indexOf(t)===-1,Yc=(e,t)=>{const n={};return Object.keys(e).filter(Fg(t)).forEach(r=>{n[r]=e[r]}),n};function Ag(e,t,n){const r=Yc(t,n);if(!e){const i=typeof Ya=="function"?{default:Ya}:Ya;Object.keys(r).forEach(o=>{i.default(o)||delete r[o]})}return r}var Hg=(e,t)=>{};function zg(e){let t="";return n=>{const r=(o,a)=>{const{as:l=e,class:s=t}=o,u=n.propsAsIs===void 0?!(typeof l=="string"&&l.indexOf("-")===-1&&!_g(l[0])):n.propsAsIs,c=Ag(u,o,["as","class"]);c.ref=a,c.className=n.atomic?zl(n.class,c.className||s):zl(c.className||s,n.class);const{vars:f}=n;if(f){const g={};for(const p in f){const v=f[p],w=v[0],b=v[1]||"",M=typeof w=="function"?w(o):w;Hg(M,n.name),g[`--${p}`]=`${M}${b}`}const h=c.style||{},m=Object.keys(h);m.length>0&&m.forEach(p=>{g[p]=h[p]}),c.style=g}return e.__linaria&&e!==l?(c.as=l,At.createElement(e,c)):At.createElement(l,c)},i=At.forwardRef?At.forwardRef(r):o=>{const a=Yc(o,["innerRef"]);return r(a,o.innerRef)};return i.displayName=n.name,i.__linaria={className:n.class||t,extends:e},i}}var mn=zg;const Vg=mn("div")({name:"ImageOverlayEditorStyle",class:"gdg-i2iowwq",propsAsIs:!1});var Za={},Vi={},zo={},Vo={},Vl;function Ng(){return Vl||(Vl=1,function(e){(function(t,n){n(e,go(),gh())})(Vo,function(t,n,r){Object.defineProperty(t,"__esModule",{value:!0}),t.setHasSupportToCaptureOption=m;var i=a(n),o=a(r);function a(b){return b&&b.__esModule?b:{default:b}}var l=Object.assign||function(b){for(var M=1;M<arguments.length;M++){var O=arguments[M];for(var S in O)Object.prototype.hasOwnProperty.call(O,S)&&(b[S]=O[S])}return b};function s(b,M){var O={};for(var S in b)M.indexOf(S)>=0||Object.prototype.hasOwnProperty.call(b,S)&&(O[S]=b[S]);return O}function u(b,M){if(!(b instanceof M))throw new TypeError("Cannot call a class as a function")}var c=function(){function b(M,O){for(var S=0;S<O.length;S++){var R=O[S];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(M,R.key,R)}}return function(M,O,S){return O&&b(M.prototype,O),S&&b(M,S),M}}();function f(b,M){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return M&&(typeof M=="object"||typeof M=="function")?M:b}function g(b,M){if(typeof M!="function"&&M!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof M);b.prototype=Object.create(M&&M.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),M&&(Object.setPrototypeOf?Object.setPrototypeOf(b,M):b.__proto__=M)}var h=!1;function m(b){h=b}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){m(!0)}}))}catch{}function p(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{capture:!0};return h?b:b.capture}function v(b){if("touches"in b){var M=b.touches[0],O=M.pageX,S=M.pageY;return{x:O,y:S}}var R=b.screenX,L=b.screenY;return{x:R,y:L}}var w=function(b){g(M,b);function M(){var O;u(this,M);for(var S=arguments.length,R=Array(S),L=0;L<S;L++)R[L]=arguments[L];var E=f(this,(O=M.__proto__||Object.getPrototypeOf(M)).call.apply(O,[this].concat(R)));return E._handleSwipeStart=E._handleSwipeStart.bind(E),E._handleSwipeMove=E._handleSwipeMove.bind(E),E._handleSwipeEnd=E._handleSwipeEnd.bind(E),E._onMouseDown=E._onMouseDown.bind(E),E._onMouseMove=E._onMouseMove.bind(E),E._onMouseUp=E._onMouseUp.bind(E),E._setSwiperRef=E._setSwiperRef.bind(E),E}return c(M,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(S){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(S))}},{key:"_onMouseMove",value:function(S){this.mouseDown&&this._handleSwipeMove(S)}},{key:"_onMouseUp",value:function(S){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(S)}},{key:"_handleSwipeStart",value:function(S){var R=v(S),L=R.x,E=R.y;this.moveStart={x:L,y:E},this.props.onSwipeStart(S)}},{key:"_handleSwipeMove",value:function(S){if(this.moveStart){var R=v(S),L=R.x,E=R.y,x=L-this.moveStart.x,_=E-this.moveStart.y;this.moving=!0;var D=this.props.onSwipeMove({x,y:_},S);D&&S.cancelable&&S.preventDefault(),this.movePosition={deltaX:x,deltaY:_}}}},{key:"_handleSwipeEnd",value:function(S){this.props.onSwipeEnd(S);var R=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-R?this.props.onSwipeLeft(1,S):this.movePosition.deltaX>R&&this.props.onSwipeRight(1,S),this.movePosition.deltaY<-R?this.props.onSwipeUp(1,S):this.movePosition.deltaY>R&&this.props.onSwipeDown(1,S)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(S){this.swiper=S,this.props.innerRef(S)}},{key:"render",value:function(){var S=this.props;S.tagName;var R=S.className,L=S.style,E=S.children;S.allowMouseEvents,S.onSwipeUp,S.onSwipeDown,S.onSwipeLeft,S.onSwipeRight,S.onSwipeStart,S.onSwipeMove,S.onSwipeEnd,S.innerRef,S.tolerance;var x=s(S,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]);return i.default.createElement(this.props.tagName,l({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:R,style:L},x),E)}}]),M}(n.Component);w.displayName="ReactSwipe",w.propTypes={tagName:o.default.string,className:o.default.string,style:o.default.object,children:o.default.node,allowMouseEvents:o.default.bool,onSwipeUp:o.default.func,onSwipeDown:o.default.func,onSwipeLeft:o.default.func,onSwipeRight:o.default.func,onSwipeStart:o.default.func,onSwipeMove:o.default.func,onSwipeEnd:o.default.func,innerRef:o.default.func,tolerance:o.default.number.isRequired},w.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},t.default=w})}(Vo)),Vo}var Nl;function Xc(){return Nl||(Nl=1,function(e){(function(t,n){n(e,Ng())})(zo,function(t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}t.default=r.default})}(zo)),zo}var Ni={},$l;function jc(){if($l)return Ni;$l=1,Object.defineProperty(Ni,"__esModule",{value:!0}),Ni.default=void 0;var e=t(mh());function t(i){return i&&i.__esModule?i:{default:i}}function n(i,o,a){return o in i?Object.defineProperty(i,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[o]=a,i}var r={ROOT:function(o){return(0,e.default)(n({"carousel-root":!0},o||"",!!o))},CAROUSEL:function(o){return(0,e.default)({carousel:!0,"carousel-slider":o})},WRAPPER:function(o,a){return(0,e.default)({"thumbs-wrapper":!o,"slider-wrapper":o,"axis-horizontal":a==="horizontal","axis-vertical":a!=="horizontal"})},SLIDER:function(o,a){return(0,e.default)({thumbs:!o,slider:o,animated:!a})},ITEM:function(o,a,l){return(0,e.default)({thumb:!o,slide:o,selected:a,previous:l})},ARROW_PREV:function(o){return(0,e.default)({"control-arrow control-prev":!0,"control-disabled":o})},ARROW_NEXT:function(o){return(0,e.default)({"control-arrow control-next":!0,"control-disabled":o})},DOT:function(o){return(0,e.default)({dot:!0,selected:o})}};return Ni.default=r,Ni}var $i={},Bi={},Bl;function $g(){if(Bl)return Bi;Bl=1,Object.defineProperty(Bi,"__esModule",{value:!0}),Bi.outerWidth=void 0;var e=function(n){var r=n.offsetWidth,i=getComputedStyle(n);return r+=parseInt(i.marginLeft)+parseInt(i.marginRight),r};return Bi.outerWidth=e,Bi}var Wi={},Wl;function Ns(){if(Wl)return Wi;Wl=1,Object.defineProperty(Wi,"__esModule",{value:!0}),Wi.default=void 0;var e=function(n,r,i){var o=n===0?n:n+r,a=i==="horizontal"?[o,0,0]:[0,o,0],l="translate3d",s="("+a.join(",")+")";return l+s};return Wi.default=e,Wi}var Ui={},Ul;function Gc(){if(Ul)return Ui;Ul=1,Object.defineProperty(Ui,"__esModule",{value:!0}),Ui.default=void 0;var e=function(){return window};return Ui.default=e,Ui}var ql;function Kc(){if(ql)return $i;ql=1,Object.defineProperty($i,"__esModule",{value:!0}),$i.default=void 0;var e=s(go()),t=a(jc()),n=$g(),r=a(Ns()),i=a(Xc()),o=a(Gc());function a(E){return E&&E.__esModule?E:{default:E}}function l(){if(typeof WeakMap!="function")return null;var E=new WeakMap;return l=function(){return E},E}function s(E){if(E&&E.__esModule)return E;if(E===null||u(E)!=="object"&&typeof E!="function")return{default:E};var x=l();if(x&&x.has(E))return x.get(E);var _={},D=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var C in E)if(Object.prototype.hasOwnProperty.call(E,C)){var I=D?Object.getOwnPropertyDescriptor(E,C):null;I&&(I.get||I.set)?Object.defineProperty(_,C,I):_[C]=E[C]}return _.default=E,x&&x.set(E,_),_}function u(E){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?u=function(_){return typeof _}:u=function(_){return _&&typeof Symbol=="function"&&_.constructor===Symbol&&_!==Symbol.prototype?"symbol":typeof _},u(E)}function c(){return c=Object.assign||function(E){for(var x=1;x<arguments.length;x++){var _=arguments[x];for(var D in _)Object.prototype.hasOwnProperty.call(_,D)&&(E[D]=_[D])}return E},c.apply(this,arguments)}function f(E,x){if(!(E instanceof x))throw new TypeError("Cannot call a class as a function")}function g(E,x){for(var _=0;_<x.length;_++){var D=x[_];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(E,D.key,D)}}function h(E,x,_){return x&&g(E.prototype,x),E}function m(E,x){if(typeof x!="function"&&x!==null)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(x&&x.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),x&&p(E,x)}function p(E,x){return p=Object.setPrototypeOf||function(D,C){return D.__proto__=C,D},p(E,x)}function v(E){var x=M();return function(){var D=O(E),C;if(x){var I=O(this).constructor;C=Reflect.construct(D,arguments,I)}else C=D.apply(this,arguments);return w(this,C)}}function w(E,x){return x&&(u(x)==="object"||typeof x=="function")?x:b(E)}function b(E){if(E===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}function M(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function O(E){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(_){return _.__proto__||Object.getPrototypeOf(_)},O(E)}function S(E,x,_){return x in E?Object.defineProperty(E,x,{value:_,enumerable:!0,configurable:!0,writable:!0}):E[x]=_,E}var R=function(x){return x.hasOwnProperty("key")},L=function(E){m(_,E);var x=v(_);function _(D){var C;return f(this,_),C=x.call(this,D),S(b(C),"itemsWrapperRef",void 0),S(b(C),"itemsListRef",void 0),S(b(C),"thumbsRef",void 0),S(b(C),"setItemsWrapperRef",function(I){C.itemsWrapperRef=I}),S(b(C),"setItemsListRef",function(I){C.itemsListRef=I}),S(b(C),"setThumbsRef",function(I,T){C.thumbsRef||(C.thumbsRef=[]),C.thumbsRef[T]=I}),S(b(C),"updateSizes",function(){if(!(!C.props.children||!C.itemsWrapperRef||!C.thumbsRef)){var I=e.Children.count(C.props.children),T=C.itemsWrapperRef.clientWidth,k=C.props.thumbWidth?C.props.thumbWidth:(0,n.outerWidth)(C.thumbsRef[0]),z=Math.floor(T/k),$=z<I,X=$?I-z:0;C.setState(function(re,j){return{itemSize:k,visibleItems:z,firstItem:$?C.getFirstItem(j.selectedItem):0,lastPosition:X,showArrows:$}})}}),S(b(C),"handleClickItem",function(I,T,k){if(!R(k)||k.key==="Enter"){var z=C.props.onSelectItem;typeof z=="function"&&z(I,T)}}),S(b(C),"onSwipeStart",function(){C.setState({swiping:!0})}),S(b(C),"onSwipeEnd",function(){C.setState({swiping:!1})}),S(b(C),"onSwipeMove",function(I){var T=I.x;if(!C.state.itemSize||!C.itemsWrapperRef||!C.state.visibleItems)return!1;var k=0,z=e.Children.count(C.props.children),$=-(C.state.firstItem*100)/C.state.visibleItems,X=Math.max(z-C.state.visibleItems,0),re=-X*100/C.state.visibleItems;$===k&&T>0&&(T=0),$===re&&T<0&&(T=0);var j=C.itemsWrapperRef.clientWidth,G=$+100/(j/T);return C.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(ae){C.itemsListRef.style[ae]=(0,r.default)(G,"%",C.props.axis)}),!0}),S(b(C),"slideRight",function(I){C.moveTo(C.state.firstItem-(typeof I=="number"?I:1))}),S(b(C),"slideLeft",function(I){C.moveTo(C.state.firstItem+(typeof I=="number"?I:1))}),S(b(C),"moveTo",function(I){I=I<0?0:I,I=I>=C.state.lastPosition?C.state.lastPosition:I,C.setState({firstItem:I})}),C.state={selectedItem:D.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},C}return h(_,[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(C){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==C.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(C){var I=C;return C>=this.state.lastPosition&&(I=this.state.lastPosition),C<this.state.firstItem+this.state.visibleItems&&(I=this.state.firstItem),C<this.state.firstItem&&(I=C),I}},{key:"renderItems",value:function(){var C=this;return this.props.children.map(function(I,T){var k=t.default.ITEM(!1,T===C.state.selectedItem),z={key:T,ref:function(X){return C.setThumbsRef(X,T)},className:k,onClick:C.handleClickItem.bind(C,T,C.props.children[T]),onKeyDown:C.handleClickItem.bind(C,T,C.props.children[T]),"aria-label":"".concat(C.props.labels.item," ").concat(T+1),style:{width:C.props.thumbWidth}};return e.default.createElement("li",c({},z,{role:"button",tabIndex:0}),I)})}},{key:"render",value:function(){var C=this;if(!this.props.children)return null;var I=e.Children.count(this.props.children)>1,T=this.state.showArrows&&this.state.firstItem>0,k=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,z={},$=-this.state.firstItem*(this.state.itemSize||0),X=(0,r.default)($,"px",this.props.axis),re=this.props.transitionTime+"ms";return z={WebkitTransform:X,MozTransform:X,MsTransform:X,OTransform:X,transform:X,msTransform:X,WebkitTransitionDuration:re,MozTransitionDuration:re,MsTransitionDuration:re,OTransitionDuration:re,transitionDuration:re,msTransitionDuration:re},e.default.createElement("div",{className:t.default.CAROUSEL(!1)},e.default.createElement("div",{className:t.default.WRAPPER(!1),ref:this.setItemsWrapperRef},e.default.createElement("button",{type:"button",className:t.default.ARROW_PREV(!T),onClick:function(){return C.slideRight()},"aria-label":this.props.labels.leftArrow}),I?e.default.createElement(i.default,{tagName:"ul",className:t.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:z,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):e.default.createElement("ul",{className:t.default.SLIDER(!1,this.state.swiping),ref:function(G){return C.setItemsListRef(G)},style:z},this.renderItems()),e.default.createElement("button",{type:"button",className:t.default.ARROW_NEXT(!k),onClick:function(){return C.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}]),_}(e.Component);return $i.default=L,S(L,"displayName","Thumbs"),S(L,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350}),$i}var qi={},Yl;function Bg(){if(Yl)return qi;Yl=1,Object.defineProperty(qi,"__esModule",{value:!0}),qi.default=void 0;var e=function(){return document};return qi.default=e,qi}var Ln={},Xl;function Zc(){if(Xl)return Ln;Xl=1,Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.setPosition=Ln.getPosition=Ln.isKeyboardEvent=Ln.defaultStatusFormatter=Ln.noop=void 0;var e=go(),t=n(Ns());function n(s){return s&&s.__esModule?s:{default:s}}var r=function(){};Ln.noop=r;var i=function(u,c){return"".concat(u," of ").concat(c)};Ln.defaultStatusFormatter=i;var o=function(u){return u?u.hasOwnProperty("key"):!1};Ln.isKeyboardEvent=o;var a=function(u,c){if(c.infiniteLoop&&++u,u===0)return 0;var f=e.Children.count(c.children);if(c.centerMode&&c.axis==="horizontal"){var g=-u*c.centerSlidePercentage,h=f-1;return u&&(u!==h||c.infiniteLoop)?g+=(100-c.centerSlidePercentage)/2:u===h&&(g+=100-c.centerSlidePercentage),g}return-u*100};Ln.getPosition=a;var l=function(u,c){var f={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(g){f[g]=(0,t.default)(u,"%",c)}),f};return Ln.setPosition=l,Ln}var er={},jl;function Wg(){if(jl)return er;jl=1,Object.defineProperty(er,"__esModule",{value:!0}),er.fadeAnimationHandler=er.slideStopSwipingHandler=er.slideSwipeAnimationHandler=er.slideAnimationHandler=void 0;var e=go(),t=r(Ns()),n=Zc();function r(f){return f&&f.__esModule?f:{default:f}}function i(f,g){var h=Object.keys(f);if(Object.getOwnPropertySymbols){var m=Object.getOwnPropertySymbols(f);g&&(m=m.filter(function(p){return Object.getOwnPropertyDescriptor(f,p).enumerable})),h.push.apply(h,m)}return h}function o(f){for(var g=1;g<arguments.length;g++){var h=arguments[g]!=null?arguments[g]:{};g%2?i(Object(h),!0).forEach(function(m){a(f,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(f,Object.getOwnPropertyDescriptors(h)):i(Object(h)).forEach(function(m){Object.defineProperty(f,m,Object.getOwnPropertyDescriptor(h,m))})}return f}function a(f,g,h){return g in f?Object.defineProperty(f,g,{value:h,enumerable:!0,configurable:!0,writable:!0}):f[g]=h,f}var l=function(g,h){var m={},p=h.selectedItem,v=p,w=e.Children.count(g.children)-1,b=g.infiniteLoop&&(p<0||p>w);if(b)return v<0?g.centerMode&&g.centerSlidePercentage&&g.axis==="horizontal"?m.itemListStyle=(0,n.setPosition)(-(w+2)*g.centerSlidePercentage-(100-g.centerSlidePercentage)/2,g.axis):m.itemListStyle=(0,n.setPosition)(-(w+2)*100,g.axis):v>w&&(m.itemListStyle=(0,n.setPosition)(0,g.axis)),m;var M=(0,n.getPosition)(p,g),O=(0,t.default)(M,"%",g.axis),S=g.transitionTime+"ms";return m.itemListStyle={WebkitTransform:O,msTransform:O,OTransform:O,transform:O},h.swiping||(m.itemListStyle=o(o({},m.itemListStyle),{},{WebkitTransitionDuration:S,MozTransitionDuration:S,OTransitionDuration:S,transitionDuration:S,msTransitionDuration:S})),m};er.slideAnimationHandler=l;var s=function(g,h,m,p){var v={},w=h.axis==="horizontal",b=e.Children.count(h.children),M=0,O=(0,n.getPosition)(m.selectedItem,h),S=h.infiniteLoop?(0,n.getPosition)(b-1,h)-100:(0,n.getPosition)(b-1,h),R=w?g.x:g.y,L=R;O===M&&R>0&&(L=0),O===S&&R<0&&(L=0);var E=O+100/(m.itemSize/L),x=Math.abs(R)>h.swipeScrollTolerance;return h.infiniteLoop&&x&&(m.selectedItem===0&&E>-100?E-=b*100:m.selectedItem===b-1&&E<-b*100&&(E+=b*100)),(!h.preventMovementUntilSwipeScrollTolerance||x||m.swipeMovementStarted)&&(m.swipeMovementStarted||p({swipeMovementStarted:!0}),v.itemListStyle=(0,n.setPosition)(E,h.axis)),x&&!m.cancelClick&&p({cancelClick:!0}),v};er.slideSwipeAnimationHandler=s;var u=function(g,h){var m=(0,n.getPosition)(h.selectedItem,g),p=(0,n.setPosition)(m,g.axis);return{itemListStyle:p}};er.slideStopSwipingHandler=u;var c=function(g,h){var m=g.transitionTime+"ms",p="ease-in-out",v={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:p,msTransitionTimingFunction:p,MozTransitionTimingFunction:p,WebkitTransitionTimingFunction:p,OTransitionTimingFunction:p};return h.swiping||(v=o(o({},v),{},{WebkitTransitionDuration:m,MozTransitionDuration:m,OTransitionDuration:m,transitionDuration:m,msTransitionDuration:m})),{slideStyle:v,selectedStyle:o(o({},v),{},{opacity:1,position:"relative"}),prevStyle:o({},v)}};return er.fadeAnimationHandler=c,er}var Gl;function Ug(){if(Gl)return Vi;Gl=1,Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.default=void 0;var e=c(go()),t=s(Xc()),n=s(jc()),r=s(Kc()),i=s(Bg()),o=s(Gc()),a=Zc(),l=Wg();function s(D){return D&&D.__esModule?D:{default:D}}function u(){if(typeof WeakMap!="function")return null;var D=new WeakMap;return u=function(){return D},D}function c(D){if(D&&D.__esModule)return D;if(D===null||f(D)!=="object"&&typeof D!="function")return{default:D};var C=u();if(C&&C.has(D))return C.get(D);var I={},T=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var k in D)if(Object.prototype.hasOwnProperty.call(D,k)){var z=T?Object.getOwnPropertyDescriptor(D,k):null;z&&(z.get||z.set)?Object.defineProperty(I,k,z):I[k]=D[k]}return I.default=D,C&&C.set(D,I),I}function f(D){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?f=function(I){return typeof I}:f=function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},f(D)}function g(){return g=Object.assign||function(D){for(var C=1;C<arguments.length;C++){var I=arguments[C];for(var T in I)Object.prototype.hasOwnProperty.call(I,T)&&(D[T]=I[T])}return D},g.apply(this,arguments)}function h(D,C){var I=Object.keys(D);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(D);C&&(T=T.filter(function(k){return Object.getOwnPropertyDescriptor(D,k).enumerable})),I.push.apply(I,T)}return I}function m(D){for(var C=1;C<arguments.length;C++){var I=arguments[C]!=null?arguments[C]:{};C%2?h(Object(I),!0).forEach(function(T){x(D,T,I[T])}):Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(I)):h(Object(I)).forEach(function(T){Object.defineProperty(D,T,Object.getOwnPropertyDescriptor(I,T))})}return D}function p(D,C){if(!(D instanceof C))throw new TypeError("Cannot call a class as a function")}function v(D,C){for(var I=0;I<C.length;I++){var T=C[I];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(D,T.key,T)}}function w(D,C,I){return C&&v(D.prototype,C),D}function b(D,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function");D.prototype=Object.create(C&&C.prototype,{constructor:{value:D,writable:!0,configurable:!0}}),C&&M(D,C)}function M(D,C){return M=Object.setPrototypeOf||function(T,k){return T.__proto__=k,T},M(D,C)}function O(D){var C=L();return function(){var T=E(D),k;if(C){var z=E(this).constructor;k=Reflect.construct(T,arguments,z)}else k=T.apply(this,arguments);return S(this,k)}}function S(D,C){return C&&(f(C)==="object"||typeof C=="function")?C:R(D)}function R(D){if(D===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return D}function L(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function E(D){return E=Object.setPrototypeOf?Object.getPrototypeOf:function(I){return I.__proto__||Object.getPrototypeOf(I)},E(D)}function x(D,C,I){return C in D?Object.defineProperty(D,C,{value:I,enumerable:!0,configurable:!0,writable:!0}):D[C]=I,D}var _=function(D){b(I,D);var C=O(I);function I(T){var k;p(this,I),k=C.call(this,T),x(R(k),"thumbsRef",void 0),x(R(k),"carouselWrapperRef",void 0),x(R(k),"listRef",void 0),x(R(k),"itemsRef",void 0),x(R(k),"timer",void 0),x(R(k),"animationHandler",void 0),x(R(k),"setThumbsRef",function($){k.thumbsRef=$}),x(R(k),"setCarouselWrapperRef",function($){k.carouselWrapperRef=$}),x(R(k),"setListRef",function($){k.listRef=$}),x(R(k),"setItemsRef",function($,X){k.itemsRef||(k.itemsRef=[]),k.itemsRef[X]=$}),x(R(k),"autoPlay",function(){e.Children.count(k.props.children)<=1||(k.clearAutoPlay(),k.props.autoPlay&&(k.timer=setTimeout(function(){k.increment()},k.props.interval)))}),x(R(k),"clearAutoPlay",function(){k.timer&&clearTimeout(k.timer)}),x(R(k),"resetAutoPlay",function(){k.clearAutoPlay(),k.autoPlay()}),x(R(k),"stopOnHover",function(){k.setState({isMouseEntered:!0},k.clearAutoPlay)}),x(R(k),"startOnLeave",function(){k.setState({isMouseEntered:!1},k.autoPlay)}),x(R(k),"isFocusWithinTheCarousel",function(){return k.carouselWrapperRef?!!((0,i.default)().activeElement===k.carouselWrapperRef||k.carouselWrapperRef.contains((0,i.default)().activeElement)):!1}),x(R(k),"navigateWithKeyboard",function($){if(k.isFocusWithinTheCarousel()){var X=k.props.axis,re=X==="horizontal",j={ArrowUp:38,ArrowRight:39,ArrowDown:40,ArrowLeft:37},G=re?j.ArrowRight:j.ArrowDown,ae=re?j.ArrowLeft:j.ArrowUp;G===$.keyCode?k.increment():ae===$.keyCode&&k.decrement()}}),x(R(k),"updateSizes",function(){if(!(!k.state.initialized||!k.itemsRef||k.itemsRef.length===0)){var $=k.props.axis==="horizontal",X=k.itemsRef[0];if(X){var re=$?X.clientWidth:X.clientHeight;k.setState({itemSize:re}),k.thumbsRef&&k.thumbsRef.updateSizes()}}}),x(R(k),"setMountState",function(){k.setState({hasMount:!0}),k.updateSizes()}),x(R(k),"handleClickItem",function($,X){if(e.Children.count(k.props.children)!==0){if(k.state.cancelClick){k.setState({cancelClick:!1});return}k.props.onClickItem($,X),$!==k.state.selectedItem&&k.setState({selectedItem:$})}}),x(R(k),"handleOnChange",function($,X){e.Children.count(k.props.children)<=1||k.props.onChange($,X)}),x(R(k),"handleClickThumb",function($,X){k.props.onClickThumb($,X),k.moveTo($)}),x(R(k),"onSwipeStart",function($){k.setState({swiping:!0}),k.props.onSwipeStart($)}),x(R(k),"onSwipeEnd",function($){k.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),k.props.onSwipeEnd($),k.clearAutoPlay(),k.state.autoPlay&&k.autoPlay()}),x(R(k),"onSwipeMove",function($,X){k.props.onSwipeMove(X);var re=k.props.swipeAnimationHandler($,k.props,k.state,k.setState.bind(R(k)));return k.setState(m({},re)),!!Object.keys(re).length}),x(R(k),"decrement",function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;k.moveTo(k.state.selectedItem-(typeof $=="number"?$:1))}),x(R(k),"increment",function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;k.moveTo(k.state.selectedItem+(typeof $=="number"?$:1))}),x(R(k),"moveTo",function($){if(typeof $=="number"){var X=e.Children.count(k.props.children)-1;$<0&&($=k.props.infiniteLoop?X:0),$>X&&($=k.props.infiniteLoop?0:X),k.selectItem({selectedItem:$}),k.state.autoPlay&&k.state.isMouseEntered===!1&&k.resetAutoPlay()}}),x(R(k),"onClickNext",function(){k.increment(1)}),x(R(k),"onClickPrev",function(){k.decrement(1)}),x(R(k),"onSwipeForward",function(){k.increment(1),k.props.emulateTouch&&k.setState({cancelClick:!0})}),x(R(k),"onSwipeBackwards",function(){k.decrement(1),k.props.emulateTouch&&k.setState({cancelClick:!0})}),x(R(k),"changeItem",function($){return function(X){(!(0,a.isKeyboardEvent)(X)||X.key==="Enter")&&k.moveTo($)}}),x(R(k),"selectItem",function($){k.setState(m({previousItem:k.state.selectedItem},$),function(){k.setState(k.animationHandler(k.props,k.state))}),k.handleOnChange($.selectedItem,e.Children.toArray(k.props.children)[$.selectedItem])}),x(R(k),"getInitialImage",function(){var $=k.props.selectedItem,X=k.itemsRef&&k.itemsRef[$],re=X&&X.getElementsByTagName("img")||[];return re[0]}),x(R(k),"getVariableItemHeight",function($){var X=k.itemsRef&&k.itemsRef[$];if(k.state.hasMount&&X&&X.children.length){var re=X.children[0].getElementsByTagName("img")||[];if(re.length>0){var j=re[0];if(!j.complete){var G=function le(){k.forceUpdate(),j.removeEventListener("load",le)};j.addEventListener("load",G)}}var ae=re[0]||X.children[0],ie=ae.clientHeight;return ie>0?ie:null}return null});var z={initialized:!1,previousItem:T.selectedItem,selectedItem:T.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:T.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return k.animationHandler=typeof T.animationHandler=="function"&&T.animationHandler||T.animationHandler==="fade"&&l.fadeAnimationHandler||l.slideAnimationHandler,k.state=m(m({},z),k.animationHandler(T,z)),k}return w(I,[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(k,z){!k.children&&this.props.children&&!this.state.initialized&&this.setupCarousel(),!k.autoFocus&&this.props.autoFocus&&this.forceFocus(),z.swiping&&!this.state.swiping&&this.setState(m({},this.props.stopSwipingHandler(this.props,this.state))),(k.selectedItem!==this.props.selectedItem||k.centerMode!==this.props.centerMode)&&(this.updateSizes(),this.moveTo(this.props.selectedItem)),k.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var k=this;this.bindEvents(),this.state.autoPlay&&e.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},function(){var z=k.getInitialImage();z&&!z.complete?z.addEventListener("load",k.setMountState):k.setMountState()})}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var k=this.carouselWrapperRef;this.props.stopOnHover&&k&&(k.addEventListener("mouseenter",this.stopOnHover),k.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var k=this.carouselWrapperRef;this.props.stopOnHover&&k&&(k.removeEventListener("mouseenter",this.stopOnHover),k.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,i.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var k=this.getInitialImage();k&&k.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,i.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var k;(k=this.carouselWrapperRef)===null||k===void 0||k.focus()}},{key:"renderItems",value:function(k){var z=this;return this.props.children?e.Children.map(this.props.children,function($,X){var re=X===z.state.selectedItem,j=X===z.state.previousItem,G=re&&z.state.selectedStyle||j&&z.state.prevStyle||z.state.slideStyle||{};z.props.centerMode&&z.props.axis==="horizontal"&&(G=m(m({},G),{},{minWidth:z.props.centerSlidePercentage+"%"})),z.state.swiping&&z.state.swipeMovementStarted&&(G=m(m({},G),{},{pointerEvents:"none"}));var ae={ref:function(le){return z.setItemsRef(le,X)},key:"itemKey"+X+(k?"clone":""),className:n.default.ITEM(!0,X===z.state.selectedItem,X===z.state.previousItem),onClick:z.handleClickItem.bind(z,X,$),style:G};return e.default.createElement("li",ae,z.props.renderItem($,{isSelected:X===z.state.selectedItem,isPrevious:X===z.state.previousItem}))}):[]}},{key:"renderControls",value:function(){var k=this,z=this.props,$=z.showIndicators,X=z.labels,re=z.renderIndicator,j=z.children;return $?e.default.createElement("ul",{className:"control-dots"},e.Children.map(j,function(G,ae){return re&&re(k.changeItem(ae),ae===k.state.selectedItem,ae,X.item)})):null}},{key:"renderStatus",value:function(){return this.props.showStatus?e.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,e.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return!this.props.showThumbs||!this.props.children||e.Children.count(this.props.children)===0?null:e.default.createElement(r.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children))}},{key:"render",value:function(){var k=this;if(!this.props.children||e.Children.count(this.props.children)===0)return null;var z=this.props.swipeable&&e.Children.count(this.props.children)>1,$=this.props.axis==="horizontal",X=this.props.showArrows&&e.Children.count(this.props.children)>1,re=X&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,j=X&&(this.state.selectedItem<e.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,G=this.renderItems(!0),ae=G.shift(),ie=G.pop(),le={className:n.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},fe={};if($){if(le.onSwipeLeft=this.onSwipeForward,le.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var Q=this.getVariableItemHeight(this.state.selectedItem);fe.height=Q||"auto"}}else le.onSwipeUp=this.props.verticalSwipe==="natural"?this.onSwipeBackwards:this.onSwipeForward,le.onSwipeDown=this.props.verticalSwipe==="natural"?this.onSwipeForward:this.onSwipeBackwards,le.style=m(m({},le.style),{},{height:this.state.itemSize}),fe.height=this.state.itemSize;return e.default.createElement("div",{"aria-label":this.props.ariaLabel,className:n.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},e.default.createElement("div",{className:n.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,re,this.props.labels.leftArrow),e.default.createElement("div",{className:n.default.WRAPPER(!0,this.props.axis),style:fe},z?e.default.createElement(t.default,g({tagName:"ul",innerRef:this.setListRef},le,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&ie,this.renderItems(),this.props.infiniteLoop&&ae):e.default.createElement("ul",{className:n.default.SLIDER(!0,this.state.swiping),ref:function(P){return k.setListRef(P)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&ie,this.renderItems(),this.props.infiniteLoop&&ae)),this.props.renderArrowNext(this.onClickNext,j,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}]),I}(e.default.Component);return Vi.default=_,x(_,"displayName","Carousel"),x(_,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:a.noop,onClickThumb:a.noop,onChange:a.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(C,I,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_PREV(!I),onClick:C})},renderArrowNext:function(C,I,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_NEXT(!I),onClick:C})},renderIndicator:function(C,I,T,k){return e.default.createElement("li",{className:n.default.DOT(I),onClick:C,onKeyDown:C,value:T,key:T,role:"button",tabIndex:0,"aria-label":"".concat(k," ").concat(T+1)})},renderItem:function(C){return C},renderThumbs:function(C){var I=e.Children.map(C,function(T){var k=T;if(T.type!=="img"&&(k=e.Children.toArray(T.props.children).find(function(z){return z.type==="img"})),!!k)return k});return I.filter(function(T){return T}).length===0?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):I},statusFormatter:a.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:l.slideSwipeAnimationHandler,stopSwipingHandler:l.slideStopSwipingHandler}),Vi}var Kl={},Zl;function qg(){return Zl||(Zl=1),Kl}var Jl;function Yg(){return Jl||(Jl=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Carousel",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"CarouselProps",{enumerable:!0,get:function(){return n.CarouselProps}}),Object.defineProperty(e,"Thumbs",{enumerable:!0,get:function(){return r.default}});var t=i(Ug()),n=qg(),r=i(Kc());function i(o){return o&&o.__esModule?o:{default:o}}}(Za)),Za}var Xg=Yg(),Ja,Ql;function jg(){if(Ql)return Ja;Ql=1;var e=ph(),t=function(){return e.Date.now()};return Ja=t,Ja}var Qa,eu;function Jc(){if(eu)return Qa;eu=1;var e=Tc(),t=jg(),n=Dc(),r="Expected a function",i=Math.max,o=Math.min;function a(l,s,u){var c,f,g,h,m,p,v=0,w=!1,b=!1,M=!0;if(typeof l!="function")throw new TypeError(r);s=n(s)||0,e(u)&&(w=!!u.leading,b="maxWait"in u,g=b?i(n(u.maxWait)||0,s):g,M="trailing"in u?!!u.trailing:M);function O(I){var T=c,k=f;return c=f=void 0,v=I,h=l.apply(k,T),h}function S(I){return v=I,m=setTimeout(E,s),w?O(I):h}function R(I){var T=I-p,k=I-v,z=s-T;return b?o(z,g-k):z}function L(I){var T=I-p,k=I-v;return p===void 0||T>=s||T<0||b&&k>=g}function E(){var I=t();if(L(I))return x(I);m=setTimeout(E,R(I))}function x(I){return m=void 0,M&&c?O(I):(c=f=void 0,h)}function _(){m!==void 0&&clearTimeout(m),v=0,c=p=f=m=void 0}function D(){return m===void 0?h:x(t())}function C(){var I=t(),T=L(I);if(c=arguments,f=this,p=I,T){if(m===void 0)return S(p);if(b)return clearTimeout(m),m=setTimeout(E,s),O(p)}return m===void 0&&(m=setTimeout(E,s)),h}return C.cancel=_,C.flush=D,C}return Qa=a,Qa}var Gg=Jc();const Qc=wr(Gg);function gn(e,t,n,r,i=!1){const o=d.useRef();o.current=t,d.useEffect(()=>{if(n===null||n.addEventListener===void 0)return;const a=n,l=s=>{var u;(u=o.current)==null||u.call(a,s)};return a.addEventListener(e,l,{passive:r,capture:i}),()=>{a.removeEventListener(e,l,{capture:i})}},[e,n,r,i])}function Yr(e,t){return e===void 0?void 0:t}const Kg=Math.PI;function tu(e){return e*Kg/180}const ed=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),td=(e,t,n,r,i)=>{switch(e){case"left":return Math.floor(t)+r+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-i/2}},nd=(e,t,n)=>Math.min(e,t-n*2),rd=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,$s=e=>{const t=e.fgColor??"currentColor";return d.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},d.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),d.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},Zg=e=>{const t=e.fgColor??"currentColor";return d.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},d.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};function Jg(e,t,n){const[r,i]=d.useState(e),o=d.useRef(!0);d.useEffect(()=>()=>{o.current=!1},[]);const a=d.useRef(Qc(l=>{o.current&&i(l)},n));return d.useLayoutEffect(()=>{o.current&&a.current(()=>e())},t),r}const Qg="֑-߿יִ-﷽ﹰ-ﻼ",em="A-Za-zÀ-ÖØ-öø-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",tm=new RegExp("^[^"+em+"]*["+Qg+"]");function Bs(e){return tm.test(e)?"rtl":"not-rtl"}let No;function xs(){if(typeof document>"u")return 0;if(No!==void 0)return No;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),No=n-r,No}const Wr=Symbol();function nm(e){const t=d.useRef([Wr,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,r]=d.useState(e),[,i]=d.useState(),o=d.useCallback(l=>{const s=t.current[0];s!==Wr&&(l=typeof l=="function"?l(s):l,l===s)||(s!==Wr&&i({}),r(u=>typeof l=="function"?l(s===Wr?u:s):l),t.current[0]=Wr)},[]),a=d.useCallback(()=>{t.current[0]=Wr,i({})},[]);return[t.current[0]===Wr?n:t.current[0],o,a]}function id(e){if(e.length===0)return"";let t=0,n=0;for(const r of e){if(n+=r.length,n>1e4)break;t++}return e.slice(0,t).join(", ")}function rm(e){const t=d.useRef(e);return Ci(e,t.current)||(t.current=e),t.current}const im=e=>{const{urls:t,canWrite:n,onEditClick:r,renderImage:i}=e,o=t.filter(l=>l!=="");if(o.length===0)return null;const a=o.length>1;return d.createElement(Vg,{"data-testid":"GDG-default-image-overlay-editor"},d.createElement(Xg.Carousel,{showArrows:a,showThumbs:!1,swipeable:a,emulateTouch:a,infiniteLoop:a},o.map(l=>{const s=(i==null?void 0:i(l))??d.createElement("img",{draggable:!1,src:l});return d.createElement("div",{className:"gdg-centering-container",key:l},s)})),n&&r&&d.createElement("button",{className:"gdg-edit-icon",onClick:r},d.createElement($s,null)))};function od(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Kr=od();function om(e){Kr=e}const ad=/[&<>"']/,am=new RegExp(ad.source,"g"),sd=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,sm=new RegExp(sd.source,"g"),lm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},nu=e=>lm[e];function wn(e,t){if(t){if(ad.test(e))return e.replace(am,nu)}else if(sd.test(e))return e.replace(sm,nu);return e}const um=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function ld(e){return e.replace(um,(t,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const cm=/(^|[^\[])\^/g;function $t(e,t){e=typeof e=="string"?e:e.source,t=t||"";const n={replace:(r,i)=>(i=i.source||i,i=i.replace(cm,"$1"),e=e.replace(r,i),n),getRegex:()=>new RegExp(e,t)};return n}const dm=/[^\w:]/g,fm=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function ru(e,t,n){if(e){let r;try{r=decodeURIComponent(ld(n)).replace(dm,"").toLowerCase()}catch{return null}if(r.indexOf("javascript:")===0||r.indexOf("vbscript:")===0||r.indexOf("data:")===0)return null}t&&!fm.test(n)&&(n=pm(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const $o={},hm=/^[^:]+:\/*[^/]*$/,gm=/^([^:]+:)[\s\S]*$/,mm=/^([^:]+:\/*[^/]*)[\s\S]*$/;function pm(e,t){$o[" "+e]||(hm.test(e)?$o[" "+e]=e+"/":$o[" "+e]=Qo(e,"/",!0)),e=$o[" "+e];const n=e.indexOf(":")===-1;return t.substring(0,2)==="//"?n?t:e.replace(gm,"$1")+t:t.charAt(0)==="/"?n?t:e.replace(mm,"$1")+t:e+t}const aa={exec:function(){}};function iu(e,t){const n=e.replace(/\|/g,(o,a,l)=>{let s=!1,u=a;for(;--u>=0&&l[u]==="\\";)s=!s;return s?"|":" |"}),r=n.split(/ \|/);let i=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(/\\\|/g,"|");return r}function Qo(e,t,n){const r=e.length;if(r===0)return"";let i=0;for(;i<r;){const o=e.charAt(r-i-1);if(o===t&&!n)i++;else if(o!==t&&n)i++;else break}return e.slice(0,r-i)}function vm(e,t){if(e.indexOf(t[1])===-1)return-1;const n=e.length;let r=0,i=0;for(;i<n;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function bm(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function ou(e,t){if(t<1)return"";let n="";for(;t>1;)t&1&&(n+=e),t>>=1,e+=e;return n+e}function au(e,t,n,r){const i=t.href,o=t.title?wn(t.title):null,a=e[1].replace(/\\([\[\]])/g,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const l={type:"link",raw:n,href:i,title:o,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}return{type:"image",raw:n,href:i,title:o,text:wn(a)}}function wm(e,t){const n=e.match(/^(\s+)(?:```)/);if(n===null)return t;const r=n[1];return t.split(`
`).map(i=>{const o=i.match(/^\s+/);if(o===null)return i;const[a]=o;return a.length>=r.length?i.slice(r.length):i}).join(`
`)}class Ws{constructor(t){this.options=t||Kr}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:Qo(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],i=wm(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline._escapes,"$1"):n[2],text:i}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const i=Qo(r,"#");(this.options.pedantic||!i||/ $/.test(i))&&(r=i.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=n[0].replace(/^ *>[ \t]?/gm,""),i=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(r);return this.lexer.state.top=i,{type:"blockquote",raw:n[0],tokens:o,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r,i,o,a,l,s,u,c,f,g,h,m,p=n[1].trim();const v=p.length>1,w={type:"list",raw:"",ordered:v,start:v?+p.slice(0,-1):"",loose:!1,items:[]};p=v?`\\d{1,9}\\${p.slice(-1)}`:`\\${p}`,this.options.pedantic&&(p=v?p:"[*+-]");const b=new RegExp(`^( {0,3}${p})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;t&&(m=!1,!(!(n=b.exec(t))||this.rules.block.hr.test(t)));){if(r=n[0],t=t.substring(r.length),c=n[2].split(`
`,1)[0].replace(/^\t+/,O=>" ".repeat(3*O.length)),f=t.split(`
`,1)[0],this.options.pedantic?(a=2,h=c.trimLeft()):(a=n[2].search(/[^ ]/),a=a>4?1:a,h=c.slice(a),a+=n[1].length),s=!1,!c&&/^ *$/.test(f)&&(r+=f+`
`,t=t.substring(f.length+1),m=!0),!m){const O=new RegExp(`^ {0,${Math.min(3,a-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),S=new RegExp(`^ {0,${Math.min(3,a-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),R=new RegExp(`^ {0,${Math.min(3,a-1)}}(?:\`\`\`|~~~)`),L=new RegExp(`^ {0,${Math.min(3,a-1)}}#`);for(;t&&(g=t.split(`
`,1)[0],f=g,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(R.test(f)||L.test(f)||O.test(f)||S.test(t)));){if(f.search(/[^ ]/)>=a||!f.trim())h+=`
`+f.slice(a);else{if(s||c.search(/[^ ]/)>=4||R.test(c)||L.test(c)||S.test(c))break;h+=`
`+f}!s&&!f.trim()&&(s=!0),r+=g+`
`,t=t.substring(g.length+1),c=f.slice(a)}}w.loose||(u?w.loose=!0:/\n *\n *$/.test(r)&&(u=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(h),i&&(o=i[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),w.items.push({type:"list_item",raw:r,task:!!i,checked:o,loose:!1,text:h}),w.raw+=r}w.items[w.items.length-1].raw=r.trimRight(),w.items[w.items.length-1].text=h.trimRight(),w.raw=w.raw.trimRight();const M=w.items.length;for(l=0;l<M;l++)if(this.lexer.state.top=!1,w.items[l].tokens=this.lexer.blockTokens(w.items[l].text,[]),!w.loose){const O=w.items[l].tokens.filter(R=>R.type==="space"),S=O.length>0&&O.some(R=>/\n.*\n/.test(R.raw));w.loose=S}if(w.loose)for(l=0;l<M;l++)w.items[l].loose=!0;return w}}html(t){const n=this.rules.block.html.exec(t);if(n){const r={type:"html",raw:n[0],pre:!this.options.sanitizer&&(n[1]==="pre"||n[1]==="script"||n[1]==="style"),text:n[0]};if(this.options.sanitize){const i=this.options.sanitizer?this.options.sanitizer(n[0]):wn(n[0]);r.type="paragraph",r.text=i,r.tokens=this.lexer.inline(i)}return r}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),i=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",o=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline._escapes,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:i,title:o}}}table(t){const n=this.rules.block.table.exec(t);if(n){const r={type:"table",header:iu(n[1]).map(i=>({text:i})),align:n[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(r.header.length===r.align.length){r.raw=n[0];let i=r.align.length,o,a,l,s;for(o=0;o<i;o++)/^ *-+: *$/.test(r.align[o])?r.align[o]="right":/^ *:-+: *$/.test(r.align[o])?r.align[o]="center":/^ *:-+ *$/.test(r.align[o])?r.align[o]="left":r.align[o]=null;for(i=r.rows.length,o=0;o<i;o++)r.rows[o]=iu(r.rows[o],r.header.length).map(u=>({text:u}));for(i=r.header.length,a=0;a<i;a++)r.header[a].tokens=this.lexer.inline(r.header[a].text);for(i=r.rows.length,a=0;a<i;a++)for(s=r.rows[a],l=0;l<s.length;l++)s[l].tokens=this.lexer.inline(s[l].text);return r}}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:wn(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):wn(n[0]):n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const a=Qo(r.slice(0,-1),"\\");if((r.length-a.length)%2===0)return}else{const a=vm(n[2],"()");if(a>-1){const s=(n[0].indexOf("!")===0?5:4)+n[1].length+a;n[2]=n[2].substring(0,a),n[0]=n[0].substring(0,s).trim(),n[3]=""}}let i=n[2],o="";if(this.options.pedantic){const a=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);a&&(i=a[1],o=a[3])}else o=n[3]?n[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(this.options.pedantic&&!/>$/.test(r)?i=i.slice(1):i=i.slice(1,-1)),au(n,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:o&&o.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){let i=(r[2]||r[1]).replace(/\s+/g," ");if(i=n[i.toLowerCase()],!i){const o=r[0].charAt(0);return{type:"text",raw:o,text:o}}return au(r,i,r[0],this.lexer)}}emStrong(t,n,r=""){let i=this.rules.inline.emStrong.lDelim.exec(t);if(!i||i[3]&&r.match(/[\p{L}\p{N}]/u))return;const o=i[1]||i[2]||"";if(!o||o&&(r===""||this.rules.inline.punctuation.exec(r))){const a=i[0].length-1;let l,s,u=a,c=0;const f=i[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(f.lastIndex=0,n=n.slice(-1*t.length+a);(i=f.exec(n))!=null;){if(l=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!l)continue;if(s=l.length,i[3]||i[4]){u+=s;continue}else if((i[5]||i[6])&&a%3&&!((a+s)%3)){c+=s;continue}if(u-=s,u>0)continue;s=Math.min(s,s+u+c);const g=t.slice(0,a+i.index+(i[0].length-l.length)+s);if(Math.min(a,s)%2){const m=g.slice(1,-1);return{type:"em",raw:g,text:m,tokens:this.lexer.inlineTokens(m)}}const h=g.slice(2,-2);return{type:"strong",raw:g,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const i=/[^ ]/.test(r),o=/^ /.test(r)&&/ $/.test(r);return i&&o&&(r=r.substring(1,r.length-1)),r=wn(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t,n){const r=this.rules.inline.autolink.exec(t);if(r){let i,o;return r[2]==="@"?(i=wn(this.options.mangle?n(r[1]):r[1]),o="mailto:"+i):(i=wn(r[1]),o=i),{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}url(t,n){let r;if(r=this.rules.inline.url.exec(t)){let i,o;if(r[2]==="@")i=wn(this.options.mangle?n(r[0]):r[0]),o="mailto:"+i;else{let a;do a=r[0],r[0]=this.rules.inline._backpedal.exec(r[0])[0];while(a!==r[0]);i=wn(r[0]),r[1]==="www."?o="http://"+r[0]:o=r[0]}return{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t,n){const r=this.rules.inline.text.exec(t);if(r){let i;return this.lexer.state.inRawBlock?i=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):wn(r[0]):r[0]:i=wn(this.options.smartypants?n(r[0]):r[0]),{type:"text",raw:r[0],text:i}}}}const Xe={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:aa,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};Xe._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;Xe._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;Xe.def=$t(Xe.def).replace("label",Xe._label).replace("title",Xe._title).getRegex();Xe.bullet=/(?:[*+-]|\d{1,9}[.)])/;Xe.listItemStart=$t(/^( *)(bull) */).replace("bull",Xe.bullet).getRegex();Xe.list=$t(Xe.list).replace(/bull/g,Xe.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+Xe.def.source+")").getRegex();Xe._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";Xe._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;Xe.html=$t(Xe.html,"i").replace("comment",Xe._comment).replace("tag",Xe._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();Xe.paragraph=$t(Xe._paragraph).replace("hr",Xe.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xe._tag).getRegex();Xe.blockquote=$t(Xe.blockquote).replace("paragraph",Xe.paragraph).getRegex();Xe.normal={...Xe};Xe.gfm={...Xe.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};Xe.gfm.table=$t(Xe.gfm.table).replace("hr",Xe.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xe._tag).getRegex();Xe.gfm.paragraph=$t(Xe._paragraph).replace("hr",Xe.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",Xe.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xe._tag).getRegex();Xe.pedantic={...Xe.normal,html:$t(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Xe._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:aa,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:$t(Xe.normal._paragraph).replace("hr",Xe.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Xe.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const Ee={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:aa,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:aa,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};Ee._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";Ee.punctuation=$t(Ee.punctuation).replace(/punctuation/g,Ee._punctuation).getRegex();Ee.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;Ee.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;Ee._comment=$t(Xe._comment).replace("(?:-->|$)","-->").getRegex();Ee.emStrong.lDelim=$t(Ee.emStrong.lDelim).replace(/punct/g,Ee._punctuation).getRegex();Ee.emStrong.rDelimAst=$t(Ee.emStrong.rDelimAst,"g").replace(/punct/g,Ee._punctuation).getRegex();Ee.emStrong.rDelimUnd=$t(Ee.emStrong.rDelimUnd,"g").replace(/punct/g,Ee._punctuation).getRegex();Ee._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;Ee._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;Ee._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;Ee.autolink=$t(Ee.autolink).replace("scheme",Ee._scheme).replace("email",Ee._email).getRegex();Ee._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;Ee.tag=$t(Ee.tag).replace("comment",Ee._comment).replace("attribute",Ee._attribute).getRegex();Ee._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;Ee._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;Ee._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;Ee.link=$t(Ee.link).replace("label",Ee._label).replace("href",Ee._href).replace("title",Ee._title).getRegex();Ee.reflink=$t(Ee.reflink).replace("label",Ee._label).replace("ref",Xe._label).getRegex();Ee.nolink=$t(Ee.nolink).replace("ref",Xe._label).getRegex();Ee.reflinkSearch=$t(Ee.reflinkSearch,"g").replace("reflink",Ee.reflink).replace("nolink",Ee.nolink).getRegex();Ee.normal={...Ee};Ee.pedantic={...Ee.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:$t(/^!?\[(label)\]\((.*?)\)/).replace("label",Ee._label).getRegex(),reflink:$t(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ee._label).getRegex()};Ee.gfm={...Ee.normal,escape:$t(Ee.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};Ee.gfm.url=$t(Ee.gfm.url,"i").replace("email",Ee.gfm._extended_email).getRegex();Ee.breaks={...Ee.gfm,br:$t(Ee.br).replace("{2,}","*").getRegex(),text:$t(Ee.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function ym(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function su(e){let t="",n,r;const i=e.length;for(n=0;n<i;n++)r=e.charCodeAt(n),Math.random()>.5&&(r="x"+r.toString(16)),t+="&#"+r+";";return t}class Lr{constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Kr,this.options.tokenizer=this.options.tokenizer||new Ws,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:Xe.normal,inline:Ee.normal};this.options.pedantic?(n.block=Xe.pedantic,n.inline=Ee.pedantic):this.options.gfm&&(n.block=Xe.gfm,this.options.breaks?n.inline=Ee.breaks:n.inline=Ee.gfm),this.tokenizer.rules=n}static get rules(){return{block:Xe,inline:Ee}}static lex(t,n){return new Lr(n).lex(t)}static lexInline(t,n){return new Lr(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);let n;for(;n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(t,n=[]){this.options.pedantic?t=t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t=t.replace(/^( *)(\t+)/gm,(l,s,u)=>s+"    ".repeat(u.length));let r,i,o,a;for(;t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>(r=l.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.space(t)){t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(r=this.tokenizer.fences(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const s=t.slice(1);let u;this.options.extensions.startBlock.forEach(function(c){u=c.call({lexer:this},s),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(o=t.substring(0,l+1))}if(this.state.top&&(r=this.tokenizer.paragraph(o))){i=n[n.length-1],a&&i.type==="paragraph"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r),a=o.length!==t.length,t=t.substring(r.raw.length);continue}if(r=this.tokenizer.text(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&i.type==="text"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,i,o,a=t,l,s,u;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(l=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(l[0].slice(l[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,l.index)+"["+ou("a",l[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(l=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,l.index)+"["+ou("a",l[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(l=this.tokenizer.rules.inline.escapedEmSt.exec(a))!=null;)a=a.slice(0,l.index+l[0].length-2)+"++"+a.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;t;)if(s||(u=""),s=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(r=c.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.escape(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.tag(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.link(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.emStrong(t,a,u)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.codespan(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.br(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.del(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.autolink(t,su)){t=t.substring(r.raw.length),n.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(t,su))){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const f=t.slice(1);let g;this.options.extensions.startInline.forEach(function(h){g=h.call({lexer:this},f),typeof g=="number"&&g>=0&&(c=Math.min(c,g))}),c<1/0&&c>=0&&(o=t.substring(0,c+1))}if(r=this.tokenizer.inlineText(o,ym)){t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(u=r.raw.slice(-1)),s=!0,i=n[n.length-1],i&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return n}}class Us{constructor(t){this.options=t||Kr}code(t,n,r){const i=(n||"").match(/\S*/)[0];if(this.options.highlight){const o=this.options.highlight(t,i);o!=null&&o!==t&&(r=!0,t=o)}return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="'+this.options.langPrefix+wn(i)+'">'+(r?t:wn(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:wn(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t){return t}heading(t,n,r,i){if(this.options.headerIds){const o=this.options.headerPrefix+i.slug(r);return`<h${n} id="${o}">${t}</h${n}>
`}return`<h${n}>${t}</h${n}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(t,n,r){const i=n?"ol":"ul",o=n&&r!==1?' start="'+r+'"':"";return"<"+i+o+`>
`+t+"</"+i+`>
`}listitem(t){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){if(t=ru(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i='<a href="'+t+'"';return n&&(i+=' title="'+n+'"'),i+=">"+r+"</a>",i}image(t,n,r){if(t=ru(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${n}"`),i+=this.options.xhtml?"/>":">",i}text(t){return t}}class ud{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class cd{constructor(){this.seen={}}serialize(t){return t.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(t,n){let r=t,i=0;if(this.seen.hasOwnProperty(r)){i=this.seen[t];do i++,r=t+"-"+i;while(this.seen.hasOwnProperty(r))}return n||(this.seen[t]=i,this.seen[r]=0),r}slug(t,n={}){const r=this.serialize(t);return this.getNextSafeSlug(r,n.dryrun)}}class _r{constructor(t){this.options=t||Kr,this.options.renderer=this.options.renderer||new Us,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ud,this.slugger=new cd}static parse(t,n){return new _r(n).parse(t)}static parseInline(t,n){return new _r(n).parseInline(t)}parse(t,n=!0){let r="",i,o,a,l,s,u,c,f,g,h,m,p,v,w,b,M,O,S,R;const L=t.length;for(i=0;i<L;i++){if(h=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[h.type]&&(R=this.options.extensions.renderers[h.type].call({parser:this},h),R!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(h.type))){r+=R||"";continue}switch(h.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{r+=this.renderer.heading(this.parseInline(h.tokens),h.depth,ld(this.parseInline(h.tokens,this.textRenderer)),this.slugger);continue}case"code":{r+=this.renderer.code(h.text,h.lang,h.escaped);continue}case"table":{for(f="",c="",l=h.header.length,o=0;o<l;o++)c+=this.renderer.tablecell(this.parseInline(h.header[o].tokens),{header:!0,align:h.align[o]});for(f+=this.renderer.tablerow(c),g="",l=h.rows.length,o=0;o<l;o++){for(u=h.rows[o],c="",s=u.length,a=0;a<s;a++)c+=this.renderer.tablecell(this.parseInline(u[a].tokens),{header:!1,align:h.align[a]});g+=this.renderer.tablerow(c)}r+=this.renderer.table(f,g);continue}case"blockquote":{g=this.parse(h.tokens),r+=this.renderer.blockquote(g);continue}case"list":{for(m=h.ordered,p=h.start,v=h.loose,l=h.items.length,g="",o=0;o<l;o++)b=h.items[o],M=b.checked,O=b.task,w="",b.task&&(S=this.renderer.checkbox(M),v?b.tokens.length>0&&b.tokens[0].type==="paragraph"?(b.tokens[0].text=S+" "+b.tokens[0].text,b.tokens[0].tokens&&b.tokens[0].tokens.length>0&&b.tokens[0].tokens[0].type==="text"&&(b.tokens[0].tokens[0].text=S+" "+b.tokens[0].tokens[0].text)):b.tokens.unshift({type:"text",text:S}):w+=S),w+=this.parse(b.tokens,v),g+=this.renderer.listitem(w,O,M);r+=this.renderer.list(g,m,p);continue}case"html":{r+=this.renderer.html(h.text);continue}case"paragraph":{r+=this.renderer.paragraph(this.parseInline(h.tokens));continue}case"text":{for(g=h.tokens?this.parseInline(h.tokens):h.text;i+1<L&&t[i+1].type==="text";)h=t[++i],g+=`
`+(h.tokens?this.parseInline(h.tokens):h.text);r+=n?this.renderer.paragraph(g):g;continue}default:{const E='Token with "'+h.type+'" type was not found.';if(this.options.silent){console.error(E);return}else throw new Error(E)}}}return r}parseInline(t,n){n=n||this.renderer;let r="",i,o,a;const l=t.length;for(i=0;i<l;i++){if(o=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]&&(a=this.options.extensions.renderers[o.type].call({parser:this},o),a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type))){r+=a||"";continue}switch(o.type){case"escape":{r+=n.text(o.text);break}case"html":{r+=n.html(o.text);break}case"link":{r+=n.link(o.href,o.title,this.parseInline(o.tokens,n));break}case"image":{r+=n.image(o.href,o.title,o.text);break}case"strong":{r+=n.strong(this.parseInline(o.tokens,n));break}case"em":{r+=n.em(this.parseInline(o.tokens,n));break}case"codespan":{r+=n.codespan(o.text);break}case"br":{r+=n.br();break}case"del":{r+=n.del(this.parseInline(o.tokens,n));break}case"text":{r+=n.text(o.text);break}default:{const s='Token with "'+o.type+'" type was not found.';if(this.options.silent){console.error(s);return}else throw new Error(s)}}}return r}}class sa{constructor(t){this.options=t||Kr}preprocess(t){return t}postprocess(t){return t}}dt(sa,"passThroughHooks",new Set(["preprocess","postprocess"]));function Cm(e,t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+wn(r.message+"",!0)+"</pre>";if(t)return Promise.resolve(i);if(n){n(null,i);return}return i}if(t)return Promise.reject(r);if(n){n(r);return}throw r}}function dd(e,t){return(n,r,i)=>{typeof r=="function"&&(i=r,r=null);const o={...r};r={...Ze.defaults,...o};const a=Cm(r.silent,r.async,i);if(typeof n>"u"||n===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(bm(r),r.hooks&&(r.hooks.options=r),i){const l=r.highlight;let s;try{r.hooks&&(n=r.hooks.preprocess(n)),s=e(n,r)}catch(f){return a(f)}const u=function(f){let g;if(!f)try{r.walkTokens&&Ze.walkTokens(s,r.walkTokens),g=t(s,r),r.hooks&&(g=r.hooks.postprocess(g))}catch(h){f=h}return r.highlight=l,f?a(f):i(null,g)};if(!l||l.length<3||(delete r.highlight,!s.length))return u();let c=0;Ze.walkTokens(s,function(f){f.type==="code"&&(c++,setTimeout(()=>{l(f.text,f.lang,function(g,h){if(g)return u(g);h!=null&&h!==f.text&&(f.text=h,f.escaped=!0),c--,c===0&&u()})},0))}),c===0&&u();return}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then(l=>e(l,r)).then(l=>r.walkTokens?Promise.all(Ze.walkTokens(l,r.walkTokens)).then(()=>l):l).then(l=>t(l,r)).then(l=>r.hooks?r.hooks.postprocess(l):l).catch(a);try{r.hooks&&(n=r.hooks.preprocess(n));const l=e(n,r);r.walkTokens&&Ze.walkTokens(l,r.walkTokens);let s=t(l,r);return r.hooks&&(s=r.hooks.postprocess(s)),s}catch(l){return a(l)}}}function Ze(e,t,n){return dd(Lr.lex,_r.parse)(e,t,n)}Ze.options=Ze.setOptions=function(e){return Ze.defaults={...Ze.defaults,...e},om(Ze.defaults),Ze};Ze.getDefaults=od;Ze.defaults=Kr;Ze.use=function(...e){const t=Ze.defaults.extensions||{renderers:{},childTokens:{}};e.forEach(n=>{const r={...n};if(r.async=Ze.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if(i.renderer){const o=t.renderers[i.name];o?t.renderers[i.name]=function(...a){let l=i.renderer.apply(this,a);return l===!1&&(l=o.apply(this,a)),l}:t.renderers[i.name]=i.renderer}if(i.tokenizer){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");t[i.level]?t[i.level].unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){const i=Ze.defaults.renderer||new Us;for(const o in n.renderer){const a=i[o];i[o]=(...l)=>{let s=n.renderer[o].apply(i,l);return s===!1&&(s=a.apply(i,l)),s}}r.renderer=i}if(n.tokenizer){const i=Ze.defaults.tokenizer||new Ws;for(const o in n.tokenizer){const a=i[o];i[o]=(...l)=>{let s=n.tokenizer[o].apply(i,l);return s===!1&&(s=a.apply(i,l)),s}}r.tokenizer=i}if(n.hooks){const i=Ze.defaults.hooks||new sa;for(const o in n.hooks){const a=i[o];sa.passThroughHooks.has(o)?i[o]=l=>{if(Ze.defaults.async)return Promise.resolve(n.hooks[o].call(i,l)).then(u=>a.call(i,u));const s=n.hooks[o].call(i,l);return a.call(i,s)}:i[o]=(...l)=>{let s=n.hooks[o].apply(i,l);return s===!1&&(s=a.apply(i,l)),s}}r.hooks=i}if(n.walkTokens){const i=Ze.defaults.walkTokens;r.walkTokens=function(o){let a=[];return a.push(n.walkTokens.call(this,o)),i&&(a=a.concat(i.call(this,o))),a}}Ze.setOptions(r)})};Ze.walkTokens=function(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(Ze,r)),r.type){case"table":{for(const i of r.header)n=n.concat(Ze.walkTokens(i.tokens,t));for(const i of r.rows)for(const o of i)n=n.concat(Ze.walkTokens(o.tokens,t));break}case"list":{n=n.concat(Ze.walkTokens(r.items,t));break}default:Ze.defaults.extensions&&Ze.defaults.extensions.childTokens&&Ze.defaults.extensions.childTokens[r.type]?Ze.defaults.extensions.childTokens[r.type].forEach(function(i){n=n.concat(Ze.walkTokens(r[i],t))}):r.tokens&&(n=n.concat(Ze.walkTokens(r.tokens,t)))}return n};Ze.parseInline=dd(Lr.lexInline,_r.parseInline);Ze.Parser=_r;Ze.parser=_r.parse;Ze.Renderer=Us;Ze.TextRenderer=ud;Ze.Lexer=Lr;Ze.lexer=Lr.lex;Ze.Tokenizer=Ws;Ze.Slugger=cd;Ze.Hooks=sa;Ze.parse=Ze;Ze.options;Ze.setOptions;Ze.use;Ze.walkTokens;Ze.parseInline;_r.parse;Lr.lex;const Sm=mn("div")({name:"MarkdownContainer",class:"gdg-mnuv029",propsAsIs:!1});class xm extends At.PureComponent{constructor(){super(...arguments);dt(this,"targetElement",null);dt(this,"containerRefHook",n=>{this.targetElement=n,this.renderMarkdownIntoDiv()})}renderMarkdownIntoDiv(){const{targetElement:n,props:r}=this;if(n===null)return;const{contents:i,createNode:o}=r,a=Ze(i),l=document.createRange();l.selectNodeContents(n),l.deleteContents();let s=o==null?void 0:o(a);if(s===void 0){const c=document.createElement("template");c.innerHTML=a,s=c.content}n.append(s);const u=n.getElementsByTagName("a");for(const c of u)c.target="_blank",c.rel="noreferrer noopener"}render(){return this.renderMarkdownIntoDiv(),At.createElement(Sm,{ref:this.containerRefHook})}}const km=mn("textarea")({name:"InputBox",class:"gdg-izpuzkl",propsAsIs:!1}),Mm=mn("div")({name:"ShadowBox",class:"gdg-s69h75o",propsAsIs:!1}),Rm=mn("div")({name:"GrowingEntryStyle",class:"gdg-g1y0xocz",propsAsIs:!1});let lu=0;const Zr=e=>{const{placeholder:t,value:n,onKeyDown:r,highlight:i,altNewline:o,validatedSelection:a,...l}=e,{onChange:s,className:u}=l,c=d.useRef(null),f=n??"";Fn(s!==void 0,"GrowingEntry must be a controlled input area");const[g]=d.useState(()=>"input-box-"+(lu=(lu+1)%1e7));d.useEffect(()=>{const m=c.current;if(m===null||m.disabled)return;const p=f.toString().length;m.focus(),m.setSelectionRange(i?0:p,p)},[]),d.useLayoutEffect(()=>{var m;if(a!==void 0){const p=typeof a=="number"?[a,null]:a;(m=c.current)==null||m.setSelectionRange(p[0],p[1])}},[a]);const h=d.useCallback(m=>{m.key==="Enter"&&m.shiftKey&&o===!0||r==null||r(m)},[o,r]);return d.createElement(Rm,{className:"gdg-growing-entry"},d.createElement(Mm,{className:u},f+`
`),d.createElement(km,{...l,className:(u??"")+" gdg-input",id:g,ref:c,onKeyDown:h,value:f,placeholder:t,dir:"auto"}))},es={};let Tr=null;function Em(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}function la(e){const t=e.toLowerCase().trim();if(es[t]!==void 0)return es[t];Tr=Tr||Em(),Tr.style.color="#000",Tr.style.color=t;const n=getComputedStyle(Tr).color;Tr.style.color="#fff",Tr.style.color=t;const r=getComputedStyle(Tr).color;if(r!==n)return[0,0,0,1];let i=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return i.length<4&&i.push(1),i=i.map(o=>Number.isNaN(o)?0:o),es[t]=i,i}function jr(e,t){const[n,r,i]=la(e);return`rgba(${n}, ${r}, ${i}, ${t})`}const uu=new Map;function cu(e,t){const n=`${e}-${t}`,r=uu.get(n);if(r!==void 0)return r;const i=An(e,t);return uu.set(n,i),i}function An(e,t){if(t===void 0)return e;const[n,r,i,o]=la(e);if(o===1)return e;const[a,l,s,u]=la(t),c=o+u*(1-o),f=(o*n+u*a*(1-o))/c,g=(o*r+u*l*(1-o))/c,h=(o*i+u*s*(1-o))/c;return`rgba(${f}, ${g}, ${h}, ${c})`}var pi=new Map,bi=new Map,ks=new Map;function Im(){pi.clear(),ks.clear(),bi.clear()}function Tm(e,t,n,r,i){var o,a,l;let s=0,u={};for(let f of e)s+=(o=n.get(f))!=null?o:i,u[f]=((a=u[f])!=null?a:0)+1;let c=t-s;for(let f of Object.keys(u)){let g=u[f],h=(l=n.get(f))!=null?l:i,m=h*g/s,p=c*m*r/g,v=h+p;n.set(f,v)}}function Dm(e,t){var n;let r=new Map,i=0;for(let u of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let c=e.measureText(u).width;r.set(u,c),i+=c}let o=i/r.size,a=3,l=(t/o+a)/(a+1),s=r.keys();for(let u of s)r.set(u,((n=r.get(u))!=null?n:o)*l);return r}function to(e,t,n,r){var i,o;let a=bi.get(n);if(r&&a!==void 0&&a.count>2e4){let u=ks.get(n);if(u===void 0&&(u=Dm(e,a.size),ks.set(n,u)),a.count>5e5){let f=0;for(let g of t)f+=(i=u.get(g))!=null?i:a.size;return f*1.01}let c=e.measureText(t);return Tm(t,c.width,u,Math.max(.05,1-a.count/2e5),a.size),bi.set(n,{count:a.count+t.length,size:a.size}),c.width}let l=e.measureText(t),s=l.width/t.length;if(((o=a==null?void 0:a.count)!=null?o:0)>2e4)return l.width;if(a===void 0)bi.set(n,{count:t.length,size:s});else{let u=s-a.size,c=t.length/(a.count+t.length),f=a.size+u*c;bi.set(n,{count:a.count+t.length,size:f})}return l.width}function Om(e,t,n,r,i,o,a,l){if(t.length<=1)return t.length;if(i<n)return-1;let s=Math.floor(n/i*o),u=to(e,t.slice(0,Math.max(0,s)),r,a);if(u!==n)if(u<n){for(;u<n;)s++,u=to(e,t.slice(0,Math.max(0,s)),r,a);s--}else for(;u>n;){let c=t.lastIndexOf(" ",s-1);c>0?s=c:s--,u=to(e,t.slice(0,Math.max(0,s)),r,a)}if(t[s]!==" "){let c=0;c=t.lastIndexOf(" ",s),c>0&&(s=c)}return s}function Pm(e,t,n,r,i,o){let a=`${t}_${n}_${r}px`,l=pi.get(a);if(l!==void 0)return l;if(r<=0)return[];let s=[],u=t.split(`
`),c=bi.get(n),f=c===void 0?t.length:r/c.size*1.5,g=i&&c!==void 0&&c.count>2e4;for(let h of u){let m=to(e,h.slice(0,Math.max(0,f)),n,g),p=Math.min(h.length,f);if(m<=r)s.push(h);else{for(;m>r;){let v=Om(e,h,r,n,m,p,g),w=h.slice(0,Math.max(0,v));h=h.slice(w.length),s.push(w),m=to(e,h.slice(0,Math.max(0,f)),n,g),p=Math.min(h.length,f)}m>0&&s.push(h)}}return s=s.map((h,m)=>m===0?h.trimEnd():h.trim()),pi.set(a,s),pi.size>500&&pi.delete(pi.keys().next().value),s}function Lm(e,t){return At.useMemo(()=>e.map((n,r)=>({group:n.group,grow:n.grow,hasMenu:n.hasMenu,icon:n.icon,id:n.id,menuIcon:n.menuIcon,overlayIcon:n.overlayIcon,sourceIndex:r,sticky:r<t,indicatorIcon:n.indicatorIcon,style:n.style,themeOverride:n.themeOverride,title:n.title,trailingRowOptions:n.trailingRowOptions,width:n.width,growOffset:n.growOffset,rowMarker:n.rowMarker,rowMarkerChecked:n.rowMarkerChecked,headerRowMarkerTheme:n.headerRowMarkerTheme,headerRowMarkerAlwaysVisible:n.headerRowMarkerAlwaysVisible,headerRowMarkerDisabled:n.headerRowMarkerDisabled})),[e,t])}function _m(e,t){const[n,r]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(r))return!0;if(e.current!==void 0){if(no(e.current.cell,t))return!0;const i=[e.current.range,...e.current.rangeStack];for(const o of i)if(n>=o.x&&n<o.x+o.width&&r>=o.y&&r<o.y+o.height)return!0}return!1}function so(e,t){return(e??"")===(t??"")}function Fm(e,t,n){return n.current===void 0||e[1]!==n.current.cell[1]?!1:t.span===void 0?n.current.cell[0]===e[0]:n.current.cell[0]>=t.span[0]&&n.current.cell[0]<=t.span[1]}function fd(e,t){const[n,r]=e;return n>=t.x&&n<t.x+t.width&&r>=t.y&&r<t.y+t.height}function no(e,t){return(e==null?void 0:e[0])===(t==null?void 0:t[0])&&(e==null?void 0:e[1])===(t==null?void 0:t[1])}function hd(e){return[e.x+e.width-1,e.y+e.height-1]}function du(e,t,n){const r=n.x,i=n.x+n.width-1,o=n.y,a=n.y+n.height-1,[l,s]=e;if(s<o||s>a)return!1;if(t.span===void 0)return l>=r&&l<=i;const[u,c]=t.span;return u>=r&&u<=i||c>=r&&u<=i||u<r&&c>i}function Am(e,t,n,r){let i=0;if(n.current===void 0)return i;const o=n.current.range;(r||o.height*o.width>1)&&du(e,t,o)&&i++;for(const a of n.current.rangeStack)du(e,t,a)&&i++;return i}function gd(e,t){let n=e;if(t!==void 0){let r=[...e];const i=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,i)):(r.splice(t.dest+1,0,i),r.splice(t.src,1)),r=r.map((o,a)=>({...o,sticky:e[a].sticky})),n=r}return n}function Si(e,t){let n=0;const r=gd(e,t);for(let i=0;i<r.length;i++){const o=r[i];if(o.sticky)n+=o.width;else break}return n}function Jr(e,t,n){if(typeof n=="number")return t*n;{let r=0;for(let i=e-t;i<e;i++)r+=n(i);return r}}function Ms(e,t,n,r,i){const o=gd(e,r),a=[];for(const u of o)if(u.sticky)a.push(u);else break;if(a.length>0)for(const u of a)n-=u.width;let l=t,s=i??0;for(;s<=n&&l<o.length;)s+=o[l].width,l++;for(let u=t;u<l;u++){const c=o[u];c.sticky||a.push(c)}return a}function Hm(e,t,n){let r=0;for(const i of t){const o=i.sticky?r:r+(n??0);if(e<=o+i.width)return i.sourceIndex;r+=i.width}return-1}function zm(e,t,n,r,i,o,a,l,s,u){const c=r+i;if(n&&e<=i)return-2;if(e<=c)return-1;let f=t;for(let m=0;m<u;m++){const p=o-1-m,v=typeof a=="number"?a:a(p);if(f-=v,e>=f)return p}const g=o-u,h=e-(s??0);if(typeof a=="number"){const m=Math.floor((h-c)/a)+l;return m>=g?void 0:m}else{let m=c;for(let p=l;p<g;p++){const v=a(p);if(h<=m+v)return p;m+=v}return}}let ea=0,ro={};const Vm=typeof window>"u";async function Nm(){var e;Vm||((e=document==null?void 0:document.fonts)==null?void 0:e.ready)===void 0||(await document.fonts.ready,ea=0,ro={},Im())}Nm();function md(e,t,n,r){return`${e}_${r??(t==null?void 0:t.font)}_${n}`}function Qr(e,t,n,r="middle"){const i=md(e,t,r,n);let o=ro[i];return o===void 0&&(o=t.measureText(e),ro[i]=o,ea++),ea>1e4&&(ro={},ea=0),o}function pd(e,t){const n=md(e,void 0,"middle",t);return ro[n]}function dr(e,t){return typeof t!="string"&&(t=t.baseFontFull),$m(e,t)}function fu(e,t){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ";e.save(),e.textBaseline=t;const r=e.measureText(n);return e.restore(),r}const hu=[];function $m(e,t){for(const o of hu)if(o.key===t)return o.val;const n=fu(e,"alphabetic"),i=-(fu(e,"middle").actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return hu.push({key:t,val:i}),i}function Bm(e,t,n,r,i,o){const{ctx:a,rect:l,theme:s}=e;let u=Number.MAX_SAFE_INTEGER;const c=500;if(t!==void 0&&(u=n-t,u<c)){const f=1-u/c;a.globalAlpha=f,a.fillStyle=s.bgSearchResult,a.fillRect(l.x+1,l.y+1,l.width-(i?2:1),l.height-(o?2:1)),a.globalAlpha=1,r!==void 0&&(r.fillStyle=s.bgSearchResult)}return u<c}function mo(e,t,n){const{ctx:r,theme:i}=e,o=t??{},a=n??i.textDark;return a!==o.fillStyle&&(r.fillStyle=a,o.fillStyle=a),o}function qs(e,t,n){const{rect:r,ctx:i,theme:o}=e;i.fillStyle=o.textDark,ur({ctx:i,rect:r,theme:o},t,n)}function vd(e,t,n,r,i,o,a,l,s){s==="right"?e.fillText(t,n+i-(l.cellHorizontalPadding+.5),r+o/2+a):s==="center"?e.fillText(t,n+i/2,r+o/2+a):e.fillText(t,n+l.cellHorizontalPadding+.5,r+o/2+a)}function bd(e,t){const n=Qr("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function Wm(e,t){e.includes(`
`)&&(e=e.split(/\r?\n/,1)[0]);const n=t/4;return e.length>n&&(e=e.slice(0,n)),e}function Um(e,t,n,r,i,o,a,l,s,u){const c=l.baseFontFull,f=Pm(e,t,c,i-l.cellHorizontalPadding*2,u??!1),g=bd(e,c),h=l.lineHeight*g,m=g+h*(f.length-1),p=m+l.cellVerticalPadding>o;p&&(e.save(),e.rect(n,r,i,o),e.clip());const v=r+o/2-m/2;let w=Math.max(r+l.cellVerticalPadding,v);for(const b of f)if(vd(e,b,n,w,i,g,a,l,s),w+=h,w>r+o)break;p&&e.restore()}function ur(e,t,n,r,i){const{ctx:o,rect:a,theme:l}=e,{x:s,y:u,width:c,height:f}=a;r=r??!1,r||(t=Wm(t,c));const g=dr(o,l),h=Bs(t)==="rtl";if(n===void 0&&h&&(n="right"),h&&(o.direction="rtl"),t.length>0){let m=!1;n==="right"?(o.textAlign="right",m=!0):n!==void 0&&n!=="left"&&(o.textAlign=n,m=!0),r?Um(o,t,s,u,c,f,g,l,n,i):vd(o,t,s,u,c,f,g,l,n),m&&(o.textAlign="start"),h&&(o.direction="inherit")}}function cr(e,t,n,r,i,o){typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.max(0,Math.min(o.tl,i/2,r/2)),tr:Math.max(0,Math.min(o.tr,i/2,r/2)),bl:Math.max(0,Math.min(o.bl,i/2,r/2)),br:Math.max(0,Math.min(o.br,i/2,r/2))},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}function qm(e,t,n){e.arc(t,n-1.25*3.5,1.25,0,2*Math.PI,!1),e.arc(t,n,1.25,0,2*Math.PI,!1),e.arc(t,n******3.5,1.25,0,2*Math.PI,!1)}function Ym(e,t,n){const r=function(l,s){const u=s.x-l.x,c=s.y-l.y,f=Math.sqrt(u*u+c*c),g=u/f,h=c/f;return{x:u,y:s.y-l.y,len:f,nx:g,ny:h,ang:Math.atan2(h,g)}};let i;const o=t.length;let a=t[o-1];for(let l=0;l<o;l++){let s=t[l%o];const u=t[(l+1)%o],c=r(s,a),f=r(s,u),g=c.nx*f.ny-c.ny*f.nx,h=c.nx*f.nx-c.ny*-f.ny;let m=Math.asin(g<-1?-1:g>1?1:g),p=1,v=!1;h<0?m<0?m=Math.PI+m:(m=Math.PI-m,p=-1,v=!0):m>0&&(p=-1,v=!0),i=s.radius!==void 0?s.radius:n;const w=m/2;let b=Math.abs(Math.cos(w)*i/Math.sin(w)),M;b>Math.min(c.len/2,f.len/2)?(b=Math.min(c.len/2,f.len/2),M=Math.abs(b*Math.sin(w)/Math.cos(w))):M=i;let O=s.x+f.nx*b,S=s.y+f.ny*b;O+=-f.ny*M*p,S+=f.nx*M*p,e.arc(O,S,M,c.ang+Math.PI/2*p,f.ang-Math.PI/2*p,v),a=s,s=u}e.closePath()}function Rs(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m){const p={x:0,y:o+u,width:0,height:0};if(e>=h.length||t>=c||t<-2||e<0)return p;const v=o-i;if(e>=f){const w=a>e?-1:1,b=Si(h);p.x+=b+s;for(let M=a;M!==e;M+=w)p.x+=h[w===1?M:M-1].width*w}else for(let w=0;w<e;w++)p.x+=h[w].width;if(p.width=h[e].width+1,t===-1)p.y=i,p.height=v;else if(t===-2){p.y=0,p.height=i;let w=e;const b=h[e].group,M=h[e].sticky;for(;w>0&&so(h[w-1].group,b)&&h[w-1].sticky===M;){const S=h[w-1];p.x-=S.width,p.width+=S.width,w--}let O=e;for(;O+1<h.length&&so(h[O+1].group,b)&&h[O+1].sticky===M;){const S=h[O+1];p.width+=S.width,O++}if(!M){const S=Si(h),R=p.x-S;R<0&&(p.x-=R,p.width+=R),p.x+p.width>n&&(p.width=n-p.x)}}else if(t>=c-g){let w=c-t;for(p.y=r;w>0;){const b=t+w-1;p.height=typeof m=="number"?m:m(b),p.y-=p.height,w--}p.height+=1}else{const w=l>t?-1:1;if(typeof m=="number"){const b=t-l;p.y+=b*m}else for(let b=l;b!==t;b+=w)p.y+=m(b)*w;p.height=(typeof m=="number"?m:m(t))+1}return p}const Ys=1<<21;function tr(e,t){return(t+2)*Ys+e}function wd(e){return e%Ys}function Xs(e){return Math.floor(e/Ys)-2}function js(e){const t=wd(e),n=Xs(e);return[t,n]}class yd{constructor(){dt(this,"visibleWindow",{x:0,y:0,width:0,height:0});dt(this,"freezeCols",0);dt(this,"freezeRows",[]);dt(this,"isInWindow",t=>{const n=wd(t),r=Xs(t),i=this.visibleWindow,o=n>=i.x&&n<=i.x+i.width||n<this.freezeCols,a=r>=i.y&&r<=i.y+i.height||this.freezeRows.includes(r);return o&&a})}setWindow(t,n,r){this.visibleWindow.x===t.x&&this.visibleWindow.y===t.y&&this.visibleWindow.width===t.width&&this.visibleWindow.height===t.height&&this.freezeCols===n&&Ci(this.freezeRows,r)||(this.visibleWindow=t,this.freezeCols=n,this.freezeRows=r,this.clearOutOfWindow())}}class Xm extends yd{constructor(){super(...arguments);dt(this,"cache",new Map);dt(this,"setValue",(n,r)=>{this.cache.set(tr(n[0],n[1]),r)});dt(this,"getValue",n=>this.cache.get(tr(n[0],n[1])));dt(this,"clearOutOfWindow",()=>{for(const[n]of this.cache.entries())this.isInWindow(n)||this.cache.delete(n)})}}class io{constructor(t=[]){dt(this,"cells");this.cells=new Set(t.map(n=>tr(n[0],n[1])))}add(t){this.cells.add(tr(t[0],t[1]))}has(t){return t===void 0?!1:this.cells.has(tr(t[0],t[1]))}remove(t){this.cells.delete(tr(t[0],t[1]))}clear(){this.cells.clear()}get size(){return this.cells.size}hasHeader(){for(const t of this.cells)if(Xs(t)<0)return!0;return!1}hasItemInRectangle(t){for(let n=t.y;n<t.y+t.height;n++)for(let r=t.x;r<t.x+t.width;r++)if(this.cells.has(tr(r,n)))return!0;return!1}hasItemInRegion(t){for(const n of t)if(this.hasItemInRectangle(n))return!0;return!1}*values(){for(const t of this.cells)yield js(t)}}function jm(e){return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":e.textGroupHeader??e.textHeader,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":e.horizontalBorderColor??e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":`${e.cellHorizontalPadding}px`,"--gdg-cell-vertical-padding":`${e.cellVerticalPadding}px`,"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize,...e.resizeIndicatorColor===void 0?{}:{"--gdg-resize-indicator-color":e.resizeIndicatorColor},...e.headerBottomBorderColor===void 0?{}:{"--gdg-header-bottom-border-color":e.headerBottomBorderColor},...e.roundingRadius===void 0?{}:{"--gdg-rounding-radius":`${e.roundingRadius}px`}}}const Cd={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#353fb5",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4};function Sd(){return Cd}const xd=At.createContext(Cd);function Gm(){return At.useContext(xd)}function vr(e,...t){const n={...e};for(const r of t)if(r!==void 0)for(const i in r)r.hasOwnProperty(i)&&(i==="bgCell"?n[i]=An(r[i],n[i]):n[i]=r[i]);return(n.headerFontFull===void 0||e.fontFamily!==n.fontFamily||e.headerFontStyle!==n.headerFontStyle)&&(n.headerFontFull=`${n.headerFontStyle} ${n.fontFamily}`),(n.baseFontFull===void 0||e.fontFamily!==n.fontFamily||e.baseFontStyle!==n.baseFontStyle)&&(n.baseFontFull=`${n.baseFontStyle} ${n.fontFamily}`),(n.markerFontFull===void 0||e.fontFamily!==n.fontFamily||e.markerFontStyle!==n.markerFontStyle)&&(n.markerFontFull=`${n.markerFontStyle} ${n.fontFamily}`),n}const Es=150;function Km(e,t,n,r){var o;const i=r(t);return((o=i==null?void 0:i.measure)==null?void 0:o.call(i,e,t,n))??Es}function kd(e,t,n,r,i,o,a,l,s){let u=0;const c=i===void 0?[]:i.map(g=>{const h=Km(e,g[r],t,s);return u=Math.max(u,h),h});if(c.length>5&&l){u=0;let g=0;for(const m of c)g+=m;const h=g/c.length;for(let m=0;m<c.length;m++)c[m]>=h*2?c[m]=0:u=Math.max(u,c[m])}u=Math.max(u,e.measureText(n.title).width+t.cellHorizontalPadding*2+(n.icon===void 0?0:28));const f=Math.max(Math.ceil(o),Math.min(Math.floor(a),Math.ceil(u)));return{...n,width:f}}function Zm(e,t,n,r,i,o,a,l,s){const u=d.useRef(t),c=d.useRef(n),f=d.useRef(a);u.current=t,c.current=n,f.current=a;const[g,h]=d.useMemo(()=>{if(typeof window>"u")return[null,null];const b=document.createElement("canvas");return b.style.display="none",b.style.opacity="0",b.style.position="fixed",[b,b.getContext("2d",{alpha:!1})]},[]);d.useLayoutEffect(()=>(g&&document.documentElement.append(g),()=>{g==null||g.remove()}),[g]);const m=d.useRef({}),p=d.useRef(),[v,w]=d.useState();return d.useLayoutEffect(()=>{const b=c.current;if(b===void 0||e.every(Ho))return;let M=Math.max(1,10-Math.floor(e.length/1e4)),O=0;M<u.current&&M>1&&(M--,O=1);const S={x:0,y:0,width:e.length,height:Math.min(u.current,M)},R={x:0,y:u.current-1,width:e.length,height:1};(async()=>{const E=b(S,s.signal),x=O>0?b(R,s.signal):void 0;let _;typeof E=="object"?_=E:_=await Al(E),x!==void 0&&(typeof x=="object"?_=[..._,...x]:_=[..._,...await Al(x)]),p.current=e,w(_)})()},[s.signal,e]),d.useMemo(()=>{let M=e.every(Ho)?e:h===null?e.map(L=>Ho(L)?L:{...L,width:Es}):(h.font=f.current.baseFontFull,e.map((L,E)=>{if(Ho(L))return L;if(m.current[L.id]!==void 0)return{...L,width:m.current[L.id]};if(v===void 0||p.current!==e||L.id===void 0)return{...L,width:Es};const x=kd(h,a,L,E,v,i,o,!0,l);return m.current[L.id]=x.width,x})),O=0,S=0;const R=[];for(const[L,E]of M.entries())O+=E.width,E.grow!==void 0&&E.grow>0&&(S+=E.grow,R.push(L));if(O<r&&R.length>0){const L=[...M],E=r-O;let x=E;for(let _=0;_<R.length;_++){const D=R[_],C=(M[D].grow??0)/S,I=_===R.length-1?x:Math.min(x,Math.floor(E*C));L[D]={...M[D],growOffset:I,width:M[D].width+I},x-=I}M=L}return{sizedColumns:M,nonGrowWidth:O}},[r,e,h,v,a,i,o,l])}var ts,gu;function Jm(){if(gu)return ts;gu=1;function e(t,n,r){return t===t&&(r!==void 0&&(t=t<=r?t:r),n!==void 0&&(t=t>=n?t:n)),t}return ts=e,ts}var ns,mu;function Qm(){if(mu)return ns;mu=1;var e=Jm(),t=Dc();function n(r,i,o){return o===void 0&&(o=i,i=void 0),o!==void 0&&(o=t(o),o=o===o?o:0),i!==void 0&&(i=t(i),i=i===i?i:0),e(t(r),i,o)}return ns=n,ns}var ep=Qm();const Un=wr(ep);var rs,pu;function tp(){if(pu)return rs;pu=1;function e(){}return rs=e,rs}var is,vu;function np(){if(vu)return is;vu=1;var e=vh(),t=tp(),n=Oc(),r=1/0,i=e&&1/n(new e([,-0]))[1]==r?function(o){return new e(o)}:t;return is=i,is}var os,bu;function rp(){if(bu)return os;bu=1;var e=bh(),t=wh(),n=Ch(),r=yh(),i=np(),o=Oc(),a=200;function l(s,u,c){var f=-1,g=t,h=s.length,m=!0,p=[],v=p;if(c)m=!1,g=n;else if(h>=a){var w=u?null:i(s);if(w)return o(w);m=!1,g=r,v=new e}else v=u?[]:p;e:for(;++f<h;){var b=s[f],M=u?u(b):b;if(b=c||b!==0?b:0,m&&M===M){for(var O=v.length;O--;)if(v[O]===M)continue e;u&&v.push(M),p.push(b)}else g(v,M,c)||(v!==p&&v.push(M),p.push(b))}return p}return os=l,os}var as,wu;function ip(){if(wu)return as;wu=1;var e=rp();function t(n){return n&&n.length?e(n):[]}return as=t,as}var op=ip();const ap=wr(op);var sp=Sh();const yu=wr(sp),qt='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',lp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}<rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="${t}"/></svg>`},up=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}<rect x="2" y="2" width="16" height="16" rx="4" fill="${n}"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="${t}"/></svg>`},cp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="${t}"/>
  </svg>`},dp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="${t}"/>
</svg>`},fp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
    <path
        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"
        fill="${n}"
    />
    <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"
        fill="${t}"
    />
</svg>`},Md=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="${t}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="${t}"/>
</svg>
  `},hp=e=>{const t=e.bgColor;return`${qt}
    <path stroke="${t}" stroke-width="2" d="M12 3v14"/>
    <path stroke="${t}" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>
    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="${t}"/>
  </svg>
`},gp=Md,mp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="${t}"/>
</svg>`},pp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="${t}"/>
    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="${t}"/>
    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
  </svg>`},vp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="${t}"/>
</svg>`},bp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path fill="${t}" d="M3 3h14v14H3z"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="${n}"/>
  </svg>`},wp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="${t}"/>
  </svg>`},yp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="${t}"/>
</svg>`},Cp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="${t}"/>
    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="${t}"/>
  </svg>`},Sp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="${t}"/>
</svg>`},xp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="8" width="10" height="8" rx="2" fill="${n}"/>
    <rect x="8" y="4" width="10" height="8" rx="2" fill="${n}"/>
    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="${t}"/>
  </svg>`},kp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path fill="${t}" d="M4 3h12v14H4z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="${n}"/>
</svg>`},Mp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="${t}"/>
  </svg>`},Rp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="${t}"/>
  </svg>`},Ep=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="${t}"/>
</svg>`},Ip=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="${t}"/>
  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="${t}"/>
</svg>`},Tp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="${t}"/>
  </svg>`},Dp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="${t}"/>
</svg>`},Op=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="${t}"/>
  </svg>`},Pp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="${t}"/>
</svg>`},Lp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="${t}"/>
</svg>`},_p=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>`},Fp=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>
`},Ap={headerRowID:lp,headerNumber:cp,headerCode:up,headerString:dp,headerBoolean:fp,headerAudioUri:gp,headerVideoUri:mp,headerEmoji:pp,headerImage:vp,headerUri:Md,headerPhone:bp,headerMarkdown:wp,headerDate:yp,headerTime:Cp,headerEmail:Sp,headerReference:xp,headerIfThenElse:kp,headerSingleValue:Mp,headerLookup:Rp,headerTextTemplate:Ep,headerMath:Ip,headerRollup:Tp,headerJoinStrings:Dp,headerSplitString:Op,headerGeoDistance:Pp,headerArray:Lp,rowOwnerOverlay:_p,protectedColumnOverlay:Fp,renameIcon:hp};function Hp(e,t){return e==="normal"?[t.bgIconHeader,t.fgIconHeader]:e==="selected"?["white",t.accentColor]:[t.accentColor,t.bgHeader]}class zp{constructor(t,n){dt(this,"onSettled");dt(this,"spriteMap",new Map);dt(this,"headerIcons");dt(this,"inFlight",0);this.onSettled=n,this.headerIcons=t??{}}drawSprite(t,n,r,i,o,a,l,s=1){const[u,c]=Hp(n,l),f=a*Math.ceil(window.devicePixelRatio),g=`${u}_${c}_${f}_${t}`;let h=this.spriteMap.get(g);if(h===void 0){const m=this.headerIcons[t];if(m===void 0)return;h=document.createElement("canvas");const p=h.getContext("2d");if(p===null)return;const v=new Image;v.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(m({fgColor:c,bgColor:u}))}`,this.spriteMap.set(g,h);const w=v.decode();if(w===void 0)return;this.inFlight++,w.then(()=>{p.drawImage(v,0,0,f,f)}).finally(()=>{this.inFlight--,this.inFlight===0&&this.onSettled()})}else s<1&&(r.globalAlpha=s),r.drawImage(h,0,0,f,f,i,o,a,a),s<1&&(r.globalAlpha=1)}}function Rd(e){if(e.length===0)return;let t;for(const n of e)t=Math.min(t??n.y,n.y)}function ka(e,t,n,r,i,o,a,l,s){l=l??t;let u=t,c=e;const f=r-o;let g=!1;for(;u<n&&c<f;){const h=i(c);if(u+h>l&&s(u,c,h,!1,a&&c===r-1)===!0){g=!0;break}u+=h,c++}if(!g){u=n;for(let h=0;h<o;h++){c=r-1-h;const m=i(c);u-=m,s(u,c,m,!0,a&&c===r-1)}}}function Fr(e,t,n,r,i,o){let a=0,l=0;const s=i+r;for(const u of e){const c=u.sticky?l:a+n;if(o(u,c,s,u.sticky?0:l,t)===!0)break;a+=u.width,l+=u.sticky?u.width:0}}function Ed(e,t,n,r,i){let o=0,a=0;for(let l=0;l<e.length;l++){const s=e[l];let u=l+1,c=s.width;for(s.sticky&&(a+=c);u<e.length&&so(e[u].group,s.group)&&e[u].sticky===e[l].sticky;){const p=e[u];c+=p.width,u++,l++,p.sticky&&(a+=p.width)}const f=s.sticky?0:n,g=o+f,h=s.sticky?0:Math.max(0,a-g),m=Math.min(c-h,t-(g+h));i([s.sourceIndex,e[u-1].sourceIndex],s.group??"",g+h,0,m,r),o+=c}}function Id(e,t,n,r,i,o,a){var g;const[l,s]=e;let u,c;const f=((g=a.find(h=>!h.sticky))==null?void 0:g.sourceIndex)??0;if(s>f){const h=Math.max(l,f);let m=t,p=r;for(let v=o.sourceIndex-1;v>=h;v--)m-=a[v].width,p+=a[v].width;for(let v=o.sourceIndex+1;v<=s;v++)p+=a[v].width;c={x:m,y:n,width:p,height:i}}if(f>l){const h=Math.min(s,f-1);let m=t,p=r;for(let v=o.sourceIndex-1;v>=l;v--)m-=a[v].width,p+=a[v].width;for(let v=o.sourceIndex+1;v<=h;v++)p+=a[v].width;u={x:m,y:n,width:p,height:i}}return[u,c]}function Vp(e,t,n,r){if(r==="any")return Td(e,{x:t,y:n,width:1,height:1});if(r==="vertical"&&(t=e.x),r==="horizontal"&&(n=e.y),fd([t,n],e))return;const i=t-e.x,o=e.x+e.width-t,a=n-e.y+1,l=e.y+e.height-n,s=Math.min(r==="vertical"?Number.MAX_SAFE_INTEGER:i,r==="vertical"?Number.MAX_SAFE_INTEGER:o,r==="horizontal"?Number.MAX_SAFE_INTEGER:a,r==="horizontal"?Number.MAX_SAFE_INTEGER:l);return s===l?{x:e.x,y:e.y+e.height,width:e.width,height:n-e.y-e.height+1}:s===a?{x:e.x,y:n,width:e.width,height:e.y-n}:s===o?{x:e.x+e.width,y:e.y,width:t-e.x-e.width+1,height:e.height}:{x:t,y:e.y,width:e.x-t,height:e.height}}function lo(e,t,n,r,i,o,a,l){return e<=i+a&&i<=e+n&&t<=o+l&&o<=t+r}function Xr(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function Td(e,t){const n=Math.min(e.x,t.x),r=Math.min(e.y,t.y),i=Math.max(e.x+e.width,t.x+t.width)-n,o=Math.max(e.y+e.height,t.y+t.height)-r;return{x:n,y:r,width:i,height:o}}function Np(e,t){return e.x<=t.x&&e.y<=t.y&&e.x+e.width>=t.x+t.width&&e.y+e.height>=t.y+t.height}function $p(e,t,n,r){if(e.x>t||e.y>n||e.x<0&&e.y<0&&e.x+e.width>t&&e.y+e.height>n)return;if(e.x>=0&&e.y>=0&&e.x+e.width<=t&&e.y+e.height<=n)return e;const i=-4,o=-4,a=t+4,l=n+4,s=i-e.x,u=e.x+e.width-a,c=o-e.y,f=e.y+e.height-l,g=s>0?e.x+Math.floor(s/r)*r:e.x,h=u>0?e.x+e.width-Math.floor(u/r)*r:e.x+e.width,m=c>0?e.y+Math.floor(c/r)*r:e.y,p=f>0?e.y+e.height-Math.floor(f/r)*r:e.y+e.height;return{x:g,y:m,width:h-g,height:p-m}}function Bp(e,t,n,r,i){const[o,a,l,s]=t,[u,c,f,g]=i,{x:h,y:m,width:p,height:v}=e,w=[];if(p<=0||v<=0)return w;const b=h+p,M=m+v,O=h<o,S=m<a,R=h+p>l,L=m+v>s,E=h>=o&&h<l||b>o&&b<=l||h<o&&b>l,x=m>=a&&m<s||M>a&&M<=s||m<a&&M>s;if(E&&x){const D=Math.max(h,o),C=Math.max(m,a),I=Math.min(b,l),T=Math.min(M,s);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:c,width:f-u+1,height:g-c+1}})}if(O&&S){const D=h,C=m,I=Math.min(b,o),T=Math.min(M,a);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:0,width:u+1,height:c+1}})}if(S&&E){const D=Math.max(h,o),C=m,I=Math.min(b,l),T=Math.min(M,a);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:0,width:f-u+1,height:c+1}})}if(S&&R){const D=Math.max(h,l),C=m,I=b,T=Math.min(M,a);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:f,y:0,width:n-f+1,height:c+1}})}if(O&&x){const D=h,C=Math.max(m,a),I=Math.min(b,o),T=Math.min(M,s);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:c,width:u+1,height:g-c+1}})}if(R&&x){const D=Math.max(h,l),C=Math.max(m,a),I=b,T=Math.min(M,s);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:f,y:c,width:n-f+1,height:g-c+1}})}if(O&&L){const D=h,C=Math.max(m,s),I=Math.min(b,o),T=M;w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:g,width:u+1,height:r-g+1}})}if(L&&E){const D=Math.max(h,o),C=Math.max(m,s),I=Math.min(b,l),T=M;w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:g,width:f-u+1,height:r-g+1}})}if(R&&L){const D=Math.max(h,l),C=Math.max(m,s),I=b,T=M;w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:f,y:g,width:n-f+1,height:r-g+1}})}return w}const Wp={kind:te.Loading,allowOverlay:!1};function Cu(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v,w,b,M,O,S,R,L,E,x,_,D,C,I,T,k,z,$,X){let re=(M==null?void 0:M.size)??Number.MAX_SAFE_INTEGER;const j=performance.now();let G=I.baseFontFull;e.font=G;const ae={ctx:e},ie=[0,0],le=v>0?Jr(s,v,u):0;let fe,Q;const H=Rd(b);return Fr(t,l,o,a,i,(P,W,ce,De,He)=>{const ye=Math.max(0,De-W),Re=W+ye,Se=i+1,Et=P.width-ye,wt=r-i-1;if(b.length>0){let ze=!1;for(let Pe=0;Pe<b.length;Pe++){const tt=b[Pe];if(lo(Re,Se,Et,wt,tt.x,tt.y,tt.width,tt.height)){ze=!0;break}}if(!ze)return}const rt=()=>{e.save(),e.beginPath(),e.rect(Re,Se,Et,wt),e.clip()},se=O.columns.hasIndex(P.sourceIndex),et=f(P.group??"").overrideTheme,me=P.themeOverride===void 0&&et===void 0?I:vr(I,et,P.themeOverride),he=me.baseFontFull;he!==G&&(G=he,e.font=he),rt();let pe;return ka(He,ce,r,s,u,v,w,H,(ze,Pe,tt,ve,ue)=>{var fn,hn,pn;if(Pe<0||(ie[0]=P.sourceIndex,ie[1]=Pe,M!==void 0&&!M.has(ie)))return;if(b.length>0){let at=!1;for(let Xt=0;Xt<b.length;Xt++){const Ot=b[Xt];if(lo(W,ze,P.width,tt,Ot.x,Ot.y,Ot.width,Ot.height)){at=!0;break}}if(!at)return}const it=O.rows.hasIndex(Pe),Ae=h.hasIndex(Pe),Qe=Pe<s?c(ie):Wp;let vt=W,ht=P.width,Ve=!1,Dt=!1;if(Qe.span!==void 0){const[at,Xt]=Qe.span,Ot=`${Pe},${at},${Xt},${P.sticky}`;if(Q===void 0&&(Q=new Set),Q.has(Ot)){re--;return}else{const rn=Id(Qe.span,W,ze,P.width,tt,P,n),Tt=P.sticky?rn[0]:rn[1];if(!P.sticky&&rn[0]!==void 0&&(Dt=!0),Tt!==void 0){vt=Tt.x,ht=Tt.width,Q.add(Ot),e.restore(),pe=void 0,e.save(),e.beginPath();const Ft=Math.max(0,De-Tt.x);e.rect(Tt.x+Ft,ze,Tt.width-Ft,tt),fe===void 0&&(fe=[]),fe.push({x:Tt.x+Ft,y:ze,width:Tt.width-Ft,height:tt}),e.clip(),Ve=!0}}}const Yt=g==null?void 0:g(Pe),xt=ue&&((fn=P.trailingRowOptions)==null?void 0:fn.themeOverride)!==void 0?(hn=P.trailingRowOptions)==null?void 0:hn.themeOverride:void 0,Lt=Qe.themeOverride===void 0&&Yt===void 0&&xt===void 0?me:vr(me,Yt,xt,Qe.themeOverride);e.beginPath();const nn=Fm(ie,Qe,O);let zt=Am(ie,Qe,O,p);const ln=Qe.span!==void 0&&O.columns.some(at=>Qe.span!==void 0&&at>=Qe.span[0]&&at<=Qe.span[1]);nn&&!m&&p?zt=0:nn&&p&&(zt=Math.max(zt,1)),ln&&zt++,nn||(it&&zt++,se&&!ue&&zt++);const It=Qe.kind===te.Protected?Lt.bgCellMedium:Lt.bgCell;let yt;if((ve||It!==I.bgCell)&&(yt=An(It,yt)),zt>0||Ae){Ae&&(yt=An(Lt.bgHeader,yt));for(let at=0;at<zt;at++)yt=An(Lt.accentLight,yt)}else if(S!==void 0){for(const at of S)if(at[0]===P.sourceIndex&&at[1]===Pe){yt=An(Lt.bgSearchResult,yt);break}}if(R!==void 0)for(let at=0;at<R.length;at++){const Xt=R[at],Ot=Xt.range;Xt.style!=="solid-outline"&&Ot.x<=P.sourceIndex&&P.sourceIndex<Ot.x+Ot.width&&Ot.y<=Pe&&Pe<Ot.y+Ot.height&&(yt=An(Xt.color,yt))}let Dn=!1;if(M!==void 0){const at=ze+1,Ot=(ve?at+tt-1:Math.min(at+tt-1,r-le))-at;(Ot!==tt-1||vt+1<=De)&&(Dn=!0,e.save(),e.beginPath(),e.rect(vt+1,at,ht-1,Ot),e.clip()),yt=yt===void 0?Lt.bgCell:An(yt,Lt.bgCell)}const yn=P.sourceIndex===n.length-1,Oe=Pe===s-1;yt!==void 0&&(e.fillStyle=yt,pe!==void 0&&(pe.fillStyle=yt),M!==void 0?e.fillRect(vt+1,ze+1,ht-(yn?2:1),tt-(Oe?2:1)):e.fillRect(vt,ze,ht,tt)),Qe.style==="faded"&&(e.globalAlpha=.6);let _t;for(let at=0;at<x.length;at++){const Xt=x[at];if(Xt.item[0]===P.sourceIndex&&Xt.item[1]===Pe){_t=Xt;break}}if(ht>X&&!Dt){const at=Lt.baseFontFull;at!==G&&(e.font=at,G=at),pe=Dd(e,Qe,P.sourceIndex,Pe,yn,Oe,vt,ze,ht,tt,zt>0,Lt,yt??Lt.bgCell,L,E,(_t==null?void 0:_t.hoverAmount)??0,_,C,j,D,pe,T,k,z,$)}return Dn&&e.restore(),Qe.style==="faded"&&(e.globalAlpha=1),re--,Ve&&(e.restore(),(pn=pe==null?void 0:pe.deprep)==null||pn.call(pe,ae),pe=void 0,rt(),G=he,e.font=he),re<=0}),e.restore(),re<=0}),fe}const Yi=[0,0],Xi={x:0,y:0,width:0,height:0},ss=[void 0,()=>{}];let Is=!1;function Up(){Is=!0}function Dd(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v,w,b,M,O,S,R,L,E){var k,z;let x,_;v!==void 0&&v[0][0]===n&&v[0][1]===r&&(x=v[1][0],_=v[1][1]);let D;Yi[0]=n,Yi[1]=r,Xi.x=a,Xi.y=l,Xi.width=s,Xi.height=u,ss[0]=R.getValue(Yi),ss[1]=$=>R.setValue(Yi,$),Is=!1;const C={ctx:e,theme:f,col:n,row:r,cell:t,rect:Xi,highlighted:c,cellFillColor:g,hoverAmount:p,frameTime:b,hoverX:x,drawState:ss,hoverY:_,imageLoader:h,spriteManager:m,hyperWrapping:w,overrideCursor:x!==void 0?E:void 0,requestAnimationFrame:Up},I=Bm(C,t.lastUpdated,b,O,i,o),T=L(t);if(T!==void 0){(O==null?void 0:O.renderer)!==T&&((k=O==null?void 0:O.deprep)==null||k.call(O,C),O=void 0);const $=(z=T.drawPrep)==null?void 0:z.call(T,C,O);M!==void 0&&!vi(C.cell)?M(C,()=>T.draw(C,t)):T.draw(C,t),D=$===void 0?void 0:{deprep:$==null?void 0:$.deprep,fillStyle:$==null?void 0:$.fillStyle,font:$==null?void 0:$.font,renderer:T}}return(I||Is)&&(S==null||S(Yi)),D}function Gs(e,t,n,r,i,o,a,l,s=-20,u=-20,c=32,f="center",g="square"){const h=Math.floor(i+a/2),m=g==="circle"?1e4:t.roundingRadius??4;let p=nd(c,a,t.cellVerticalPadding),v=p/2;const w=td(f,r,o,t.cellHorizontalPadding,p),b=ed(w,h,p),M=rd(r+s,i+u,b);switch(n){case!0:{e.beginPath(),cr(e,w-p/2,h-p/2,p,p,m),g==="circle"&&(v*=.8,p*=.8),e.fillStyle=l?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(w-v+p/4.23,h-v+p/1.97),e.lineTo(w-v+p/2.42,h-v+p/1.44),e.lineTo(w-v+p/1.29,h-v+p/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break}case ia:case!1:{e.beginPath(),cr(e,w-p/2+.5,h-p/2+.5,p-1,p-1,m),e.lineWidth=1,e.strokeStyle=M?t.textDark:t.textMedium,e.stroke();break}case zs:{e.beginPath(),cr(e,w-p/2,h-p/2,p,p,m),e.fillStyle=M?t.textMedium:t.textLight,e.fill(),g==="circle"&&(v*=.8,p*=.8),e.beginPath(),e.moveTo(w-p/3,h),e.lineTo(w+p/3,h),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break}default:ao()}}function qp(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v,w,b){var x,_,D,C;const M=a+l;if(M<=0)return;e.fillStyle=f.bgHeader,e.fillRect(0,0,i,M);const O=(x=r==null?void 0:r[0])==null?void 0:x[0],S=(_=r==null?void 0:r[0])==null?void 0:_[1],R=(D=r==null?void 0:r[1])==null?void 0:D[0],L=(C=r==null?void 0:r[1])==null?void 0:C[1],E=f.headerFontFull;e.font=E,Fr(t,0,o,0,M,(I,T,k,z)=>{var P;if(v!==void 0&&!v.has([I.sourceIndex,-1]))return;const $=Math.max(0,z-T);e.save(),e.beginPath(),e.rect(T+$,l,I.width-$,a),e.clip();const X=p(I.group??"").overrideTheme,re=I.themeOverride===void 0&&X===void 0?f:vr(f,X,I.themeOverride);re.bgHeader!==f.bgHeader&&(e.fillStyle=re.bgHeader,e.fill()),re!==f&&(e.font=re.baseFontFull);const j=c.columns.hasIndex(I.sourceIndex),G=s!==void 0||u,ae=!G&&S===-1&&O===I.sourceIndex,ie=G?0:((P=h.find(W=>W.item[0]===I.sourceIndex&&W.item[1]===-1))==null?void 0:P.hoverAmount)??0,le=(c==null?void 0:c.current)!==void 0&&c.current.cell[0]===I.sourceIndex,fe=j?re.accentColor:le?re.bgHeaderHasFocus:re.bgHeader,Q=n?l:0,H=I.sourceIndex===0?0:1;j?(e.fillStyle=fe,e.fillRect(T+H,Q,I.width-H,a)):(le||ie>0)&&(e.beginPath(),e.rect(T+H,Q,I.width-H,a),le&&(e.fillStyle=re.bgHeaderHasFocus,e.fill()),ie>0&&(e.globalAlpha=ie,e.fillStyle=re.bgHeaderHovered,e.fill(),e.globalAlpha=1)),Ld(e,T,Q,I.width,a,I,j,re,ae,ae?R:void 0,ae?L:void 0,le,ie,g,w,b),e.restore()}),n&&Yp(e,t,i,o,l,r,f,g,h,m,p,v)}function Yp(e,t,n,r,i,o,a,l,s,u,c,f){const[h,m]=(o==null?void 0:o[0])??[];let p=0;Ed(t,n,r,i,(v,w,b,M,O,S)=>{if(f!==void 0&&!f.hasItemInRectangle({x:v[0],y:-2,width:v[1]-v[0]+1,height:1}))return;e.save(),e.beginPath(),e.rect(b,M,O,S),e.clip();const R=c(w),L=(R==null?void 0:R.overrideTheme)===void 0?a:vr(a,R.overrideTheme),E=m===-2&&h!==void 0&&h>=v[0]&&h<=v[1],x=E?L.bgHeaderHovered:L.bgHeader;if(x!==a.bgHeader&&(e.fillStyle=x,e.fill()),e.fillStyle=L.textGroupHeader??L.textHeader,R!==void 0){let _=b;if(R.icon!==void 0&&(l.drawSprite(R.icon,"normal",e,_+8,(i-20)/2,20,L),_+=26),e.fillText(R.name,_+8,i/2+dr(e,a.headerFontFull)),R.actions!==void 0&&E){const D=Od({x:b,y:M,width:O,height:S},R.actions);e.beginPath();const C=D[0].x-10,I=b+O-C;e.rect(C,0,I,i);const T=e.createLinearGradient(C,0,C+I,0),k=jr(x,0);T.addColorStop(0,k),T.addColorStop(10/I,x),T.addColorStop(1,x),e.fillStyle=T,e.fill(),e.globalAlpha=.6;const[z,$]=(o==null?void 0:o[1])??[-1,-1];for(let X=0;X<R.actions.length;X++){const re=R.actions[X],j=D[X],G=Xr(j,z+b,$);G&&(e.globalAlpha=1),l.drawSprite(re.icon,"normal",e,j.x+j.width/2-10,j.y+j.height/2-10,20,L),G&&(e.globalAlpha=.6)}e.globalAlpha=1}}b!==0&&u(v[0])&&(e.beginPath(),e.moveTo(b+.5,0),e.lineTo(b+.5,i),e.strokeStyle=a.borderColor,e.lineWidth=1,e.stroke()),e.restore(),p=b+O}),e.beginPath(),e.moveTo(p+.5,0),e.lineTo(p+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=a.borderColor,e.lineWidth=1,e.stroke()}const Bo=30;function Xp(e,t,n,r,i){return{x:e+n-Bo,y:Math.max(t,t+r/2-Bo/2),width:Bo,height:Math.min(Bo,r)}}function Od(e,t){const n=[];let r=e.x+e.width-26*t.length;const i=e.y+e.height/2-13,o=26,a=26;for(let l=0;l<t.length;l++)n.push({x:r,y:i,width:a,height:o}),r+=26;return n}function ji(e,t,n){return!n||e===void 0||(e.x=t-(e.x-t)-e.width),e}function Pd(e,t,n,r,i,o,a,l){var w;const s=a.cellHorizontalPadding,u=a.headerIconSize,c=Xp(n,r,i,o);let f=n+s;const g=t.icon===void 0?void 0:{x:f,y:r+(o-u)/2,width:u,height:u},h=g===void 0||t.overlayIcon===void 0?void 0:{x:g.x+9,y:g.y+6,width:18,height:18};g!==void 0&&(f+=Math.ceil(u*1.3));const m={x:f,y:r,width:i-f,height:o};let p;if(t.indicatorIcon!==void 0){const b=e===void 0?((w=pd(t.title,a.headerFontFull))==null?void 0:w.width)??0:Qr(t.title,e,a.headerFontFull).width;m.width=b,f+=b+s,p={x:f,y:r+(o-u)/2,width:u,height:u}}const v=n+i/2;return{menuBounds:ji(c,v,l),iconBounds:ji(g,v,l),iconOverlayBounds:ji(h,v,l),textBounds:ji(m,v,l),indicatorIconBounds:ji(p,v,l)}}function Su(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p){if(o.rowMarker!==void 0&&o.headerRowMarkerDisabled!==!0){const b=o.rowMarkerChecked;b!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=f);const M=o.headerRowMarkerTheme!==void 0?vr(l,o.headerRowMarkerTheme):l;Gs(e,M,b,t,n,r,i,!1,void 0,void 0,18,"center",o.rowMarker),b!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=1);return}const v=a?l.textHeaderSelected:l.textHeader,w=o.hasMenu===!0&&(s||h&&a)&&p.menuBounds!==void 0;if(o.icon!==void 0&&p.iconBounds!==void 0){let b=a?"selected":"normal";o.style==="highlight"&&(b=a?"selected":"special"),g.drawSprite(o.icon,b,e,p.iconBounds.x,p.iconBounds.y,p.iconBounds.width,l),o.overlayIcon!==void 0&&p.iconOverlayBounds!==void 0&&g.drawSprite(o.overlayIcon,a?"selected":"special",e,p.iconOverlayBounds.x,p.iconOverlayBounds.y,p.iconOverlayBounds.width,l)}if(w&&r>35){const M=m?35:r-35,O=m?35*.7:r-35*.7,S=M/r,R=O/r,L=e.createLinearGradient(t,0,t+r,0),E=jr(v,0);L.addColorStop(m?1:0,v),L.addColorStop(S,v),L.addColorStop(R,E),L.addColorStop(m?0:1,E),e.fillStyle=L}else e.fillStyle=v;if(m&&(e.textAlign="right"),p.textBounds!==void 0&&e.fillText(o.title,m?p.textBounds.x+p.textBounds.width:p.textBounds.x,n+i/2+dr(e,l.headerFontFull)),m&&(e.textAlign="left"),o.indicatorIcon!==void 0&&p.indicatorIconBounds!==void 0&&(!w||!lo(p.menuBounds.x,p.menuBounds.y,p.menuBounds.width,p.menuBounds.height,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,p.indicatorIconBounds.height))){let b=a?"selected":"normal";o.style==="highlight"&&(b=a?"selected":"special"),g.drawSprite(o.indicatorIcon,b,e,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,l)}if(w&&p.menuBounds!==void 0){const b=p.menuBounds,M=u!==void 0&&c!==void 0&&Xr(b,u+t,c+n);if(M||(e.globalAlpha=.7),o.menuIcon===void 0||o.menuIcon===oa.Triangle){e.beginPath();const O=b.x+b.width/2-5.5,S=b.y+b.height/2-3;Ym(e,[{x:O,y:S},{x:O+11,y:S},{x:O+5.5,y:S+6}],1),e.fillStyle=v,e.fill()}else if(o.menuIcon===oa.Dots){e.beginPath();const O=b.x+b.width/2,S=b.y+b.height/2;qm(e,O,S),e.fillStyle=v,e.fill()}else{const O=b.x+(b.width-l.headerIconSize)/2,S=b.y+(b.height-l.headerIconSize)/2;g.drawSprite(o.menuIcon,"normal",e,O,S,l.headerIconSize,l)}M||(e.globalAlpha=1)}}function Ld(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p){const v=Bs(o.title)==="rtl",w=Pd(e,o,t,n,r,i,l,v);m!==void 0?m({ctx:e,theme:l,rect:{x:t,y:n,width:r,height:i},column:o,columnIndex:o.sourceIndex,isSelected:a,hoverAmount:g,isHovered:s,hasSelectedCell:f,spriteManager:h,menuBounds:(w==null?void 0:w.menuBounds)??{x:0,y:0,height:0,width:0}},()=>Su(e,t,n,r,i,o,a,l,s,u,c,g,h,p,v,w)):Su(e,t,n,r,i,o,a,l,s,u,c,g,h,p,v,w)}function jp(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v,w,b){if(w!==void 0||t[t.length-1]!==n[t.length-1])return;const M=Rd(v);Fr(t,s,a,l,o,(O,S,R,L,E)=>{if(O!==t[t.length-1])return;S+=O.width;const x=Math.max(S,L);x>r||(e.save(),e.beginPath(),e.rect(x,o+1,1e4,i-o-1),e.clip(),ka(E,R,i,u,c,m,p,M,(_,D,C,I)=>{if(!I&&v.length>0&&!v.some(X=>lo(S,_,1e4,C,X.x,X.y,X.width,X.height)))return;const T=g.hasIndex(D),k=h.hasIndex(D);e.beginPath();const z=f==null?void 0:f(D),$=z===void 0?b:vr(b,z);$.bgCell!==b.bgCell&&(e.fillStyle=$.bgCell,e.fillRect(S,_,1e4,C)),k&&(e.fillStyle=$.bgHeader,e.fillRect(S,_,1e4,C)),T&&(e.fillStyle=$.accentLight,e.fillRect(S,_,1e4,C))}),e.restore())})}function Gp(e,t,n,r,i,o,a,l,s){let u=!1;for(const m of t)if(!m.sticky){u=a(m.sourceIndex);break}const c=s.horizontalBorderColor??s.borderColor,f=s.borderColor,g=u?Si(t):0;let h;if(g!==0&&(h=cu(f,s.bgCell),e.beginPath(),e.moveTo(g+.5,0),e.lineTo(g+.5,r),e.strokeStyle=h,e.stroke()),i>0){const m=f===c&&h!==void 0?h:cu(c,s.bgCell),p=Jr(o,i,l);e.beginPath(),e.moveTo(0,r-p+.5),e.lineTo(n,r-p+.5),e.strokeStyle=m,e.stroke()}}const _d=(e,t,n)=>{let r=0,i=t,o=0,a=n;if(e!==void 0&&e.length>0){r=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,a=Number.MIN_SAFE_INTEGER;for(const l of e)r=Math.min(r,l.x-1),i=Math.max(i,l.x+l.width+1),o=Math.min(o,l.y-1),a=Math.max(a,l.y+l.height+1)}return{minX:r,maxX:i,minY:o,maxY:a}};function Kp(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m){var C;const p=m.bgCell,{minX:v,maxX:w,minY:b,maxY:M}=_d(l,o,a),O=[],S=a-Jr(h,g,u);let R=s,L=n,E=0;for(;R+i<S;){const I=R+i,T=u(L);if(I>=b&&I<=M-1){const k=c==null?void 0:c(L),z=k==null?void 0:k.bgCell;z!==void 0&&z!==p&&L>=h-g&&O.push({x:v,y:I,w:w-v,h:T,color:z})}R+=T,L<h-g&&(E=R),L++}let x=0;const _=Math.min(S,M)-E;if(_>0)for(let I=0;I<t.length;I++){const T=t[I];if(T.width===0)continue;const k=T.sticky?x:x+r,z=(C=T.themeOverride)==null?void 0:C.bgCell;z!==void 0&&z!==p&&k>=v&&k<=w&&f(I+1)&&O.push({x:k,y:E,w:T.width,h:_,color:z}),x+=T.width}if(O.length===0)return;let D;e.beginPath();for(let I=O.length-1;I>=0;I--){const T=O[I];D===void 0?D=T.color:T.color!==D&&(e.fillStyle=D,e.fill(),e.beginPath(),D=T.color),e.rect(T.x,T.y,T.w,T.h)}D!==void 0&&(e.fillStyle=D,e.fill()),e.beginPath()}function xu(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v,w=!1){if(s!==void 0){e.beginPath(),e.save(),e.rect(0,0,o,a);for(const C of s)e.rect(C.x+1,C.y+1,C.width-1,C.height-1);e.clip("evenodd")}const b=v.horizontalBorderColor??v.borderColor,M=v.borderColor,{minX:O,maxX:S,minY:R,maxY:L}=_d(l,o,a),E=[];e.beginPath();let x=.5;for(let C=0;C<t.length;C++){const I=t[C];if(I.width===0)continue;x+=I.width;const T=I.sticky?x:x+r;T>=O&&T<=S&&h(C+1)&&E.push({x1:T,y1:Math.max(u,R),x2:T,y2:Math.min(a,L),color:M})}let _=a+.5;for(let C=p-m;C<p;C++){const I=f(C);_-=I,E.push({x1:O,y1:_,x2:S,y2:_,color:b})}if(w!==!0){let C=c+.5,I=n;const T=_;for(;C+i<T;){const k=C+i;if(k>=R&&k<=L-1){const z=g==null?void 0:g(I);E.push({x1:O,y1:k,x2:S,y2:k,color:(z==null?void 0:z.horizontalBorderColor)??(z==null?void 0:z.borderColor)??b})}C+=f(I),I++}}const D=xh(E,C=>C.color);for(const C of Object.keys(D)){e.strokeStyle=C;for(const I of D[C])e.moveTo(I.x1,I.y1),e.lineTo(I.x2,I.y2);e.stroke(),e.beginPath()}s!==void 0&&e.restore()}function Zp(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v,w,b){const M=[];e.imageSmoothingEnabled=!1;const O=Math.min(i.cellYOffset,a),S=Math.max(i.cellYOffset,a);let R=0;if(typeof w=="number")R+=(S-O)*w;else for(let T=O;T<S;T++)R+=w(T);a>i.cellYOffset&&(R=-R),R+=s-i.translateY;const L=Math.min(i.cellXOffset,o),E=Math.max(i.cellXOffset,o);let x=0;for(let T=L;T<E;T++)x+=p[T].width;o>i.cellXOffset&&(x=-x),x+=l-i.translateX;const _=Si(v);if(x!==0&&R!==0)return{regions:[]};const D=u>0?Jr(g,u,w):0,C=c-_-Math.abs(x),I=f-h-D-Math.abs(R)-1;if(C>150&&I>150){const T={sx:0,sy:0,sw:c*m,sh:f*m,dx:0,dy:0,dw:c*m,dh:f*m};if(R>0?(T.sy=(h+1)*m,T.sh=I*m,T.dy=(R+h+1)*m,T.dh=I*m,M.push({x:0,y:h,width:c,height:R+1})):R<0&&(T.sy=(-R+h+1)*m,T.sh=I*m,T.dy=(h+1)*m,T.dh=I*m,M.push({x:0,y:f+R-D,width:c,height:-R+D})),x>0?(T.sx=_*m,T.sw=C*m,T.dx=(x+_)*m,T.dw=C*m,M.push({x:_-1,y:0,width:x+2,height:f})):x<0&&(T.sx=(_-x)*m,T.sw=C*m,T.dx=_*m,T.dw=C*m,M.push({x:c+x,y:0,width:-x,height:f})),e.setTransform(1,0,0,1,0,0),b){if(_>0&&x!==0&&R===0&&(r===void 0||(n==null?void 0:n[1])!==!1)){const k=_*m,z=f*m;e.drawImage(t,0,0,k,z,0,0,k,z)}if(D>0&&x===0&&R!==0&&(r===void 0||(n==null?void 0:n[0])!==!1)){const k=(f-D)*m,z=c*m,$=D*m;e.drawImage(t,0,k,z,$,0,k,z,$)}}e.drawImage(t,T.sx,T.sy,T.sw,T.sh,T.dx,T.dy,T.dw,T.dh),e.scale(m,m)}return e.imageSmoothingEnabled=!0,{regions:M}}function Jp(e,t,n,r,i,o,a,l,s,u){const c=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||i!==e.translateY||Fr(s,n,r,i,l,(f,g,h,m)=>{if(f.sourceIndex===u){const p=Math.max(g,m)+1;return c.push({x:p,y:0,width:o-p,height:a}),!0}}),c}function Qp(e,t){if(t===void 0||e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.freezeColumns!==t.freezeColumns||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.maxScaleFactor!==t.maxScaleFactor)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let r=0;r<e.mappedColumns.length;r++){const i=e.mappedColumns[r],o=t.mappedColumns[r];if(Ci(i,o))continue;if(n!==void 0||i.width===o.width)return!1;const{width:a,...l}=i,{width:s,...u}=o;if(!Ci(l,u))return!1;n=r}return n===void 0?!0:n}return!0}function ku(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p){const v=m==null?void 0:m.filter(L=>L.style!=="no-outline");if(v===void 0||v.length===0)return;const w=Si(l),b=Jr(h,g,f),M=[s,0,l.length,h-g],O=[w,0,t,n-b],S=v.map(L=>{const E=L.range,x=L.style??"dashed";return Bp(E,M,t,n,O).map(_=>{const D=_.rect,C=Rs(D.x,D.y,t,n,c,u+c,r,i,o,a,h,s,g,l,f),I=D.width===1&&D.height===1?C:Rs(D.x+D.width-1,D.y+D.height-1,t,n,c,u+c,r,i,o,a,h,s,g,l,f);return D.x+D.width>=l.length&&(I.width-=1),D.y+D.height>=h&&(I.height-=1),{color:L.color,style:x,clip:_.clip,rect:$p({x:C.x,y:C.y,width:I.x+I.width-C.x,height:I.y+I.height-C.y},t,n,8)}})}),R=()=>{e.lineWidth=1;let L=!1;for(const E of S)for(const x of E)if((x==null?void 0:x.rect)!==void 0&&lo(0,0,t,n,x.rect.x,x.rect.y,x.rect.width,x.rect.height)){const _=L,D=!Np(x.clip,x.rect);D&&(e.save(),e.rect(x.clip.x,x.clip.y,x.clip.width,x.clip.height),e.clip()),x.style==="dashed"&&!L?(e.setLineDash([5,3]),L=!0):(x.style==="solid"||x.style==="solid-outline")&&L&&(e.setLineDash([]),L=!1),e.strokeStyle=x.style==="solid-outline"?An(An(x.color,p.borderColor),p.bgCell):jr(x.color,1),e.strokeRect(x.rect.x+.5,x.rect.y+.5,x.rect.width-1,x.rect.height-1),D&&(e.restore(),L=_)}L&&e.setLineDash([])};return R(),R}function Mu(e,t,n,r,i){e.beginPath(),e.moveTo(t,n),e.lineTo(t,r),e.lineWidth=2,e.strokeStyle=i,e.stroke(),e.globalAlpha=1}function ls(e,t,n,r,i,o,a,l,s,u,c,f,g,h,m,p,v){if(c.current===void 0)return;const w=c.current.range,b=c.current.cell,M=[w.x+w.width-1,w.y+w.height-1];if(b[1]>=v&&M[1]>=v||!a.some(T=>T.sourceIndex===b[0]||T.sourceIndex===M[0]))return;const[S,R]=c.current.cell,L=g(c.current.cell),E=L.span??[S,S],x=R>=v-h,_=h>0&&!x?Jr(v,h,f)-1:0,D=M[1];let C;if(Fr(a,r,i,o,u,(T,k,z,$,X)=>{if(T.sticky&&S>T.sourceIndex)return;const re=T.sourceIndex<E[0],j=T.sourceIndex>E[1],G=T.sourceIndex===M[0];if(!(!G&&(re||j)))return ka(X,z,n,v,f,h,m,void 0,(ae,ie,le)=>{if(ie!==R&&ie!==D)return;let fe=k,Q=T.width;if(L.span!==void 0){const P=Id(L.span,k,ae,T.width,le,T,l),W=T.sticky?P[0]:P[1];W!==void 0&&(fe=W.x,Q=W.width)}return ie===D&&G&&p&&(C=()=>{var P;$>fe&&!T.sticky&&(e.beginPath(),e.rect($,0,t-$,n),e.clip()),e.beginPath(),e.rect(fe+Q-4,ae+le-4,4,4),e.fillStyle=((P=T.themeOverride)==null?void 0:P.accentColor)??s.accentColor,e.fill()}),C!==void 0}),C!==void 0}),C===void 0)return;const I=()=>{e.save(),e.beginPath(),e.rect(0,u,t,n-u-_),e.clip(),C==null||C(),e.restore()};return I(),I}function e0(e,t,n,r,i,o,a,l,s){s===void 0||s.size===0||(e.beginPath(),Ed(t,n,o,r,(u,c,f,g,h,m)=>{s.hasItemInRectangle({x:u[0],y:-2,width:u[1]-u[0]+1,height:1})&&e.rect(f,g,h,m)}),Fr(t,l,o,a,i,(u,c,f,g)=>{const h=Math.max(0,g-c),m=c+h+1,p=u.width-h-1;s.has([u.sourceIndex,-1])&&e.rect(m,r,p,i-r)}),e.clip())}function t0(e,t,n,r,i,o,a,l,s,u){let c=0;return Fr(e,o,r,i,n,(f,g,h,m,p)=>(ka(p,h,t,a,l,s,u,void 0,(v,w,b,M)=>{M||(c=Math.max(w,c))}),!0)),c}function Ru(e,t){var yn;const{canvasCtx:n,headerCanvasCtx:r,width:i,height:o,cellXOffset:a,cellYOffset:l,translateX:s,translateY:u,mappedColumns:c,enableGroups:f,freezeColumns:g,dragAndDropState:h,theme:m,drawFocus:p,headerHeight:v,groupHeaderHeight:w,disabledRows:b,rowHeight:M,verticalBorder:O,overrideCursor:S,isResizing:R,selection:L,fillHandle:E,freezeTrailingRows:x,rows:_,getCellContent:D,getGroupDetails:C,getRowThemeOverride:I,isFocused:T,drawHeaderCallback:k,prelightCells:z,drawCellCallback:$,highlightRegions:X,resizeCol:re,imageLoader:j,lastBlitData:G,hoverValues:ae,hyperWrapping:ie,hoverInfo:le,spriteManager:fe,maxScaleFactor:Q,hasAppendRow:H,touchMode:P,enqueue:W,renderStateProvider:ce,getCellRenderer:De,renderStrategy:He,bufferACtx:ye,bufferBCtx:Re,damage:Se,minimumCellWidth:Et,resizeIndicator:wt}=e;if(i===0||o===0)return;const rt=He==="double-buffer",se=Math.min(Q,Math.ceil(window.devicePixelRatio??1)),et=He!=="direct"&&Qp(e,t),me=n.canvas;(me.width!==i*se||me.height!==o*se)&&(me.width=i*se,me.height=o*se,me.style.width=i+"px",me.style.height=o+"px");const he=r.canvas,pe=f?w+v:v,ze=pe+1;(he.width!==i*se||he.height!==ze*se)&&(he.width=i*se,he.height=ze*se,he.style.width=i+"px",he.style.height=ze+"px");const Pe=ye.canvas,tt=Re.canvas;rt&&(Pe.width!==i*se||Pe.height!==o*se)&&(Pe.width=i*se,Pe.height=o*se,G.current!==void 0&&(G.current.aBufferScroll=void 0)),rt&&(tt.width!==i*se||tt.height!==o*se)&&(tt.width=i*se,tt.height=o*se,G.current!==void 0&&(G.current.bBufferScroll=void 0));const ve=G.current;if(et===!0&&a===(ve==null?void 0:ve.cellXOffset)&&l===(ve==null?void 0:ve.cellYOffset)&&s===(ve==null?void 0:ve.translateX)&&u===(ve==null?void 0:ve.translateY))return;let ue=null;rt&&(ue=n);const it=r;let Ae;rt?Se!==void 0?Ae=(ve==null?void 0:ve.lastBuffer)==="b"?Re:ye:Ae=(ve==null?void 0:ve.lastBuffer)==="b"?ye:Re:Ae=n;const Qe=Ae.canvas,vt=rt?Qe===Pe?tt:Pe:me,ht=typeof M=="number"?()=>M:M;it.save(),Ae.save(),it.beginPath(),Ae.beginPath(),it.textBaseline="middle",Ae.textBaseline="middle",se!==1&&(it.scale(se,se),Ae.scale(se,se));const Ve=Ms(c,a,i,h,s);let Dt=[];const Yt=p&&((yn=L.current)==null?void 0:yn.cell[1])===l&&u===0;let xt=!1;if(X!==void 0){for(const Oe of X)if(Oe.style!=="no-outline"&&Oe.range.y===l&&u===0){xt=!0;break}}const Lt=()=>{qp(it,Ve,f,le,i,s,v,w,h,R,L,m,fe,ae,O,C,Se,k,P),xu(it,Ve,l,s,u,i,o,void 0,void 0,w,pe,ht,I,O,x,_,m,!0),it.beginPath(),it.moveTo(0,ze-.5),it.lineTo(i,ze-.5),it.strokeStyle=An(m.headerBottomBorderColor??m.horizontalBorderColor??m.borderColor,m.bgHeader),it.stroke(),xt&&ku(it,i,o,a,l,s,u,c,g,v,w,M,x,_,X,m),Yt&&ls(it,i,o,l,s,u,Ve,c,m,pe,L,ht,D,x,H,E,_)};if(Se!==void 0){const Oe=Ve[Ve.length-1].sourceIndex+1,_t=Se.hasItemInRegion([{x:a,y:-2,width:Oe,height:2},{x:a,y:l,width:Oe,height:300},{x:0,y:l,width:g,height:300},{x:0,y:-2,width:g,height:2},{x:a,y:_-x,width:Oe,height:x,when:x>0}]),fn=hn=>{Cu(hn,Ve,c,o,pe,s,u,l,_,ht,D,C,I,b,T,p,x,H,Dt,Se,L,z,X,j,fe,ae,le,$,ie,m,W,ce,De,S,Et);const pn=L.current;E&&p&&pn!==void 0&&Se.has(hd(pn.range))&&ls(hn,i,o,l,s,u,Ve,c,m,pe,L,ht,D,x,H,E,_)};_t&&(fn(Ae),ue!==null&&(ue.save(),ue.scale(se,se),ue.textBaseline="middle",fn(ue),ue.restore()),Se.hasHeader()&&(e0(it,Ve,i,w,pe,s,u,l,Se),Lt())),Ae.restore(),it.restore();return}if((et!==!0||a!==(ve==null?void 0:ve.cellXOffset)||s!==(ve==null?void 0:ve.translateX)||Yt!==(ve==null?void 0:ve.mustDrawFocusOnHeader)||xt!==(ve==null?void 0:ve.mustDrawHighlightRingsOnHeader))&&Lt(),et===!0){Fn(vt!==void 0&&ve!==void 0);const{regions:Oe}=Zp(Ae,vt,vt===Pe?ve.aBufferScroll:ve.bBufferScroll,vt===Pe?ve.bBufferScroll:ve.aBufferScroll,ve,a,l,s,u,x,i,o,_,pe,se,c,Ve,M,rt);Dt=Oe}else et!==!1&&(Fn(ve!==void 0),Dt=Jp(ve,a,l,s,u,i,o,pe,Ve,et));Gp(Ae,Ve,i,o,x,_,O,ht,m);const nn=ku(Ae,i,o,a,l,s,u,c,g,v,w,M,x,_,X,m),zt=p?ls(Ae,i,o,l,s,u,Ve,c,m,pe,L,ht,D,x,H,E,_):void 0;if(Ae.fillStyle=m.bgCell,Dt.length>0){Ae.beginPath();for(const Oe of Dt)Ae.rect(Oe.x,Oe.y,Oe.width,Oe.height);Ae.clip(),Ae.fill(),Ae.beginPath()}else Ae.fillRect(0,0,i,o);const ln=Cu(Ae,Ve,c,o,pe,s,u,l,_,ht,D,C,I,b,T,p,x,H,Dt,Se,L,z,X,j,fe,ae,le,$,ie,m,W,ce,De,S,Et);jp(Ae,Ve,c,i,o,pe,s,u,l,_,ht,I,L.rows,b,x,H,Dt,Se,m),Kp(Ae,Ve,l,s,u,i,o,Dt,pe,ht,I,O,x,_,m),xu(Ae,Ve,l,s,u,i,o,Dt,ln,w,pe,ht,I,O,x,_,m),nn==null||nn(),zt==null||zt(),R&&wt!=="none"&&Fr(Ve,0,s,0,pe,(Oe,_t)=>Oe.sourceIndex===re?(Mu(it,_t+Oe.width,0,pe+1,An(m.resizeIndicatorColor??m.accentLight,m.bgHeader)),wt==="full"&&Mu(Ae,_t+Oe.width,pe,o,An(m.resizeIndicatorColor??m.accentLight,m.bgCell)),!0):!1),ue!==null&&(ue.fillStyle=m.bgCell,ue.fillRect(0,0,i,o),ue.drawImage(Ae.canvas,0,0));const It=t0(Ve,o,pe,s,u,l,_,ht,x,H);j==null||j.setWindow({x:a,y:l,width:Ve.length,height:It-l},g,Array.from({length:x},(Oe,_t)=>_-1-_t));const yt=ve!==void 0&&(a!==ve.cellXOffset||s!==ve.translateX),Dn=ve!==void 0&&(l!==ve.cellYOffset||u!==ve.translateY);G.current={cellXOffset:a,cellYOffset:l,translateX:s,translateY:u,mustDrawFocusOnHeader:Yt,mustDrawHighlightRingsOnHeader:xt,lastBuffer:rt?Qe===Pe?"a":"b":void 0,aBufferScroll:Qe===Pe?[yt,Dn]:ve==null?void 0:ve.aBufferScroll,bBufferScroll:Qe===tt?[yt,Dn]:ve==null?void 0:ve.bBufferScroll},Ae.restore(),it.restore()}const n0=80;function r0(e){const t=e-1;return t*t*t+1}class i0{constructor(t){dt(this,"callback");dt(this,"currentHoveredItem");dt(this,"leavingItems",[]);dt(this,"lastAnimationTime");dt(this,"addToLeavingItems",t=>{this.leavingItems.some(r=>no(r.item,t.item))||this.leavingItems.push(t)});dt(this,"removeFromLeavingItems",t=>{const n=this.leavingItems.find(r=>no(r.item,t));return this.leavingItems=this.leavingItems.filter(r=>r!==n),(n==null?void 0:n.hoverAmount)??0});dt(this,"cleanUpLeavingElements",()=>{this.leavingItems=this.leavingItems.filter(t=>t.hoverAmount>0)});dt(this,"shouldStep",()=>{const t=this.leavingItems.length>0,n=this.currentHoveredItem!==void 0&&this.currentHoveredItem.hoverAmount<1;return t||n});dt(this,"getAnimatingItems",()=>this.currentHoveredItem!==void 0?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map(t=>({...t,hoverAmount:r0(t.hoverAmount)})));dt(this,"step",t=>{if(this.lastAnimationTime===void 0)this.lastAnimationTime=t;else{const r=(t-this.lastAnimationTime)/n0;for(const o of this.leavingItems)o.hoverAmount=Un(o.hoverAmount-r,0,1);this.currentHoveredItem!==void 0&&(this.currentHoveredItem.hoverAmount=Un(this.currentHoveredItem.hoverAmount+r,0,1));const i=this.getAnimatingItems();this.callback(i),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=t,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0});dt(this,"setHovered",t=>{var n;if(!no((n=this.currentHoveredItem)==null?void 0:n.item,t)){if(this.currentHoveredItem!==void 0&&this.addToLeavingItems(this.currentHoveredItem),t!==void 0){const r=this.removeFromLeavingItems(t);this.currentHoveredItem={item:t,hoverAmount:r}}else this.currentHoveredItem=void 0;this.lastAnimationTime===void 0&&window.requestAnimationFrame(this.step)}});this.callback=t}}class o0{constructor(t){dt(this,"fn");dt(this,"val");this.fn=t}get value(){return this.val??(this.val=this.fn())}}function Ks(e){return new o0(e)}const a0=Ks(()=>window.navigator.userAgent.includes("Firefox")),ua=Ks(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome")),ca=Ks(()=>window.navigator.platform.toLowerCase().startsWith("mac"));function s0(e){const t=d.useRef([]),n=d.useRef(0),r=d.useRef(e);r.current=e;const i=d.useCallback(()=>{const o=()=>window.requestAnimationFrame(a),a=()=>{const l=t.current.map(js);t.current=[],r.current(new io(l)),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?o:a)},[]);return d.useCallback(o=>{t.current.length===0&&i();const a=tr(o[0],o[1]);t.current.includes(a)||t.current.push(a)},[i])}const Or="header",qn="group-header",da="out-of-bounds";var wi;(function(e){e[e.Start=-2]="Start",e[e.StartPadding=-1]="StartPadding",e[e.Center=0]="Center",e[e.EndPadding=1]="EndPadding",e[e.End=2]="End"})(wi||(wi={}));function Fd(e,t){return e===t?!0:(e==null?void 0:e.kind)==="out-of-bounds"?(e==null?void 0:e.kind)===(t==null?void 0:t.kind)&&(e==null?void 0:e.location[0])===(t==null?void 0:t.location[0])&&(e==null?void 0:e.location[1])===(t==null?void 0:t.location[1])&&(e==null?void 0:e.region[0])===(t==null?void 0:t.region[0])&&(e==null?void 0:e.region[1])===(t==null?void 0:t.region[1]):(e==null?void 0:e.kind)===(t==null?void 0:t.kind)&&(e==null?void 0:e.location[0])===(t==null?void 0:t.location[0])&&(e==null?void 0:e.location[1])===(t==null?void 0:t.location[1])}const Eu=6,l0=(e,t)=>{if(e.kind===te.Custom)return e.copyData;const n=t==null?void 0:t(e);return(n==null?void 0:n.getAccessibilityString(e))??""},u0=(e,t)=>{const{width:n,height:r,accessibilityHeight:i,columns:o,cellXOffset:a,cellYOffset:l,headerHeight:s,fillHandle:u=!1,groupHeaderHeight:c,rowHeight:f,rows:g,getCellContent:h,getRowThemeOverride:m,onHeaderMenuClick:p,onHeaderIndicatorClick:v,enableGroups:w,isFilling:b,onCanvasFocused:M,onCanvasBlur:O,isFocused:S,selection:R,freezeColumns:L,onContextMenu:E,freezeTrailingRows:x,fixedShadowX:_=!0,fixedShadowY:D=!0,drawFocusRing:C,onMouseDown:I,onMouseUp:T,onMouseMoveRaw:k,onMouseMove:z,onItemHovered:$,dragAndDropState:X,firstColAccessible:re,onKeyDown:j,onKeyUp:G,highlightRegions:ae,canvasRef:ie,onDragStart:le,onDragEnd:fe,eventTargetRef:Q,isResizing:H,resizeColumn:P,isDragging:W,isDraggable:ce=!1,allowResize:De,disabledRows:He,hasAppendRow:ye,getGroupDetails:Re,theme:Se,prelightCells:Et,headerIcons:wt,verticalBorder:rt,drawCell:se,drawHeader:et,onCellFocused:me,onDragOverCell:he,onDrop:pe,onDragLeave:ze,imageWindowLoader:Pe,smoothScrollX:tt=!1,smoothScrollY:ve=!1,experimental:ue,getCellRenderer:it,resizeIndicator:Ae="full"}=e,Qe=e.translateX??0,vt=e.translateY??0,ht=Math.max(L,Math.min(o.length-1,a)),Ve=d.useRef(null),Dt=d.useRef((ue==null?void 0:ue.eventTarget)??window),Yt=Dt.current,xt=Pe,Lt=d.useRef(),[nn,zt]=d.useState(!1),ln=d.useRef([]),It=d.useRef(),[yt,Dn]=d.useState(),[yn,Oe]=d.useState(),_t=d.useRef(null),[fn,hn]=d.useState(),[pn,at]=d.useState(!1),Xt=d.useRef(pn);Xt.current=pn;const Ot=d.useMemo(()=>new zp(wt,()=>{Wt.current=void 0,Sn.current()}),[wt]),rn=w?c+s:s,Tt=d.useRef(-1),Ft=((ue==null?void 0:ue.enableFirefoxRescaling)??!1)&&a0.value,Be=((ue==null?void 0:ue.enableSafariRescaling)??!1)&&ua.value;d.useLayoutEffect(()=>{window.devicePixelRatio===1||!Ft&&!Be||(Tt.current!==-1&&zt(!0),window.clearTimeout(Tt.current),Tt.current=window.setTimeout(()=>{zt(!1),Tt.current=-1},200))},[l,ht,Qe,vt,Ft,Be]);const Bt=Lm(o,L),Pt=_?Si(Bt,X):0,Vt=d.useCallback((N,oe,Ce)=>{const xe=N.getBoundingClientRect();if(oe>=Bt.length||Ce>=g)return;const ge=xe.width/n,Me=Rs(oe,Ce,n,r,c,rn,ht,l,Qe,vt,g,L,x,Bt,f);return ge!==1&&(Me.x*=ge,Me.y*=ge,Me.width*=ge,Me.height*=ge),Me.x+=xe.x,Me.y+=xe.y,Me},[n,r,c,rn,ht,l,Qe,vt,g,L,x,Bt,f]),Nt=d.useCallback((N,oe,Ce,xe)=>{const ge=N.getBoundingClientRect(),Me=ge.width/n,nt=(oe-ge.left)/Me,ot=(Ce-ge.top)/Me,Ie=5,kt=Ms(Bt,ht,n,void 0,Qe);let ke=0,Ge=0;xe instanceof MouseEvent&&(ke=xe.button,Ge=xe.buttons);const Ue=Hm(nt,kt,Qe),ft=zm(ot,r,w,s,c,g,f,l,vt,x),sn=(xe==null?void 0:xe.shiftKey)===!0,En=(xe==null?void 0:xe.ctrlKey)===!0,Ct=(xe==null?void 0:xe.metaKey)===!0,Jt=xe!==void 0&&!(xe instanceof MouseEvent)||(xe==null?void 0:xe.pointerType)==="touch",Gn=[nt<0?-1:n<nt?1:0,ot<rn?-1:r<ot?1:0];let xn;if(Ue===-1||ot<0||nt<0||ft===void 0||nt>n||ot>r){const Rt=nt>n?1:nt<0?-1:0,In=ot>r?1:ot<0?-1:0;let On=Rt*2,St=In*2;Rt===0&&(On=Ue===-1?wi.EndPadding:wi.Center),In===0&&(St=ft===void 0?wi.EndPadding:wi.Center);let en=!1;if(Ue===-1&&ft===-1){const Rr=Vt(N,Bt.length-1,-1);Fn(Rr!==void 0),en=oe<Rr.x+Rr.width+Ie}const Mr=nt>n&&nt<n+xs()||ot>r&&ot<r+xs();xn={kind:da,location:[Ue!==-1?Ue:nt<0?0:Bt.length-1,ft??g-1],region:[On,St],shiftKey:sn,ctrlKey:En,metaKey:Ct,isEdge:en,isTouch:Jt,button:ke,buttons:Ge,scrollEdge:Gn,isMaybeScrollbar:Mr}}else if(ft<=-1){let Rt=Vt(N,Ue,ft);Fn(Rt!==void 0);let In=Rt!==void 0&&Rt.x+Rt.width-oe<=Ie;const On=Ue-1;oe-Rt.x<=Ie&&On>=0?(In=!0,Rt=Vt(N,On,ft),Fn(Rt!==void 0),xn={kind:w&&ft===-2?qn:Or,location:[On,ft],bounds:Rt,group:Bt[On].group??"",isEdge:In,shiftKey:sn,ctrlKey:En,metaKey:Ct,isTouch:Jt,localEventX:oe-Rt.x,localEventY:Ce-Rt.y,button:ke,buttons:Ge,scrollEdge:Gn}):xn={kind:w&&ft===-2?qn:Or,group:Bt[Ue].group??"",location:[Ue,ft],bounds:Rt,isEdge:In,shiftKey:sn,ctrlKey:En,metaKey:Ct,isTouch:Jt,localEventX:oe-Rt.x,localEventY:Ce-Rt.y,button:ke,buttons:Ge,scrollEdge:Gn}}else{const Rt=Vt(N,Ue,ft);Fn(Rt!==void 0);const In=Rt!==void 0&&Rt.x+Rt.width-oe<Ie;let On=!1;if(u&&R.current!==void 0){const St=hd(R.current.range),en=Vt(N,St[0],St[1]);if(en!==void 0){const Mr=en.x+en.width-2,Rr=en.y+en.height-2;On=Math.abs(Mr-oe)<Eu&&Math.abs(Rr-Ce)<Eu}}xn={kind:"cell",location:[Ue,ft],bounds:Rt,isEdge:In,shiftKey:sn,ctrlKey:En,isFillHandle:On,metaKey:Ct,isTouch:Jt,localEventX:oe-Rt.x,localEventY:Ce-Rt.y,button:ke,buttons:Ge,scrollEdge:Gn}}return xn},[n,Bt,ht,Qe,r,w,s,c,g,f,l,vt,x,Vt,u,R,rn]),[Cn]=yt??[],nr=d.useRef(()=>{}),Hn=d.useRef(yt);Hn.current=yt;const[V,Je]=d.useMemo(()=>{const N=document.createElement("canvas"),oe=document.createElement("canvas");return N.style.display="none",N.style.opacity="0",N.style.position="fixed",oe.style.display="none",oe.style.opacity="0",oe.style.position="fixed",[N.getContext("2d",{alpha:!1}),oe.getContext("2d",{alpha:!1})]},[]);d.useLayoutEffect(()=>{if(!(V===null||Je===null))return document.documentElement.append(V.canvas),document.documentElement.append(Je.canvas),()=>{V.canvas.remove(),Je.canvas.remove()}},[V,Je]);const We=d.useMemo(()=>new Xm,[]),de=Ft&&nn?1:Be&&nn?2:5,st=(ue==null?void 0:ue.disableMinimumCellWidth)===!0?1:10,Wt=d.useRef(),dn=d.useRef(null),Zt=d.useRef(null),Gt=d.useCallback(()=>{var nt;const N=Ve.current,oe=_t.current;if(N===null||oe===null||(dn.current===null&&(dn.current=N.getContext("2d",{alpha:!1}),N.width=0,N.height=0),Zt.current===null&&(Zt.current=oe.getContext("2d",{alpha:!1}),oe.width=0,oe.height=0),dn.current===null||Zt.current===null||V===null||Je===null))return;let Ce=!1;const xe=ot=>{Ce=!0,hn(ot)},ge=Wt.current,Me={headerCanvasCtx:Zt.current,canvasCtx:dn.current,bufferACtx:V,bufferBCtx:Je,width:n,height:r,cellXOffset:ht,cellYOffset:l,translateX:Math.round(Qe),translateY:Math.round(vt),mappedColumns:Bt,enableGroups:w,freezeColumns:L,dragAndDropState:X,theme:Se,headerHeight:s,groupHeaderHeight:c,disabledRows:He??mt.empty(),rowHeight:f,verticalBorder:rt,isResizing:H,resizeCol:P,isFocused:S,selection:R,fillHandle:u,drawCellCallback:se,hasAppendRow:ye,overrideCursor:xe,maxScaleFactor:de,freezeTrailingRows:x,rows:g,drawFocus:C,getCellContent:h,getGroupDetails:Re??(ot=>({name:ot})),getRowThemeOverride:m,drawHeaderCallback:et,prelightCells:Et,highlightRegions:ae,imageLoader:xt,lastBlitData:It,damage:Lt.current,hoverValues:ln.current,hoverInfo:Hn.current,spriteManager:Ot,scrolling:nn,hyperWrapping:(ue==null?void 0:ue.hyperWrapping)??!1,touchMode:pn,enqueue:nr.current,renderStateProvider:We,renderStrategy:(ue==null?void 0:ue.renderStrategy)??(ua.value?"double-buffer":"single-buffer"),getCellRenderer:it,minimumCellWidth:st,resizeIndicator:Ae};Me.damage===void 0?(Wt.current=Me,Ru(Me,ge)):Ru(Me,void 0),!Ce&&(Me.damage===void 0||Me.damage.has((nt=Hn==null?void 0:Hn.current)==null?void 0:nt[0]))&&hn(void 0)},[V,Je,n,r,ht,l,Qe,vt,Bt,w,L,X,Se,s,c,He,f,rt,H,ye,P,S,R,u,x,g,C,de,h,Re,m,se,et,Et,ae,xt,Ot,nn,ue==null?void 0:ue.hyperWrapping,ue==null?void 0:ue.renderStrategy,pn,We,it,st,Ae]),Sn=d.useRef(Gt);d.useLayoutEffect(()=>{Gt(),Sn.current=Gt},[Gt]),d.useLayoutEffect(()=>{(async()=>{var oe;((oe=document==null?void 0:document.fonts)==null?void 0:oe.ready)!==void 0&&(await document.fonts.ready,Wt.current=void 0,Sn.current())})()},[]);const vn=d.useCallback(N=>{Lt.current=N,Sn.current(),Lt.current=void 0},[]),Cr=s0(vn);nr.current=Cr;const Xn=d.useCallback(N=>{vn(new io(N.map(oe=>oe.cell)))},[vn]);xt.setCallback(vn);const[vo,Ia]=d.useState(!1),[ei,Sr]=Cn??[],ki=ei!==void 0&&Sr===-1,Ta=ei!==void 0&&Sr===-2;let Mi=!1,Ri=!1,Ar=fn;if(Ar===void 0&&ei!==void 0&&Sr!==void 0&&Sr>-1&&Sr<g){const N=h([ei,Sr],!0);Mi=N.kind===Yn.NewRow||N.kind===Yn.Marker&&N.markerKind!=="number",Ri=N.kind===te.Boolean&&Vs(N),Ar=N.cursor}const Rn=W?"grabbing":(yn??!1)||H?"col-resize":vo||b?"crosshair":Ar!==void 0?Ar:ki||Mi||Ri||Ta?"pointer":"default",Ei=d.useMemo(()=>({contain:"strict",display:"block",cursor:Rn}),[Rn]),Ii=d.useRef("default"),ti=Q==null?void 0:Q.current;ti!=null&&Ii.current!==Ei.cursor&&(ti.style.cursor=Ii.current=Ei.cursor);const fr=d.useCallback((N,oe,Ce,xe)=>{if(Re===void 0)return;const ge=Re(N);if(ge.actions!==void 0){const Me=Od(oe,ge.actions);for(const[nt,ot]of Me.entries())if(Xr(ot,Ce+oe.x,xe+ot.y))return ge.actions[nt]}},[Re]),hr=d.useCallback((N,oe,Ce,xe)=>{const ge=Bt[oe];if(!W&&!H&&!(yn??!1)){const Me=Vt(N,oe,-1);Fn(Me!==void 0);const nt=Pd(void 0,ge,Me.x,Me.y,Me.width,Me.height,Se,Bs(ge.title)==="rtl");if(ge.hasMenu===!0&&nt.menuBounds!==void 0&&Xr(nt.menuBounds,Ce,xe))return{area:"menu",bounds:nt.menuBounds};if(ge.indicatorIcon!==void 0&&nt.indicatorIconBounds!==void 0&&Xr(nt.indicatorIconBounds,Ce,xe))return{area:"indicator",bounds:nt.indicatorIconBounds}}},[Bt,Vt,yn,W,H,Se]),ni=d.useRef(0),rr=d.useRef(),ir=d.useRef(!1),Hr=d.useCallback(N=>{const oe=Ve.current,Ce=Q==null?void 0:Q.current;if(oe===null||N.target!==oe&&N.target!==Ce)return;ir.current=!0;let xe,ge;if(N instanceof MouseEvent?(xe=N.clientX,ge=N.clientY):(xe=N.touches[0].clientX,ge=N.touches[0].clientY),N.target===Ce&&Ce!==null){const nt=Ce.getBoundingClientRect();if(xe>nt.right||ge>nt.bottom)return}const Me=Nt(oe,xe,ge,N);rr.current=Me.location,Me.isTouch&&(ni.current=Date.now()),Xt.current!==Me.isTouch&&at(Me.isTouch),!(Me.kind===Or&&hr(oe,Me.location[0],xe,ge)!==void 0)&&(Me.kind===qn&&fr(Me.group,Me.bounds,Me.localEventX,Me.localEventY)!==void 0||(I==null||I(Me),!Me.isTouch&&ce!==!0&&ce!==Me.kind&&Me.button<3&&Me.button!==1&&N.preventDefault()))},[Q,ce,Nt,fr,hr,I]);gn("touchstart",Hr,Yt,!1),gn("mousedown",Hr,Yt,!1);const bo=d.useRef(0),Ti=d.useCallback(N=>{var Ge,Ue;const oe=bo.current;bo.current=Date.now();const Ce=Ve.current;if(ir.current=!1,T===void 0||Ce===null)return;const xe=Q==null?void 0:Q.current,ge=N.target!==Ce&&N.target!==xe;let Me,nt,ot=!0;if(N instanceof MouseEvent){if(Me=N.clientX,nt=N.clientY,ot=N.button<3,N.pointerType==="touch")return}else Me=N.changedTouches[0].clientX,nt=N.changedTouches[0].clientY;let Ie=Nt(Ce,Me,nt,N);Ie.isTouch&&ni.current!==0&&Date.now()-ni.current>500&&(Ie={...Ie,isLongTouch:!0}),oe!==0&&Date.now()-oe<(Ie.isTouch?1e3:500)&&(Ie={...Ie,isDoubleClick:!0}),Xt.current!==Ie.isTouch&&at(Ie.isTouch),!ge&&N.cancelable&&ot&&N.preventDefault();const[kt]=Ie.location,ke=hr(Ce,kt,Me,nt);if(Ie.kind===Or&&ke!==void 0){(Ie.button!==0||((Ge=rr.current)==null?void 0:Ge[0])!==kt||((Ue=rr.current)==null?void 0:Ue[1])!==-1)&&T(Ie,!0);return}else if(Ie.kind===qn){const ft=fr(Ie.group,Ie.bounds,Ie.localEventX,Ie.localEventY);if(ft!==void 0){Ie.button===0&&ft.onClick(Ie);return}}T(Ie,ge)},[T,Q,Nt,hr,fr]);gn("mouseup",Ti,Yt,!1),gn("touchend",Ti,Yt,!1);const je=d.useCallback(N=>{var kt,ke;const oe=Ve.current;if(oe===null)return;const Ce=Q==null?void 0:Q.current,xe=N.target!==oe&&N.target!==Ce;let ge,Me,nt=!0;N instanceof MouseEvent?(ge=N.clientX,Me=N.clientY,nt=N.button<3):(ge=N.changedTouches[0].clientX,Me=N.changedTouches[0].clientY);const ot=Nt(oe,ge,Me,N);Xt.current!==ot.isTouch&&at(ot.isTouch),!xe&&N.cancelable&&nt&&N.preventDefault();const[Ie]=ot.location;if(ot.kind===Or){const Ge=hr(oe,Ie,ge,Me);Ge!==void 0&&ot.button===0&&((kt=rr.current)==null?void 0:kt[0])===Ie&&((ke=rr.current)==null?void 0:ke[1])===-1&&(Ge.area==="menu"?p==null||p(Ie,Ge.bounds):Ge.area==="indicator"&&(v==null||v(Ie,Ge.bounds)))}else if(ot.kind===qn){const Ge=fr(ot.group,ot.bounds,ot.localEventX,ot.localEventY);Ge!==void 0&&ot.button===0&&Ge.onClick(ot)}},[Q,Nt,hr,p,v,fr]);gn("click",je,Yt,!1);const wo=d.useCallback(N=>{const oe=Ve.current,Ce=Q==null?void 0:Q.current;if(oe===null||N.target!==oe&&N.target!==Ce||E===void 0)return;const xe=Nt(oe,N.clientX,N.clientY,N);E(xe,()=>{N.cancelable&&N.preventDefault()})},[Q,Nt,E]);gn("contextmenu",wo,(Q==null?void 0:Q.current)??null,!1);const yo=d.useCallback(N=>{Lt.current=new io(N.map(oe=>oe.item)),ln.current=N,Sn.current(),Lt.current=void 0},[]),zn=d.useMemo(()=>new i0(yo),[yo]),Co=d.useRef(zn);Co.current=zn,d.useLayoutEffect(()=>{const N=Co.current;if(Cn===void 0||Cn[1]<0){N.setHovered(Cn);return}const oe=h(Cn,!0),Ce=it(oe),xe=Ce===void 0&&oe.kind===te.Custom||(Ce==null?void 0:Ce.needsHover)!==void 0&&(typeof Ce.needsHover=="boolean"?Ce.needsHover:Ce.needsHover(oe));N.setHovered(xe?Cn:void 0)},[h,it,Cn]);const jn=d.useRef(),Di=d.useCallback(N=>{var ot;const oe=Ve.current;if(oe===null)return;const Ce=Q==null?void 0:Q.current,xe=N.target!==oe&&N.target!==Ce,ge=Nt(oe,N.clientX,N.clientY,N);if(ge.kind!=="out-of-bounds"&&xe&&!ir.current&&!ge.isTouch)return;const Me=(Ie,kt)=>{Dn(ke=>ke===Ie||(ke==null?void 0:ke[0][0])===(Ie==null?void 0:Ie[0][0])&&(ke==null?void 0:ke[0][1])===(Ie==null?void 0:Ie[0][1])&&((ke==null?void 0:ke[1][0])===(Ie==null?void 0:Ie[1][0])&&(ke==null?void 0:ke[1][1])===(Ie==null?void 0:Ie[1][1])||!kt)?ke:Ie)};if(!Fd(ge,jn.current))hn(void 0),$==null||$(ge),Me(ge.kind===da?void 0:[ge.location,[ge.localEventX,ge.localEventY]],!0),jn.current=ge;else if(ge.kind==="cell"||ge.kind===Or||ge.kind===qn){let Ie=!1,kt=!0;if(ge.kind==="cell"){const Ge=h(ge.location);kt=((ot=it(Ge))==null?void 0:ot.needsHoverPosition)??Ge.kind===te.Custom,Ie=kt}else Ie=!0;const ke=[ge.location,[ge.localEventX,ge.localEventY]];Me(ke,kt),Hn.current=ke,Ie&&vn(new io([ge.location]))}const nt=ge.location[0]>=(re?0:1);Oe(ge.kind===Or&&ge.isEdge&&nt&&De===!0),Ia(ge.kind==="cell"&&ge.isFillHandle),k==null||k(N),z(ge)},[Q,Nt,re,De,k,z,$,h,it,vn]);gn("mousemove",Di,Yt,!0);const So=d.useCallback(N=>{const oe=Ve.current;if(oe===null)return;let Ce,xe;R.current!==void 0&&(Ce=Vt(oe,R.current.cell[0],R.current.cell[1]),xe=R.current.cell),j==null||j({bounds:Ce,stopPropagation:()=>N.stopPropagation(),preventDefault:()=>N.preventDefault(),cancel:()=>{},ctrlKey:N.ctrlKey,metaKey:N.metaKey,shiftKey:N.shiftKey,altKey:N.altKey,key:N.key,keyCode:N.keyCode,rawEvent:N,location:xe})},[j,R,Vt]),xo=d.useCallback(N=>{const oe=Ve.current;if(oe===null)return;let Ce,xe;R.current!==void 0&&(Ce=Vt(oe,R.current.cell[0],R.current.cell[1]),xe=R.current.cell),G==null||G({bounds:Ce,stopPropagation:()=>N.stopPropagation(),preventDefault:()=>N.preventDefault(),cancel:()=>{},ctrlKey:N.ctrlKey,metaKey:N.metaKey,shiftKey:N.shiftKey,altKey:N.altKey,key:N.key,keyCode:N.keyCode,rawEvent:N,location:xe})},[G,R,Vt]),Da=d.useCallback(N=>{if(Ve.current=N,ie!==void 0&&(ie.current=N),ue!=null&&ue.eventTarget)Dt.current=ue.eventTarget;else if(N===null)Dt.current=window;else{const oe=N.getRootNode();oe===document&&(Dt.current=window),Dt.current=oe}},[ie,ue==null?void 0:ue.eventTarget]),Oa=d.useCallback(N=>{const oe=Ve.current;if(oe===null||ce===!1||H){N.preventDefault();return}let Ce,xe;const ge=Nt(oe,N.clientX,N.clientY);if(ce!==!0&&ge.kind!==ce){N.preventDefault();return}const Me=(Ge,Ue)=>{Ce=Ge,xe=Ue};let nt,ot,Ie;const kt=(Ge,Ue,ft)=>{nt=Ge,ot=Ue,Ie=ft};let ke=!1;if(le==null||le({...ge,setData:Me,setDragImage:kt,preventDefault:()=>ke=!0,defaultPrevented:()=>ke}),!ke&&Ce!==void 0&&xe!==void 0&&N.dataTransfer!==null)if(N.dataTransfer.setData(Ce,xe),N.dataTransfer.effectAllowed="copyLink",nt!==void 0&&ot!==void 0&&Ie!==void 0)N.dataTransfer.setDragImage(nt,ot,Ie);else{const[Ge,Ue]=ge.location;if(Ue!==void 0){const ft=document.createElement("canvas"),sn=Vt(oe,Ge,Ue);Fn(sn!==void 0);const En=Math.ceil(window.devicePixelRatio??1);ft.width=sn.width*En,ft.height=sn.height*En;const Ct=ft.getContext("2d");Ct!==null&&(Ct.scale(En,En),Ct.textBaseline="middle",Ue===-1?(Ct.font=Se.headerFontFull,Ct.fillStyle=Se.bgHeader,Ct.fillRect(0,0,ft.width,ft.height),Ld(Ct,0,0,sn.width,sn.height,Bt[Ge],!1,Se,!1,void 0,void 0,!1,0,Ot,et,!1)):(Ct.font=Se.baseFontFull,Ct.fillStyle=Se.bgCell,Ct.fillRect(0,0,ft.width,ft.height),Dd(Ct,h([Ge,Ue]),0,Ue,!1,!1,0,0,sn.width,sn.height,!1,Se,Se.bgCell,xt,Ot,1,void 0,!1,0,void 0,void 0,void 0,We,it,()=>{}))),ft.style.left="-100%",ft.style.position="absolute",ft.style.width=`${sn.width}px`,ft.style.height=`${sn.height}px`,document.body.append(ft),N.dataTransfer.setDragImage(ft,sn.width/2,sn.height/2),window.setTimeout(()=>{ft.remove()},0)}}else N.preventDefault()},[ce,H,Nt,le,Vt,Se,Bt,Ot,et,h,xt,We,it]);gn("dragstart",Oa,(Q==null?void 0:Q.current)??null,!1,!1);const Vn=d.useRef(),xr=d.useCallback(N=>{const oe=Ve.current;if(pe!==void 0&&N.preventDefault(),oe===null||he===void 0)return;const Ce=Nt(oe,N.clientX,N.clientY),[xe,ge]=Ce.location,Me=xe-(re?0:1),[nt,ot]=Vn.current??[];(nt!==Me||ot!==ge)&&(Vn.current=[Me,ge],he([Me,ge],N.dataTransfer))},[re,Nt,he,pe]);gn("dragover",xr,(Q==null?void 0:Q.current)??null,!1,!1);const Nn=d.useCallback(()=>{Vn.current=void 0,fe==null||fe()},[fe]);gn("dragend",Nn,(Q==null?void 0:Q.current)??null,!1,!1);const q=d.useCallback(N=>{const oe=Ve.current;if(oe===null||pe===void 0)return;N.preventDefault();const Ce=Nt(oe,N.clientX,N.clientY),[xe,ge]=Ce.location,Me=xe-(re?0:1);pe([Me,ge],N.dataTransfer)},[re,Nt,pe]);gn("drop",q,(Q==null?void 0:Q.current)??null,!1,!1);const Kt=d.useCallback(()=>{ze==null||ze()},[ze]);gn("dragleave",Kt,(Q==null?void 0:Q.current)??null,!1,!1);const kr=d.useRef(R);kr.current=R;const ri=d.useRef(null),ii=d.useCallback(N=>{var oe;Ve.current===null||!Ve.current.contains(document.activeElement)||(N===null&&kr.current.current!==void 0?(oe=ie==null?void 0:ie.current)==null||oe.focus({preventScroll:!0}):N!==null&&N.focus({preventScroll:!0}),ri.current=N)},[ie]);d.useImperativeHandle(t,()=>({focus:()=>{var oe;const N=ri.current;N===null||!document.contains(N)?(oe=ie==null?void 0:ie.current)==null||oe.focus({preventScroll:!0}):N.focus({preventScroll:!0})},getBounds:(N,oe)=>{if(!(ie===void 0||ie.current===null))return Vt(ie.current,N??0,oe??-1)},damage:Xn}),[ie,Xn,Vt]);const Oi=d.useRef(),Pa=Jg(()=>{var ot,Ie,kt;if(n<50||(ue==null?void 0:ue.disableAccessibilityTree)===!0)return null;let N=Ms(Bt,ht,n,X,Qe);const oe=re?0:-1;!re&&((ot=N[0])==null?void 0:ot.sourceIndex)===0&&(N=N.slice(1));const[Ce,xe]=((Ie=R.current)==null?void 0:Ie.cell)??[],ge=(kt=R.current)==null?void 0:kt.range,Me=N.map(ke=>ke.sourceIndex),nt=lr(l,Math.min(g,l+i));return Ce!==void 0&&xe!==void 0&&!(Me.includes(Ce)&&nt.includes(xe))&&ii(null),d.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":g+1,"aria-multiselectable":"true","aria-colcount":Bt.length+oe},d.createElement("thead",{role:"rowgroup"},d.createElement("tr",{role:"row","aria-rowindex":1},N.map(ke=>d.createElement("th",{role:"columnheader","aria-selected":R.columns.hasIndex(ke.sourceIndex),"aria-colindex":ke.sourceIndex+1+oe,tabIndex:-1,onFocus:Ge=>{if(Ge.target!==ri.current)return me==null?void 0:me([ke.sourceIndex,-1])},key:ke.sourceIndex},ke.title)))),d.createElement("tbody",{role:"rowgroup"},nt.map(ke=>d.createElement("tr",{role:"row","aria-selected":R.rows.hasIndex(ke),key:ke,"aria-rowindex":ke+2},N.map(Ge=>{const Ue=Ge.sourceIndex,ft=tr(Ue,ke),sn=Ce===Ue&&xe===ke,En=ge!==void 0&&Ue>=ge.x&&Ue<ge.x+ge.width&&ke>=ge.y&&ke<ge.y+ge.height,Ct=`glide-cell-${Ue}-${ke}`,Jt=[Ue,ke],Gn=h(Jt,!0);return d.createElement("td",{key:ft,role:"gridcell","aria-colindex":Ue+1+oe,"aria-selected":En,"aria-readonly":vi(Gn)||!Qi(Gn),id:Ct,"data-testid":Ct,onClick:()=>{const xn=ie==null?void 0:ie.current;if(xn!=null)return j==null?void 0:j({bounds:Vt(xn,Ue,ke),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:Jt})},onFocusCapture:xn=>{var Rt,In;if(!(xn.target===ri.current||((Rt=Oi.current)==null?void 0:Rt[0])===Ue&&((In=Oi.current)==null?void 0:In[1])===ke))return Oi.current=Jt,me==null?void 0:me(Jt)},ref:sn?ii:void 0,tabIndex:-1},l0(Gn,it))})))))},[n,Bt,ht,X,Qe,g,l,i,R,ii,h,ie,j,Vt,me],200),Pi=L===0||!_?0:ht>L?1:Un(-Qe/100,0,1),K=-l*32+vt,un=D?Un(-K/100,0,1):0,cn=d.useMemo(()=>{if(!Pi&&!un)return null;const N={position:"absolute",top:0,left:Pt,width:n-Pt,height:r,opacity:Pi,pointerEvents:"none",transition:tt?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},oe={position:"absolute",top:rn,left:0,width:n,height:r,opacity:un,pointerEvents:"none",transition:ve?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return d.createElement(d.Fragment,null,Pi>0&&d.createElement("div",{id:"shadow-x",style:N}),un>0&&d.createElement("div",{id:"shadow-y",style:oe}))},[Pi,un,Pt,n,tt,rn,r,ve]),La=d.useMemo(()=>({position:"absolute",top:0,left:0}),[]);return d.createElement(d.Fragment,null,d.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:So,onKeyUp:xo,onFocus:M,onBlur:O,ref:Da,style:Ei},Pa),d.createElement("canvas",{ref:_t,style:La}),cn)},c0=d.memo(d.forwardRef(u0));function Gi(e,t,n,r){return Un(Math.round(t-(e.growOffset??0)),Math.ceil(n),Math.floor(r))}const d0=e=>{const[t,n]=d.useState(),[r,i]=d.useState(),[o,a]=d.useState(),[l,s]=d.useState(),[u,c]=d.useState(!1),[f,g]=d.useState(),[h,m]=d.useState(),[p,v]=d.useState(),[w,b]=d.useState(!1),[M,O]=d.useState(),{onHeaderMenuClick:S,onHeaderIndicatorClick:R,getCellContent:L,onColumnMoved:E,onColumnResize:x,onColumnResizeStart:_,onColumnResizeEnd:D,gridRef:C,maxColumnWidth:I,minColumnWidth:T,onRowMoved:k,lockColumns:z,onColumnProposeMove:$,onMouseDown:X,onMouseUp:re,onItemHovered:j,onDragStart:G,canvasRef:ae}=e,ie=(x??D??_)!==void 0,{columns:le,selection:fe}=e,Q=fe.columns,H=d.useCallback(se=>{const[et,me]=se.location;o!==void 0&&l!==et&&et>=z?(c(!0),s(et)):h!==void 0&&me!==void 0?(b(!0),v(Math.max(0,me))):r===void 0&&!u&&!w&&(j==null||j(se))},[o,h,l,j,z,r,u,w]),P=E!==void 0,W=d.useCallback(se=>{var et;if(se.button===0){const[me,he]=se.location;if(se.kind==="out-of-bounds"&&se.isEdge&&ie){const pe=(et=C==null?void 0:C.current)==null?void 0:et.getBounds(le.length-1,-1);pe!==void 0&&(n(pe.x),i(le.length-1))}else if(se.kind==="header"&&me>=z){const pe=ae==null?void 0:ae.current;if(se.isEdge&&ie&&pe){n(se.bounds.x),i(me);const Pe=pe.getBoundingClientRect().width/pe.offsetWidth,tt=se.bounds.width/Pe;_==null||_(le[me],tt,me,tt+(le[me].growOffset??0))}else se.kind==="header"&&P&&(g(se.bounds.x),a(me))}else se.kind==="cell"&&z>0&&me===0&&he!==void 0&&k!==void 0&&(O(se.bounds.y),m(he))}X==null||X(se)},[X,ie,z,k,C,le,P,_,ae]),ce=d.useCallback((se,et)=>{u||w||S==null||S(se,et)},[u,w,S]),De=d.useCallback((se,et)=>{u||w||R==null||R(se,et)},[u,w,R]),He=d.useRef(-1),ye=d.useCallback(()=>{He.current=-1,m(void 0),v(void 0),O(void 0),b(!1),a(void 0),s(void 0),g(void 0),c(!1),i(void 0),n(void 0)},[]),Re=d.useCallback((se,et)=>{if(se.button===0){if(r!==void 0){if((Q==null?void 0:Q.hasIndex(r))===!0)for(const he of Q){if(he===r)continue;const pe=le[he],ze=Gi(pe,He.current,T,I);x==null||x(pe,ze,he,ze+(pe.growOffset??0))}const me=Gi(le[r],He.current,T,I);if(D==null||D(le[r],me,r,me+(le[r].growOffset??0)),Q.hasIndex(r))for(const he of Q){if(he===r)continue;const pe=le[he],ze=Gi(pe,He.current,T,I);D==null||D(pe,ze,he,ze+(pe.growOffset??0))}}ye(),o!==void 0&&l!==void 0&&(E==null||E(o,l)),h!==void 0&&p!==void 0&&(k==null||k(h,p))}re==null||re(se,et)},[re,r,o,l,h,p,Q,D,le,T,I,x,E,k,ye]),Se=d.useMemo(()=>{if(!(o===void 0||l===void 0)&&o!==l&&($==null?void 0:$(o,l))!==!1)return{src:o,dest:l}},[o,l,$]),Et=d.useCallback(se=>{const et=ae==null?void 0:ae.current;if(o!==void 0&&f!==void 0)Math.abs(se.clientX-f)>20&&c(!0);else if(h!==void 0&&M!==void 0)Math.abs(se.clientY-M)>20&&b(!0);else if(r!==void 0&&t!==void 0&&et){const he=et.getBoundingClientRect().width/et.offsetWidth,pe=(se.clientX-t)/he,ze=le[r],Pe=Gi(ze,pe,T,I);if(x==null||x(ze,Pe,r,Pe+(ze.growOffset??0)),He.current=pe,(Q==null?void 0:Q.first())===r)for(const tt of Q){if(tt===r)continue;const ve=le[tt],ue=Gi(ve,He.current,T,I);x==null||x(ve,ue,tt,ue+(ve.growOffset??0))}}},[o,f,h,M,r,t,le,T,I,x,Q,ae]),wt=d.useCallback((se,et)=>{if(h===void 0||p===void 0)return L(se,et);let[me,he]=se;return he===p?he=h:(he>p&&(he-=1),he>=h&&(he+=1)),L([me,he],et)},[h,p,L]),rt=d.useCallback(se=>{G==null||G(se),se.defaultPrevented()||ye()},[ye,G]);return d.createElement(c0,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,resizeColumn:r,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,resizeIndicator:e.resizeIndicator,verticalBorder:e.verticalBorder,width:e.width,getCellContent:wt,isResizing:r!==void 0,onHeaderMenuClick:ce,onHeaderIndicatorClick:De,isDragging:u,onItemHovered:H,onDragStart:rt,onMouseDown:W,allowResize:ie,onMouseUp:Re,dragAndDropState:Se,onMouseMoveRaw:Et,ref:C})};function f0(e){const t=d.useRef(null),[n,r]=d.useState({width:e==null?void 0:e[0],height:e==null?void 0:e[1]});return d.useLayoutEffect(()=>{const i=a=>{for(const l of a){const{width:s,height:u}=l&&l.contentRect||{};r(c=>c.width===s&&c.height===u?c:{width:s,height:u})}},o=new window.ResizeObserver(i);return t.current&&o.observe(t.current,void 0),()=>{o.disconnect()}},[t.current]),{ref:t,...n}}const h0=(e,t,n)=>{const r=d.useRef(null),i=d.useRef(null),o=d.useRef(null),a=d.useRef(0),l=d.useRef(t);l.current=t;const s=n.current;d.useEffect(()=>{const u=()=>{var g,h;if(i.current===!1&&s!==null){const m=[s.scrollLeft,s.scrollTop];if(((g=o.current)==null?void 0:g[0])===m[0]&&((h=o.current)==null?void 0:h[1])===m[1])if(a.current>10){o.current=null,i.current=null;return}else a.current++;else a.current=0,l.current(m[0],m[1]),o.current=m;r.current=window.setTimeout(u,8.333333333333334)}},c=()=>{i.current=!0,o.current=null,r.current!==null&&(window.clearTimeout(r.current),r.current=null)},f=g=>{g.touches.length===0&&(i.current=!1,a.current=0,r.current=window.setTimeout(u,8.333333333333334))};if(e&&s!==null){const g=s;return g.addEventListener("touchstart",c),g.addEventListener("touchend",f),()=>{g.removeEventListener("touchstart",c),g.removeEventListener("touchend",f),r.current!==null&&window.clearTimeout(r.current)}}},[e,s])},g0=()=>e=>e.isSafari?"scroll":"auto",m0=mn("div")({name:"ScrollRegionStyle",class:"gdg-s1dgczr6",propsAsIs:!1,vars:{"s1dgczr6-0":[g0()]}});function p0(e){const[t,n]=d.useState(!1),r=typeof window>"u"?null:window,i=d.useRef(0);return gn("touchstart",d.useCallback(()=>{window.clearTimeout(i.current),n(!0)},[]),r,!0,!1),gn("touchend",d.useCallback(o=>{o.touches.length===0&&(i.current=window.setTimeout(()=>n(!1),e))},[e]),r,!0,!1),t}const v0=e=>{var ie,le;const{children:t,clientHeight:n,scrollHeight:r,scrollWidth:i,update:o,draggable:a,className:l,preventDiagonalScrolling:s=!1,paddingBottom:u=0,paddingRight:c=0,rightElement:f,rightElementProps:g,kineticScrollPerfHack:h=!1,scrollRef:m,initialSize:p}=e,v=[],w=(g==null?void 0:g.sticky)??!1,b=(g==null?void 0:g.fill)??!1,M=d.useRef(0),O=d.useRef(0),S=d.useRef(null),R=typeof window>"u"?1:window.devicePixelRatio,L=d.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),E=d.useRef(null),x=p0(200),[_,D]=d.useState(!0),C=d.useRef(0);d.useLayoutEffect(()=>{if(!_||x||L.current.lockDirection===void 0)return;const fe=S.current;if(fe===null)return;const[Q,H]=L.current.lockDirection;Q!==void 0?fe.scrollLeft=Q:H!==void 0&&(fe.scrollTop=H),L.current.lockDirection=void 0},[x,_]);const I=d.useCallback((fe,Q)=>{var rt;const H=S.current;if(H===null)return;Q=Q??H.scrollTop,fe=fe??H.scrollLeft;const P=L.current.scrollTop,W=L.current.scrollLeft,ce=fe-W,De=Q-P;x&&ce!==0&&De!==0&&(Math.abs(ce)>3||Math.abs(De)>3)&&s&&L.current.lockDirection===void 0&&(L.current.lockDirection=Math.abs(ce)<Math.abs(De)?[W,void 0]:[void 0,P]);const He=L.current.lockDirection;fe=(He==null?void 0:He[0])??fe,Q=(He==null?void 0:He[1])??Q,L.current.scrollLeft=fe,L.current.scrollTop=Q;const ye=H.clientWidth,Re=H.clientHeight,Se=Q,Et=O.current-Se,wt=H.scrollHeight-Re;if(O.current=Se,wt>0&&(Math.abs(Et)>2e3||Se===0||Se===wt)&&r>H.scrollHeight+5){const se=Se/wt,et=(r-Re)*se;M.current=et-Se}He!==void 0&&(window.clearTimeout(C.current),D(!1),C.current=window.setTimeout(()=>D(!0),200)),o({x:fe,y:Se+M.current,width:ye-c,height:Re-u,paddingRight:((rt=E.current)==null?void 0:rt.clientWidth)??0})},[u,c,r,o,s,x]);h0(h&&ua.value,I,S);const T=d.useRef(I);T.current=I;const k=d.useRef(),z=d.useRef(!1);d.useLayoutEffect(()=>{z.current?I():z.current=!0},[I,u,c]);const $=d.useCallback(fe=>{S.current=fe,m!==void 0&&(m.current=fe)},[m]);let X=0,re=0;for(v.push(d.createElement("div",{key:X++,style:{width:i,height:0}}));re<r;){const fe=Math.min(5e6,r-re);v.push(d.createElement("div",{key:X++,style:{width:0,height:fe}})),re+=fe}const{ref:j,width:G,height:ae}=f0(p);return typeof window<"u"&&(((ie=k.current)==null?void 0:ie.height)!==ae||((le=k.current)==null?void 0:le.width)!==G)&&(window.setTimeout(()=>T.current(),0),k.current={width:G,height:ae}),(G??0)===0||(ae??0)===0?d.createElement("div",{ref:j}):d.createElement("div",{ref:j},d.createElement(m0,{isSafari:ua.value},d.createElement("div",{className:"dvn-underlay"},t),d.createElement("div",{ref:$,style:k.current,draggable:a,onDragStart:fe=>{a||(fe.stopPropagation(),fe.preventDefault())},className:"dvn-scroller "+(l??""),onScroll:()=>I()},d.createElement("div",{className:"dvn-scroll-inner"+(f===void 0?" dvn-hidden":"")},d.createElement("div",{className:"dvn-stack"},v),f!==void 0&&d.createElement(d.Fragment,null,!b&&d.createElement("div",{className:"dvn-spacer"}),d.createElement("div",{ref:E,style:{height:ae,maxHeight:n-Math.ceil(R%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:c,flexGrow:b?1:void 0,right:w?c??0:void 0,pointerEvents:"auto"}},f))))))},b0=e=>{const{columns:t,rows:n,rowHeight:r,headerHeight:i,groupHeaderHeight:o,enableGroups:a,freezeColumns:l,experimental:s,nonGrowWidth:u,clientSize:c,className:f,onVisibleRegionChanged:g,scrollRef:h,preventDiagonalScrolling:m,rightElement:p,rightElementProps:v,overscrollX:w,overscrollY:b,initialSize:M,smoothScrollX:O=!1,smoothScrollY:S=!1,isDraggable:R}=e,{paddingRight:L,paddingBottom:E}=s??{},[x,_]=c,D=d.useRef(),C=d.useRef(),I=d.useRef(),T=d.useRef(),k=u+Math.max(0,w??0);let z=a?i+o:i;if(typeof r=="number")z+=n*r;else for(let j=0;j<n;j++)z+=r(j);b!==void 0&&(z+=b);const $=d.useRef(),X=d.useCallback(()=>{var De,He;if($.current===void 0)return;const j={...$.current};let G=0,ae=j.x<0?-j.x:0,ie=0,le=0;j.x=j.x<0?0:j.x;let fe=0;for(let ye=0;ye<l;ye++)fe+=t[ye].width;for(const ye of t){const Re=G-fe;if(j.x>=Re+ye.width)G+=ye.width,le++,ie++;else if(j.x>Re)G+=ye.width,O?ae+=Re-j.x:le++,ie++;else if(j.x+j.width>Re)G+=ye.width,ie++;else break}let Q=0,H=0,P=0;if(typeof r=="number")S?(H=Math.floor(j.y/r),Q=H*r-j.y):H=Math.ceil(j.y/r),P=Math.ceil(j.height/r)+H,Q<0&&P++;else{let ye=0;for(let Re=0;Re<n;Re++){const Se=r(Re),Et=ye+(S?0:Se/2);if(j.y>=ye+Se)ye+=Se,H++,P++;else if(j.y>Et)ye+=Se,S?Q+=Et-j.y:H++,P++;else if(j.y+j.height>Se/2+ye)ye+=Se,P++;else break}}const W={x:le,y:H,width:ie-le,height:P-H},ce=D.current;(ce===void 0||ce.y!==W.y||ce.x!==W.x||ce.height!==W.height||ce.width!==W.width||C.current!==ae||I.current!==Q||j.width!==((De=T.current)==null?void 0:De[0])||j.height!==((He=T.current)==null?void 0:He[1]))&&(g==null||g({x:le,y:H,width:ie-le,height:P-H},j.width,j.height,j.paddingRight??0,ae,Q),D.current=W,C.current=ae,I.current=Q,T.current=[j.width,j.height])},[t,r,n,g,l,O,S]),re=d.useCallback(j=>{$.current=j,X()},[X]);return d.useEffect(()=>{X()},[X]),d.createElement(v0,{scrollRef:h,className:f,kineticScrollPerfHack:s==null?void 0:s.kineticScrollPerfHack,preventDiagonalScrolling:m,draggable:R===!0||typeof R=="string",scrollWidth:k+(L??0),scrollHeight:z+(E??0),clientHeight:_,rightElement:p,paddingBottom:E,paddingRight:L,rightElementProps:v,update:re,initialSize:M},d.createElement(d0,{eventTargetRef:h,width:x,height:_,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,onColumnProposeMove:e.onColumnProposeMove,verticalBorder:e.verticalBorder,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}))},w0=mn("div")({name:"SearchWrapper",class:"gdg-seveqep",propsAsIs:!1}),y0=d.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},d.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),C0=d.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},d.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),S0=d.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},d.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),x0=10,k0=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:i,searchInputRef:o,searchValue:a,searchResults:l,onSearchValueChange:s,getCellsForSelection:u,onSearchResultsChanged:c,showSearch:f=!1,onSearchClose:g}=e,[h]=d.useState(()=>"search-box-"+Math.round(Math.random()*1e3)),[m,p]=d.useState(""),v=a??m,w=d.useCallback(G=>{p(G),s==null||s(G)},[s]),[b,M]=d.useState(),O=d.useRef(b);O.current=b,d.useEffect(()=>{l!==void 0&&(l.length>0?M(G=>({rowsSearched:r,results:l.length,selectedIndex:(G==null?void 0:G.selectedIndex)??-1})):M(void 0))},[r,l]);const S=d.useRef();S.current===void 0&&(S.current=new AbortController);const R=d.useRef(),[L,E]=d.useState([]),x=l??L,_=d.useCallback(()=>{R.current!==void 0&&(window.cancelAnimationFrame(R.current),R.current=void 0,S.current.abort())},[]),D=d.useRef(n);D.current=n;const C=d.useCallback(G=>{const ae=new RegExp(G.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let ie=D.current,le=Math.min(10,r),fe=0;M(void 0),E([]);const Q=[],H=async()=>{var wt;if(u===void 0)return;const P=performance.now(),W=r-fe;let ce=u({x:0,y:ie,width:i.length,height:Math.min(le,W,r-ie)},S.current.signal);typeof ce=="function"&&(ce=await ce());let De=!1;for(const[rt,se]of ce.entries())for(const[et,me]of se.entries()){let he;switch(me.kind){case te.Text:case te.Number:he=me.displayData;break;case te.Uri:case te.Markdown:he=me.data;break;case te.Boolean:he=typeof me.data=="boolean"?me.data.toString():void 0;break;case te.Image:case te.Bubble:he=me.data.join("🐳");break;case te.Custom:he=me.copyData;break}he!==void 0&&ae.test(he)&&(Q.push([et,rt+ie]),De=!0)}const He=performance.now();De&&E([...Q]),fe+=ce.length,Fn(fe<=r);const ye=((wt=O.current)==null?void 0:wt.selectedIndex)??-1;M({results:Q.length,rowsSearched:fe,selectedIndex:ye}),c==null||c(Q,ye),ie+le>=r?ie=0:ie+=le;const Re=He-P,Se=Math.max(Re,1),Et=x0/Se;le=Math.ceil(le*Et),fe<r&&Q.length<1e3&&(R.current=window.requestAnimationFrame(H))};_(),R.current=window.requestAnimationFrame(H)},[_,i.length,u,c,r]),I=d.useCallback(()=>{var G;g==null||g(),M(void 0),E([]),c==null||c([],-1),_(),(G=t==null?void 0:t.current)==null||G.focus()},[_,t,g,c]),T=d.useCallback(G=>{w(G.target.value),l===void 0&&(G.target.value===""?(M(void 0),E([]),_()):C(G.target.value))},[C,_,w,l]);d.useEffect(()=>{f&&o.current!==null&&(w(""),o.current.focus({preventScroll:!0}))},[f,o,w]);const k=d.useCallback(G=>{var ie;if((ie=G==null?void 0:G.stopPropagation)==null||ie.call(G),b===void 0)return;const ae=(b.selectedIndex+1)%b.results;M({...b,selectedIndex:ae}),c==null||c(x,ae)},[b,c,x]),z=d.useCallback(G=>{var ie;if((ie=G==null?void 0:G.stopPropagation)==null||ie.call(G),b===void 0)return;let ae=(b.selectedIndex-1)%b.results;ae<0&&(ae+=b.results),M({...b,selectedIndex:ae}),c==null||c(x,ae)},[c,x,b]),$=d.useCallback(G=>{(G.ctrlKey||G.metaKey)&&G.nativeEvent.code==="KeyF"||G.key==="Escape"?(I(),G.stopPropagation(),G.preventDefault()):G.key==="Enter"&&(G.shiftKey?z():k())},[I,k,z]);d.useEffect(()=>()=>{_()},[_]);const[X,re]=d.useState(!1);d.useEffect(()=>{if(f)re(!0);else{const G=setTimeout(()=>re(!1),150);return()=>clearTimeout(G)}},[f]);const j=d.useMemo(()=>{if(!f&&!X)return null;let G;b!==void 0&&(G=b.results>=1e3?"over 1000":`${b.results} result${b.results!==1?"s":""}`,b.selectedIndex>=0&&(G=`${b.selectedIndex+1} of ${G}`));const ae=fe=>{fe.stopPropagation()},le={width:`${Math.floor(((b==null?void 0:b.rowsSearched)??0)/r*100)}%`};return d.createElement(w0,{className:f?"":"out",onMouseDown:ae,onMouseMove:ae,onMouseUp:ae,onClick:ae},d.createElement("div",{className:"gdg-search-bar-inner"},d.createElement("input",{id:h,"aria-hidden":!f,"data-testid":"search-input",ref:o,onChange:T,value:v,tabIndex:f?void 0:-1,onKeyDownCapture:$}),d.createElement("button",{"aria-label":"Previous Result","aria-hidden":!f,tabIndex:f?void 0:-1,onClick:z,disabled:((b==null?void 0:b.results)??0)===0},y0),d.createElement("button",{"aria-label":"Next Result","aria-hidden":!f,tabIndex:f?void 0:-1,onClick:k,disabled:((b==null?void 0:b.results)??0)===0},C0),g!==void 0&&d.createElement("button",{"aria-label":"Close Search","aria-hidden":!f,"data-testid":"search-close-button",tabIndex:f?void 0:-1,onClick:I},S0)),b!==void 0?d.createElement(d.Fragment,null,d.createElement("div",{className:"gdg-search-status"},d.createElement("div",{"data-testid":"search-result-area"},G)),d.createElement("div",{className:"gdg-search-progress",style:le})):d.createElement("div",{className:"gdg-search-status"},d.createElement("label",{htmlFor:h},"Type to search")))},[f,X,b,r,h,o,T,v,$,z,k,g,I]);return d.createElement(d.Fragment,null,d.createElement(b0,{prelightCells:x,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,nonGrowWidth:e.nonGrowWidth,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,onColumnProposeMove:e.onColumnProposeMove,drawFocusRing:e.drawFocusRing,drawCell:e.drawCell,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}),j)};class M0 extends d.PureComponent{constructor(){super(...arguments);dt(this,"wrapperRef",d.createRef());dt(this,"clickOutside",n=>{if(!(this.props.isOutsideClick&&!this.props.isOutsideClick(n))&&this.wrapperRef.current!==null&&!this.wrapperRef.current.contains(n.target)){let r=n.target;for(;r!==null;){if(r.classList.contains("click-outside-ignore"))return;r=r.parentElement}this.props.onClickOutside()}})}componentDidMount(){const n=this.props.customEventTarget??document;n.addEventListener("touchend",this.clickOutside,!0),n.addEventListener("mousedown",this.clickOutside,!0),n.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){const n=this.props.customEventTarget??document;n.removeEventListener("touchend",this.clickOutside,!0),n.removeEventListener("mousedown",this.clickOutside,!0),n.removeEventListener("contextmenu",this.clickOutside,!0)}render(){const{onClickOutside:n,isOutsideClick:r,customEventTarget:i,...o}=this.props;return d.createElement("div",{...o,ref:this.wrapperRef},this.props.children)}}const R0=()=>e=>Math.max(16,e.targetHeight-10),E0=mn("input")({name:"RenameInput",class:"gdg-r17m35ur",propsAsIs:!1,vars:{"r17m35ur-0":[R0(),"px"]}}),I0=e=>{const{bounds:t,group:n,onClose:r,canvasBounds:i,onFinish:o}=e,[a,l]=At.useState(n);return At.createElement(M0,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"gdg-c1tqibwd",onClickOutside:r},At.createElement(E0,{targetHeight:t.height,"data-testid":"group-rename-input",value:a,onBlur:r,onFocus:s=>s.target.setSelectionRange(0,a.length),onChange:s=>l(s.target.value),onKeyDown:s=>{s.key==="Enter"?o(a):s.key==="Escape"&&r()},autoFocus:!0}))};function T0(e,t){return e===void 0?!1:e.length>1&&e.startsWith("_")?Number.parseInt(e.slice(1))===t.keyCode:e.length===1&&e>="a"&&e<="z"?e.toUpperCase().codePointAt(0)===t.keyCode:e===t.key}function ut(e,t,n){const r=Ad(e,t);return r&&(n.didMatch=!0),r}function Ad(e,t){if(e.length===0)return!1;if(e.includes("|")){const s=e.split("|");for(const u of s)if(Ad(u,t))return!0;return!1}let n=!1,r=!1,i=!1,o=!1;const a=e.split("+"),l=a.pop();if(!T0(l,t))return!1;if(a[0]==="any")return!0;for(const s of a)switch(s){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":i=!0;break;case"meta":o=!0;break;case"primary":ca.value?o=!0:n=!0;break}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===o}function D0(e,t,n,r,i,o,a){const l=At.useCallback((c,f,g,h)=>{var M;(o==="cell"||o==="multi-cell")&&c!==void 0&&(c={...c,range:{x:c.cell[0],y:c.cell[1],width:1,height:1}}),!a&&c!==void 0&&c.range.width>1&&(c={...c,range:{...c.range,width:1,x:c.cell[0]}});const m=n==="mixed"&&(g||h==="drag"),p=r==="mixed"&&m,v=i==="mixed"&&m;let w={current:c===void 0?void 0:{...c,rangeStack:h==="drag"?((M=e.current)==null?void 0:M.rangeStack)??[]:[]},columns:p?e.columns:mt.empty(),rows:v?e.rows:mt.empty()};g&&(o==="multi-rect"||o==="multi-cell")&&w.current!==void 0&&e.current!==void 0&&(w={...w,current:{...w.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(w,f)},[r,e,n,o,a,i,t]),s=At.useCallback((c,f,g)=>{c=c??e.rows,f!==void 0&&(c=c.add(f));let h;if(i==="exclusive"&&c.length>0)h={current:void 0,columns:mt.empty(),rows:c};else{const m=g&&n==="mixed",p=g&&r==="mixed";h={current:m?e.current:void 0,columns:p?e.columns:mt.empty(),rows:c}}t(h,!1)},[r,e,n,i,t]),u=At.useCallback((c,f,g)=>{c=c??e.columns,f!==void 0&&(c=c.add(f));let h;if(r==="exclusive"&&c.length>0)h={current:void 0,rows:mt.empty(),columns:c};else{const m=g&&n==="mixed",p=g&&i==="mixed";h={current:m?e.current:void 0,rows:p?e.rows:mt.empty(),columns:c}}t(h,!1)},[r,e,n,i,t]);return[l,s,u]}function O0(e,t,n,r,i){const o=d.useCallback(u=>{if(e===!0){const c=[];for(let f=u.y;f<u.y+u.height;f++){const g=[];for(let h=u.x;h<u.x+u.width;h++)h<0||f>=i?g.push({kind:te.Loading,allowOverlay:!1}):g.push(t([h,f]));c.push(g)}return c}return(e==null?void 0:e(u,r.signal))??[]},[r.signal,t,e,i]),a=e!==void 0?o:void 0,l=d.useCallback(u=>{if(a===void 0)return[];const c={...u,x:u.x-n};if(c.x<0){c.x=0,c.width--;const f=a(c,r.signal);return typeof f=="function"?async()=>(await f()).map(g=>[{kind:te.Loading,allowOverlay:!1},...g]):f.map(g=>[{kind:te.Loading,allowOverlay:!1},...g])}return a(c,r.signal)},[r.signal,a,n]);return[e!==void 0?l:void 0,a]}function P0(e){if(e.copyData!==void 0)return{formatted:e.copyData,rawValue:e.copyData,format:"string"};switch(e.kind){case te.Boolean:return{formatted:e.data===!0?"TRUE":e.data===!1?"FALSE":e.data===zs?"INDETERMINATE":"",rawValue:e.data,format:"boolean"};case te.Custom:return{formatted:e.copyData,rawValue:e.copyData,format:"string"};case te.Image:case te.Bubble:return{formatted:e.data,rawValue:e.data,format:"string-array"};case te.Drilldown:return{formatted:e.data.map(t=>t.text),rawValue:e.data.map(t=>t.text),format:"string-array"};case te.Text:return{formatted:e.displayData??e.data,rawValue:e.data,format:"string"};case te.Uri:return{formatted:e.displayData??e.data,rawValue:e.data,format:"url"};case te.Markdown:case te.RowID:return{formatted:e.data,rawValue:e.data,format:"string"};case te.Number:return{formatted:e.displayData,rawValue:e.data,format:"number"};case te.Loading:return{formatted:"#LOADING",rawValue:"",format:"string"};case te.Protected:return{formatted:"************",rawValue:"",format:"string"};default:ao()}}function L0(e,t){return e.map((r,i)=>{const o=t[i];return r.map(a=>a.span!==void 0&&a.span[0]!==o?{formatted:"",rawValue:"",format:"string"}:P0(a))})}function Iu(e,t){return(t?/[\t\n",]/:/[\t\n"]/).test(e)&&(e=`"${e.replace(/"/g,'""')}"`),e}function _0(e){var n;const t=[];for(const r of e){const i=[];for(const o of r)o.format==="url"?i.push(((n=o.rawValue)==null?void 0:n.toString())??""):o.format==="string-array"?i.push(o.formatted.map(a=>Iu(a,!0)).join(",")):i.push(Iu(o.formatted,!1));t.push(i.join("	"))}return t.join(`
`)}function us(e){return e.replace(/\t/g,"    ").replace(/ {2,}/g,t=>"<span> </span>".repeat(t.length))}function Tu(e){return'"'+e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")+'"'}function F0(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}function A0(e){var n;const t=[];t.push('<style type="text/css"><!--br {mso-data-placement:same-cell;}--></style>',"<table><tbody>");for(const r of e){t.push("<tr>");for(const i of r){const o=`gdg-format="${i.format}"`;i.format==="url"?t.push(`<td ${o}><a href="${i.rawValue}">${us(i.formatted)}</a></td>`):i.format==="string-array"?t.push(`<td ${o}><ol>${i.formatted.map((a,l)=>`<li gdg-raw-value=${Tu(i.rawValue[l])}>`+us(a)+"</li>").join("")}</ol></td>`):t.push(`<td gdg-raw-value=${Tu(((n=i.rawValue)==null?void 0:n.toString())??"")} ${o}>${us(i.formatted)}</td>`)}t.push("</tr>")}return t.push("</tbody></table>"),t.join("")}function H0(e,t){const n=L0(e,t),r=_0(n),i=A0(n);return{textPlain:r,textHtml:i}}function Du(e){var a;const t=document.createElement("html");t.innerHTML=e.replace(/&nbsp;/g," ");const n=t.querySelector("table");if(n===null)return;const r=[n],i=[];let o;for(;r.length>0;){const l=r.pop();if(l===void 0)break;if(l instanceof HTMLTableElement||l.nodeName==="TBODY")r.push(...[...l.children].reverse());else if(l instanceof HTMLTableRowElement)o!==void 0&&i.push(o),o=[],r.push(...[...l.children].reverse());else if(l instanceof HTMLTableCellElement){const s=l.cloneNode(!0),c=s.children.length===1&&s.children[0].nodeName==="P"?s.children[0]:null,f=(c==null?void 0:c.children.length)===1&&c.children[0].nodeName==="FONT",g=s.querySelectorAll("br");for(const p of g)p.replaceWith(`
`);const h=s.getAttribute("gdg-raw-value"),m=s.getAttribute("gdg-format")??"string";if(s.querySelector("a")!==null)o==null||o.push({rawValue:((a=s.querySelector("a"))==null?void 0:a.getAttribute("href"))??"",formatted:s.textContent??"",format:m});else if(s.querySelector("ol")!==null){const p=s.querySelectorAll("li");o==null||o.push({rawValue:[...p].map(v=>v.getAttribute("gdg-raw-value")??""),formatted:[...p].map(v=>v.textContent??""),format:"string-array"})}else if(h!==null)o==null||o.push({rawValue:F0(h),formatted:s.textContent??"",format:m});else{let p=s.textContent??"";f&&(p=p.replace(/\n(?!\n)/g,"")),o==null||o.push({rawValue:p??"",formatted:p??"",format:m})}}}return o!==void 0&&i.push(o),i}function z0(e,t,n,r,i){var l;const o=e;if(r==="allowPartial"||e.current===void 0||t===void 0)return e;let a=!1;do{if((e==null?void 0:e.current)===void 0)break;const s=(l=e.current)==null?void 0:l.range,u=[];if(s.width>2){const g=t({x:s.x,y:s.y,width:1,height:s.height},i.signal);if(typeof g=="function")return o;u.push(...g);const h=t({x:s.x+s.width-1,y:s.y,width:1,height:s.height},i.signal);if(typeof h=="function")return o;u.push(...h)}else{const g=t({x:s.x,y:s.y,width:s.width,height:s.height},i.signal);if(typeof g=="function")return o;u.push(...g)}let c=s.x-n,f=s.x+s.width-1-n;for(const g of u)for(const h of g)h.span!==void 0&&(c=Math.min(h.span[0],c),f=Math.max(h.span[1],f));c===s.x-n&&f===s.x+s.width-1-n?a=!0:e={current:{cell:e.current.cell??[0,0],range:{x:c+n,y:s.y,width:f-c+1,height:s.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!a);return e}function Ou(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function V0(e){let t;(function(l){l[l.None=0]="None",l[l.inString=1]="inString",l[l.inStringPostQuote=2]="inStringPostQuote"})(t||(t={}));const n=[];let r=[],i=0,o=t.None;e=e.replace(/\r\n/g,`
`);let a=0;for(const l of e){switch(o){case t.None:l==="	"||l===`
`?(r.push(e.slice(i,a)),i=a+1,l===`
`&&(n.push(r),r=[])):l==='"'&&(o=t.inString);break;case t.inString:l==='"'&&(o=t.inStringPostQuote);break;case t.inStringPostQuote:l==='"'?o=t.inString:((l==="	"||l===`
`)&&(r.push(Ou(e.slice(i,a))),i=a+1,l===`
`&&(n.push(r),r=[])),o=t.None);break}a++}return i<e.length&&r.push(Ou(e.slice(i,e.length))),n.push(r),n.map(l=>l.map(s=>({rawValue:s,formatted:s,format:"string"})))}function Pu(e,t,n){var l;const r=H0(e,t),i=s=>{var u;(u=window.navigator.clipboard)==null||u.writeText(s)},o=(s,u)=>{var c;return((c=window.navigator.clipboard)==null?void 0:c.write)===void 0?!1:(window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([s],{type:"text/plain"}),"text/html":new Blob([u],{type:"text/html"})})]),!0)},a=(s,u)=>{var c,f;try{if(n===void 0||n.clipboardData===null)throw new Error("No clipboard data");(c=n==null?void 0:n.clipboardData)==null||c.setData("text/plain",s),(f=n==null?void 0:n.clipboardData)==null||f.setData("text/html",u)}catch{o(s,u)||i(s)}};((l=window.navigator.clipboard)==null?void 0:l.write)!==void 0||(n==null?void 0:n.clipboardData)!==void 0?a(r.textPlain,r.textHtml):i(r.textPlain),n==null||n.preventDefault()}function Hd(e){return e!==!0}function Lu(e){return typeof e=="string"?e:`${e}px`}const N0=()=>e=>e.innerWidth,$0=()=>e=>e.innerHeight,B0=mn("div")({name:"Wrapper",class:"gdg-wmyidgi",propsAsIs:!1,vars:{"wmyidgi-0":[N0()],"wmyidgi-1":[$0()]}}),W0=e=>{const{inWidth:t,inHeight:n,children:r,...i}=e;return d.createElement(B0,{innerHeight:Lu(n),innerWidth:Lu(t),...i},r)},U0=2,q0=1300;function Y0(e,t,n){const r=At.useRef(0),[i,o]=e??[0,0];At.useEffect(()=>{if(i===0&&o===0){r.current=0;return}let a=!1,l=0;const s=u=>{var c;if(!a){if(l===0)l=u;else{const f=u-l;r.current=Math.min(1,r.current+f/q0);const g=r.current**1.618*f*U0;(c=t.current)==null||c.scrollBy(i*g,o*g),l=u,n==null||n()}window.requestAnimationFrame(s)}};return window.requestAnimationFrame(s),()=>{a=!0}},[t,i,o,n])}function X0({rowHeight:e,headerHeight:t,groupHeaderHeight:n,theme:r,overscrollX:i,overscrollY:o,scaleToRem:a,remSize:l}){const[s,u,c,f,g,h]=At.useMemo(()=>{if(!a||l===16)return[e,t,n,r,i,o];const m=l/16,p=e,v=Sd();return[typeof p=="number"?p*m:w=>Math.ceil(p(w)*m),Math.ceil(t*m),Math.ceil(n*m),{...r,headerIconSize:((r==null?void 0:r.headerIconSize)??v.headerIconSize)*m,cellHorizontalPadding:((r==null?void 0:r.cellHorizontalPadding)??v.cellHorizontalPadding)*m,cellVerticalPadding:((r==null?void 0:r.cellVerticalPadding)??v.cellVerticalPadding)*m},Math.ceil((i??0)*m),Math.ceil((o??0)*m)]},[n,t,i,o,l,e,a,r]);return{rowHeight:s,headerHeight:u,groupHeaderHeight:c,theme:f,overscrollX:g,overscrollY:h}}const Dr={downFill:!1,rightFill:!1,clear:!0,closeOverlay:!0,acceptOverlayDown:!0,acceptOverlayUp:!0,acceptOverlayLeft:!0,acceptOverlayRight:!0,copy:!0,paste:!0,cut:!0,search:!1,delete:!0,activateCell:!0,scrollToSelectedCell:!0,goToFirstCell:!0,goToFirstColumn:!0,goToFirstRow:!0,goToLastCell:!0,goToLastColumn:!0,goToLastRow:!0,goToNextPage:!0,goToPreviousPage:!0,selectToFirstCell:!0,selectToFirstColumn:!0,selectToFirstRow:!0,selectToLastCell:!0,selectToLastColumn:!0,selectToLastRow:!0,selectAll:!0,selectRow:!0,selectColumn:!0,goUpCell:!0,goRightCell:!0,goDownCell:!0,goLeftCell:!0,goUpCellRetainSelection:!0,goRightCellRetainSelection:!0,goDownCellRetainSelection:!0,goLeftCellRetainSelection:!0,selectGrowUp:!0,selectGrowRight:!0,selectGrowDown:!0,selectGrowLeft:!0};function ct(e,t){return e===!0?t:e===!1?"":e}function _u(e){const t=ca.value;return{activateCell:ct(e.activateCell," |Enter|shift+Enter"),clear:ct(e.clear,"any+Escape"),closeOverlay:ct(e.closeOverlay,"any+Escape"),acceptOverlayDown:ct(e.acceptOverlayDown,"Enter"),acceptOverlayUp:ct(e.acceptOverlayUp,"shift+Enter"),acceptOverlayLeft:ct(e.acceptOverlayLeft,"shift+Tab"),acceptOverlayRight:ct(e.acceptOverlayRight,"Tab"),copy:e.copy,cut:e.cut,delete:ct(e.delete,t?"Backspace|Delete":"Delete"),downFill:ct(e.downFill,"primary+_68"),scrollToSelectedCell:ct(e.scrollToSelectedCell,"primary+Enter"),goDownCell:ct(e.goDownCell,"ArrowDown"),goDownCellRetainSelection:ct(e.goDownCellRetainSelection,"alt+ArrowDown"),goLeftCell:ct(e.goLeftCell,"ArrowLeft|shift+Tab"),goLeftCellRetainSelection:ct(e.goLeftCellRetainSelection,"alt+ArrowLeft"),goRightCell:ct(e.goRightCell,"ArrowRight|Tab"),goRightCellRetainSelection:ct(e.goRightCellRetainSelection,"alt+ArrowRight"),goUpCell:ct(e.goUpCell,"ArrowUp"),goUpCellRetainSelection:ct(e.goUpCellRetainSelection,"alt+ArrowUp"),goToFirstCell:ct(e.goToFirstCell,"primary+Home"),goToFirstColumn:ct(e.goToFirstColumn,"Home|primary+ArrowLeft"),goToFirstRow:ct(e.goToFirstRow,"primary+ArrowUp"),goToLastCell:ct(e.goToLastCell,"primary+End"),goToLastColumn:ct(e.goToLastColumn,"End|primary+ArrowRight"),goToLastRow:ct(e.goToLastRow,"primary+ArrowDown"),goToNextPage:ct(e.goToNextPage,"PageDown"),goToPreviousPage:ct(e.goToPreviousPage,"PageUp"),paste:e.paste,rightFill:ct(e.rightFill,"primary+_82"),search:ct(e.search,"primary+f"),selectAll:ct(e.selectAll,"primary+a"),selectColumn:ct(e.selectColumn,"ctrl+ "),selectGrowDown:ct(e.selectGrowDown,"shift+ArrowDown"),selectGrowLeft:ct(e.selectGrowLeft,"shift+ArrowLeft"),selectGrowRight:ct(e.selectGrowRight,"shift+ArrowRight"),selectGrowUp:ct(e.selectGrowUp,"shift+ArrowUp"),selectRow:ct(e.selectRow,"shift+ "),selectToFirstCell:ct(e.selectToFirstCell,"primary+shift+Home"),selectToFirstColumn:ct(e.selectToFirstColumn,"primary+shift+ArrowLeft"),selectToFirstRow:ct(e.selectToFirstRow,"primary+shift+ArrowUp"),selectToLastCell:ct(e.selectToLastCell,"primary+shift+End"),selectToLastColumn:ct(e.selectToLastColumn,"primary+shift+ArrowRight"),selectToLastRow:ct(e.selectToLastRow,"primary+shift+ArrowDown")}}function j0(e){const t=rm(e);return At.useMemo(()=>{if(t===void 0)return _u(Dr);const n={...t,goToNextPage:(t==null?void 0:t.goToNextPage)??(t==null?void 0:t.pageDown)??Dr.goToNextPage,goToPreviousPage:(t==null?void 0:t.goToPreviousPage)??(t==null?void 0:t.pageUp)??Dr.goToPreviousPage,goToFirstCell:(t==null?void 0:t.goToFirstCell)??(t==null?void 0:t.first)??Dr.goToFirstCell,goToLastCell:(t==null?void 0:t.goToLastCell)??(t==null?void 0:t.last)??Dr.goToLastCell,selectToFirstCell:(t==null?void 0:t.selectToFirstCell)??(t==null?void 0:t.first)??Dr.selectToFirstCell,selectToLastCell:(t==null?void 0:t.selectToLastCell)??(t==null?void 0:t.last)??Dr.selectToLastCell};return _u({...Dr,...n})},[t])}function G0(e){function t(r,i,o){if(typeof r=="number")return{headerIndex:r,isCollapsed:!1,depth:i,path:o};const a={headerIndex:r.headerIndex,isCollapsed:r.isCollapsed,depth:i,path:o};return r.subGroups!==void 0&&(a.subGroups=r.subGroups.map((l,s)=>t(l,i+1,[...o,s])).sort((l,s)=>l.headerIndex-s.headerIndex)),a}return e.map((r,i)=>t(r,0,[i])).sort((r,i)=>r.headerIndex-i.headerIndex)}function Zs(e,t){const n=[];function r(a,l,s=!1){let u=l!==null?l-a.headerIndex:t-a.headerIndex;if(a.subGroups!==void 0&&(u=a.subGroups[0].headerIndex-a.headerIndex),u--,n.push({headerIndex:a.headerIndex,contentIndex:-1,skip:s,isCollapsed:a.isCollapsed,depth:a.depth,path:a.path,rows:u}),a.subGroups)for(let c=0;c<a.subGroups.length;c++){const f=c<a.subGroups.length-1?a.subGroups[c+1].headerIndex:l;r(a.subGroups[c],f,s||a.isCollapsed)}}const i=G0(e.groups);for(let a=0;a<i.length;a++){const l=a<i.length-1?i[a+1].headerIndex:null;r(i[a],l)}let o=0;for(const a of n)a.contentIndex=o,o+=a.rows;return n.filter(a=>a.skip===!1).map(a=>{const{skip:l,...s}=a;return s})}function fa(e,t){if(t===void 0||Zs.length===0)return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1};let n=e;for(const r of t){if(n===0)return{path:[...r.path,-1],originalIndex:r.headerIndex,isGroupHeader:!0,groupIndex:-1,contentIndex:-1,groupRows:r.rows};if(n--,!r.isCollapsed){if(n<r.rows)return{path:[...r.path,n],originalIndex:r.headerIndex+n,isGroupHeader:!1,groupIndex:n,contentIndex:r.contentIndex+n,groupRows:r.rows};n-=r.rows}}return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1}}function K0(e,t,n,r){const i=At.useMemo(()=>e===void 0?void 0:Zs(e,t),[e,t]),o=At.useMemo(()=>i===void 0?t:i.reduce((u,c)=>u+(c.isCollapsed?1:c.rows+1),0),[i,t]),a=At.useMemo(()=>e===void 0||typeof n=="number"&&e.height===n?n:u=>{const{isGroupHeader:c}=fa(u,i);return c?e.height:typeof n=="number"?n:n(u)},[i,e,n]),l=At.useCallback(u=>{if(i===void 0)return u;let c=u;for(const f of i){if(c===0)return;if(c--,!f.isCollapsed){if(c<f.rows)return f.contentIndex+c;c-=f.rows}}return u},[i]),s=Yr(r??(e==null?void 0:e.themeOverride),At.useCallback(u=>{if(e===void 0)return r==null?void 0:r(u,u,u);if(r===void 0&&(e==null?void 0:e.themeOverride)===void 0)return;const{isGroupHeader:c,contentIndex:f,groupIndex:g}=fa(u,i);return c?e.themeOverride:r==null?void 0:r(u,g,f)},[i,r,e]));return e===void 0?{rowHeight:a,rows:t,rowNumberMapper:l,getRowThemeOverride:s}:{rowHeight:a,rows:o,rowNumberMapper:l,getRowThemeOverride:s}}function Z0(e,t){const n=At.useMemo(()=>e===void 0?void 0:Zs(e,t),[e,t]);return{getRowGroupingForPath:Vd,updateRowGroupingByPath:zd,mapper:At.useCallback(r=>{if(typeof r=="number")return fa(r,n);const i=fa(r[1],n);return{...i,originalIndex:[r[0],i.originalIndex]}},[n])}}function zd(e,t,n){const[r,...i]=t;return i[0]===-1?e.map((o,a)=>a===r?{...o,...n}:o):e.map((o,a)=>a===r?{...o,subGroups:zd(o.subGroups??[],i,n)}:o)}function Vd(e,t){const[n,...r]=t;return r[0]===-1?e[n]:Vd(e[n].subGroups??[],r)}function J0(e,t){const[n]=d.useState(()=>({value:e,callback:t,facade:{get current(){return n.value},set current(r){const i=n.value;i!==r&&(n.value=r,n.callback(r,i))}}}));return n.callback=t,n.facade}function Q0(e,t,n,r,i){const[o,a]=d.useMemo(()=>[t!==void 0&&typeof n=="number"?Math.floor(t/n):0,t!==void 0&&typeof n=="number"?-(t%n):0],[t,n]),l=d.useMemo(()=>({x:r.current.x,y:o,width:r.current.width??1,height:r.current.height??1,ty:a}),[r,a,o]),[s,u,c]=nm(l),f=d.useRef(i);f.current=i;const g=J0(null,p=>{p!==null&&t!==void 0?p.scrollTop=t:p!==null&&e!==void 0&&(p.scrollLeft=e)}),h=(s.height??1)>1;d.useLayoutEffect(()=>{if(t!==void 0&&g.current!==null&&h){if(g.current.scrollTop===t)return;g.current.scrollTop=t,g.current.scrollTop!==t&&c(),f.current()}},[t,h,c,g]);const m=(s.width??1)>1;return d.useLayoutEffect(()=>{if(e!==void 0&&g.current!==null&&m){if(g.current.scrollLeft===e)return;g.current.scrollLeft=e,g.current.scrollLeft!==e&&c(),f.current()}},[e,m,c,g]),{visibleRegion:s,setVisibleRegion:u,scrollRef:g}}const ev=d.lazy(async()=>await Fs(()=>import("./data-grid-overlay-editor.CUwpDfvI.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url));let tv=0;function nv(e){return ap(yu(yu(e).filter(t=>t.span!==void 0).map(t=>{var n,r;return lr((((n=t.span)==null?void 0:n[0])??0)+1,(((r=t.span)==null?void 0:r[1])??0)+1)})))}function Wo(e,t){return e===void 0||t===0||e.columns.length===0&&e.current===void 0?e:{current:e.current===void 0?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map(n=>({...n,x:n.x+t}))},rows:e.rows,columns:e.columns.offset(t)}}const Uo={kind:te.Loading,allowOverlay:!1},qo={columns:mt.empty(),rows:mt.empty(),current:void 0},rv=(e,t)=>{var Il,Tl,Dl;const[n,r]=d.useState(qo),[i,o]=d.useState(),a=d.useRef(null),l=d.useRef(null),[s,u]=d.useState(),c=d.useRef(),f=typeof window>"u"?null:window,{imageEditorOverride:g,getRowThemeOverride:h,markdownDivCreateNode:m,width:p,height:v,columns:w,rows:b,getCellContent:M,onCellClicked:O,onCellActivated:S,onFillPattern:R,onFinishedEditing:L,coercePasteValue:E,drawHeader:x,drawCell:_,editorBloom:D,onHeaderClicked:C,onColumnProposeMove:I,rangeSelectionColumnSpanning:T=!0,spanRangeBehavior:k="default",onGroupHeaderClicked:z,onCellContextMenu:$,className:X,onHeaderContextMenu:re,getCellsForSelection:j,onGroupHeaderContextMenu:G,onGroupHeaderRenamed:ae,onCellEdited:ie,onCellsEdited:le,onSearchResultsChanged:fe,searchResults:Q,onSearchValueChange:H,searchValue:P,onKeyDown:W,onKeyUp:ce,keybindings:De,editOnType:He=!0,onRowAppended:ye,onColumnMoved:Re,validateCell:Se,highlightRegions:Et,rangeSelect:wt="rect",columnSelect:rt="multi",rowSelect:se="multi",rangeSelectionBlending:et="exclusive",columnSelectionBlending:me="exclusive",rowSelectionBlending:he="exclusive",onDelete:pe,onDragStart:ze,onMouseMove:Pe,onPaste:tt,copyHeaders:ve=!1,freezeColumns:ue=0,cellActivationBehavior:it="second-click",rowSelectionMode:Ae="auto",onHeaderMenuClick:Qe,onHeaderIndicatorClick:vt,getGroupDetails:ht,rowGrouping:Ve,onSearchClose:Dt,onItemHovered:Yt,onSelectionCleared:xt,showSearch:Lt,onVisibleRegionChanged:nn,gridSelection:zt,onGridSelectionChange:ln,minColumnWidth:It=50,maxColumnWidth:yt=500,maxColumnAutoWidth:Dn,provideEditor:yn,trailingRowOptions:Oe,freezeTrailingRows:_t=0,allowedFillDirections:fn="orthogonal",scrollOffsetX:hn,scrollOffsetY:pn,verticalBorder:at,onDragOverCell:Xt,onDrop:Ot,onColumnResize:rn,onColumnResizeEnd:Tt,onColumnResizeStart:Ft,customRenderers:Be,fillHandle:Bt,experimental:Pt,fixedShadowX:Vt,fixedShadowY:Nt,headerIcons:Cn,imageWindowLoader:nr,initialSize:Hn,isDraggable:V,onDragLeave:Je,onRowMoved:We,overscrollX:de,overscrollY:st,preventDiagonalScrolling:Wt,rightElement:dn,rightElementProps:Zt,trapFocus:Gt=!1,smoothScrollX:Sn,smoothScrollY:vn,scaleToRem:Cr=!1,rowHeight:Xn=34,headerHeight:vo=36,groupHeaderHeight:Ia=vo,theme:ei,isOutsideClick:Sr,renderers:ki,resizeIndicator:Ta,scrollToActiveCell:Mi=!0,drawFocusRing:Ri=!0}=e,Ar=Ri==="no-editor"?i===void 0:Ri,Qt=typeof e.rowMarkers=="string"?void 0:e.rowMarkers,Rn=(Qt==null?void 0:Qt.kind)??e.rowMarkers??"none",Ei=(Qt==null?void 0:Qt.width)??e.rowMarkerWidth,Ii=(Qt==null?void 0:Qt.startIndex)??e.rowMarkerStartIndex??1,ti=(Qt==null?void 0:Qt.theme)??e.rowMarkerTheme,fr=Qt==null?void 0:Qt.headerTheme,hr=Qt==null?void 0:Qt.headerAlwaysVisible,ni=se!=="multi",rr=(Qt==null?void 0:Qt.checkboxStyle)??"square",ir=Math.max(It,20),Hr=Math.max(yt,ir),bo=Math.max(Dn??Hr,ir),Ti=d.useMemo(()=>typeof window>"u"?{fontSize:"16px"}:window.getComputedStyle(document.documentElement),[]),{rows:je,rowNumberMapper:wo,rowHeight:yo,getRowThemeOverride:zn}=K0(Ve,b,Xn,h),Co=d.useMemo(()=>Number.parseFloat(Ti.fontSize),[Ti]),{rowHeight:jn,headerHeight:Di,groupHeaderHeight:So,theme:xo,overscrollX:Da,overscrollY:Oa}=X0({groupHeaderHeight:Ia,headerHeight:vo,overscrollX:de,overscrollY:st,remSize:Co,rowHeight:yo,scaleToRem:Cr,theme:ei}),Vn=j0(De),xr=Ei??(b>1e4?48:b>1e3?44:b>100?36:32),Nn=Rn!=="none",q=Nn?1:0,Kt=ye!==void 0,kr=(Oe==null?void 0:Oe.sticky)===!0,[ri,ii]=d.useState(!1),Oi=Lt??ri,Pa=d.useCallback(()=>{Dt!==void 0?Dt():ii(!1)},[Dt]),K=d.useMemo(()=>zt===void 0?void 0:Wo(zt,q),[zt,q])??n,un=d.useRef();un.current===void 0&&(un.current=new AbortController),d.useEffect(()=>()=>un==null?void 0:un.current.abort(),[]);const[cn,La]=O0(j,M,q,un.current,je),N=d.useCallback((y,A,F)=>{if(Se===void 0)return!0;const U=[y[0]-q,y[1]];return Se==null?void 0:Se(U,A,F)},[q,Se]),oe=d.useRef(zt),Ce=d.useCallback((y,A)=>{A&&(y=z0(y,cn,q,k,un.current)),ln!==void 0?(oe.current=Wo(y,-q),ln(oe.current)):r(y)},[ln,cn,q,k]),xe=Yr(rn,d.useCallback((y,A,F,U)=>{rn==null||rn(w[F-q],A,F-q,U)},[rn,q,w])),ge=Yr(Tt,d.useCallback((y,A,F,U)=>{Tt==null||Tt(w[F-q],A,F-q,U)},[Tt,q,w])),Me=Yr(Ft,d.useCallback((y,A,F,U)=>{Ft==null||Ft(w[F-q],A,F-q,U)},[Ft,q,w])),nt=Yr(x,d.useCallback((y,A)=>(x==null?void 0:x({...y,columnIndex:y.columnIndex-q},A))??!1,[x,q])),ot=Yr(_,d.useCallback((y,A)=>(_==null?void 0:_({...y,col:y.col-q},A))??!1,[_,q])),Ie=d.useCallback(y=>{if(pe!==void 0){const A=pe(Wo(y,-q));return typeof A=="boolean"?A:Wo(A,q)}return!0},[pe,q]),[kt,ke,Ge]=D0(K,Ce,et,me,he,wt,T),Ue=d.useMemo(()=>vr(Sd(),xo),[xo]),[ft,sn]=d.useState([0,0,0]),En=d.useMemo(()=>{if(ki===void 0)return{};const y={};for(const A of ki)y[A.kind]=A;return y},[ki]),Ct=d.useCallback(y=>y.kind!==te.Custom?En[y.kind]:Be==null?void 0:Be.find(A=>A.isMatch(y)),[Be,En]);let{sizedColumns:Jt,nonGrowWidth:Gn}=Zm(w,je,La,ft[0]-(q===0?0:xr)-ft[2],ir,bo,Ue,Ct,un.current);Rn!=="none"&&(Gn+=xr);const xn=d.useMemo(()=>Jt.some(y=>y.group!==void 0),[Jt]),Rt=xn?Di+So:Di,In=K.rows.length,On=Rn==="none"?void 0:In===0?!1:In===je?!0:void 0,St=d.useMemo(()=>Rn==="none"?Jt:[{title:"",width:xr,icon:void 0,hasMenu:!1,style:"normal",themeOverride:ti,rowMarker:rr,rowMarkerChecked:On,headerRowMarkerTheme:fr,headerRowMarkerAlwaysVisible:hr,headerRowMarkerDisabled:ni},...Jt],[Rn,Jt,xr,ti,rr,On,fr,hr,ni]),en=d.useRef({height:1,width:1,x:0,y:0}),Mr=d.useRef(!1),{setVisibleRegion:Rr,visibleRegion:zr,scrollRef:Ut}=Q0(hn,pn,jn,en,()=>Mr.current=!0);en.current=zr;const Ff=zr.x+q,ko=zr.y,Tn=d.useRef(null),kn=d.useCallback(y=>{var A;y===!0?(A=Tn.current)==null||A.focus():window.requestAnimationFrame(()=>{var F;(F=Tn.current)==null||F.focus()})},[]),bn=Kt?je+1:je,$n=d.useCallback(y=>{const A=q===0?y:y.map(U=>({...U,location:[U.location[0]-q,U.location[1]]})),F=le==null?void 0:le(A);if(F!==!0)for(const U of A)ie==null||ie(U.location,U.value);return F},[ie,le,q]),[Vr,_a]=d.useState(),Mo=K.current!==void 0&&K.current.range.width*K.current.range.height>1?K.current.range:void 0,oi=Ar?(Il=K.current)==null?void 0:Il.cell:void 0,Ro=oi==null?void 0:oi[0],Eo=oi==null?void 0:oi[1],Af=d.useMemo(()=>{if((Et===void 0||Et.length===0)&&(Mo??Ro??Eo??Vr)===void 0)return;const y=[];if(Et!==void 0)for(const A of Et){const F=St.length-A.range.x-q;F>0&&y.push({color:A.color,range:{...A.range,x:A.range.x+q,width:Math.min(F,A.range.width)},style:A.style})}return Vr!==void 0&&y.push({color:jr(Ue.accentColor,0),range:Vr,style:"dashed"}),Mo!==void 0&&y.push({color:jr(Ue.accentColor,.5),range:Mo,style:"solid-outline"}),Ro!==void 0&&Eo!==void 0&&y.push({color:Ue.accentColor,range:{x:Ro,y:Eo,width:1,height:1},style:"solid-outline"}),y.length>0?y:void 0},[Vr,Mo,Ro,Eo,Et,St.length,Ue.accentColor,q]),gl=d.useRef(St);gl.current=St;const Pn=d.useCallback(([y,A],F=!1)=>{var J,Y,Z,ee,ne,be,Le;const U=Kt&&A===bn-1;if(y===0&&Nn){if(U)return Uo;const we=wo(A);return we===void 0?Uo:{kind:Yn.Marker,allowOverlay:!1,checkboxStyle:rr,checked:(K==null?void 0:K.rows.hasIndex(A))===!0,markerKind:Rn==="clickable-number"?"number":Rn,row:Ii+we,drawHandle:We!==void 0,cursor:Rn==="clickable-number"?"pointer":void 0}}else if(U){const Fe=y===q?(Oe==null?void 0:Oe.hint)??"":"",Te=gl.current[y];if(((J=Te==null?void 0:Te.trailingRowOptions)==null?void 0:J.disabled)===!0)return Uo;{const gt=((Y=Te==null?void 0:Te.trailingRowOptions)==null?void 0:Y.hint)??Fe,qe=((Z=Te==null?void 0:Te.trailingRowOptions)==null?void 0:Z.addIcon)??(Oe==null?void 0:Oe.addIcon);return{kind:Yn.NewRow,hint:gt,allowOverlay:!1,icon:qe}}}else{const we=y-q;if(F||(Pt==null?void 0:Pt.strict)===!0){const Te=en.current,gt=Te.x>we||we>Te.x+Te.width||Te.y>A||A>Te.y+Te.height||A>=Aa.current,qe=we===((ne=(ee=Te.extras)==null?void 0:ee.selected)==null?void 0:ne[0])&&A===((be=Te.extras)==null?void 0:be.selected[1]);let _e=!1;if(((Le=Te.extras)==null?void 0:Le.freezeRegions)!==void 0){for(const Mt of Te.extras.freezeRegions)if(Xr(Mt,we,A)){_e=!0;break}}if(gt&&!qe&&!_e)return Uo}let Fe=M([we,A]);return q!==0&&Fe.span!==void 0&&(Fe={...Fe,span:[Fe.span[0]+q,Fe.span[1]+q]}),Fe}},[Kt,bn,Nn,wo,rr,K==null?void 0:K.rows,Rn,Ii,We,q,Oe==null?void 0:Oe.hint,Oe==null?void 0:Oe.addIcon,Pt==null?void 0:Pt.strict,M]),Fa=d.useCallback(y=>{let A=(ht==null?void 0:ht(y))??{name:y};return ae!==void 0&&y!==""&&(A={icon:A.icon,name:A.name,overrideTheme:A.overrideTheme,actions:[...A.actions??[],{title:"Rename",icon:"renameIcon",onClick:F=>za({group:A.name,bounds:F.bounds})}]}),A},[ht,ae]),Io=d.useCallback(y=>{var Z;const[A,F]=y.cell,U=St[A],B=(U==null?void 0:U.group)!==void 0?(Z=Fa(U.group))==null?void 0:Z.overrideTheme:void 0,J=U==null?void 0:U.themeOverride,Y=zn==null?void 0:zn(F);o({...y,theme:vr(Ue,B,J,Y,y.content.themeOverride)})},[zn,St,Fa,Ue]),ai=d.useCallback((y,A,F)=>{var Y;if(K.current===void 0)return;const[U,B]=K.current.cell,J=Pn([U,B]);if(J.kind!==te.Boolean&&J.allowOverlay){let Z=J;if(F!==void 0)switch(Z.kind){case te.Number:{const ee=Rg(()=>F==="-"?-0:Number.parseFloat(F),0);Z={...Z,data:Number.isNaN(ee)?0:ee};break}case te.Text:case te.Markdown:case te.Uri:Z={...Z,data:F};break}Io({target:y,content:Z,initialValue:F,cell:[U,B],highlight:F===void 0,forceEditMode:F!==void 0})}else J.kind===te.Boolean&&A&&J.readonly!==!0&&($n([{location:K.current.cell,value:{...J,data:Hd(J.data)}}]),(Y=Tn.current)==null||Y.damage([{cell:K.current.cell}]))},[Pn,K,$n,Io]),ml=d.useCallback((y,A)=>{var B;const F=(B=Tn.current)==null?void 0:B.getBounds(y,A);if(F===void 0||Ut.current===null)return;const U=Pn([y,A]);U.allowOverlay&&Io({target:F,content:U,initialValue:void 0,highlight:!0,cell:[y,A],forceEditMode:!0})},[Pn,Ut,Io]),on=d.useCallback((y,A,F="both",U=0,B=0,J=void 0)=>{if(Ut.current!==null){const Y=Tn.current,Z=l.current,ee=typeof y!="number"?y.unit==="cell"?y.amount:void 0:y,ne=typeof A!="number"?A.unit==="cell"?A.amount:void 0:A,be=typeof y!="number"&&y.unit==="px"?y.amount:void 0,Le=typeof A!="number"&&A.unit==="px"?A.amount:void 0;if(Y!==null&&Z!==null){let we={x:0,y:0,width:0,height:0},Fe=0,Te=0;if((ee!==void 0||ne!==void 0)&&(we=Y.getBounds((ee??0)+q,ne??0)??we,we.width===0||we.height===0))return;const gt=Z.getBoundingClientRect(),qe=gt.width/Z.offsetWidth;if(be!==void 0&&(we={...we,x:be-gt.left-Ut.current.scrollLeft,width:1}),Le!==void 0&&(we={...we,y:Le+gt.top-Ut.current.scrollTop,height:1}),we!==void 0){const _e={x:we.x-U,y:we.y-B,width:we.width+2*U,height:we.height+2*B};let Mt=0;for(let qa=0;qa<ue;qa++)Mt+=Jt[qa].width;let jt=0;const an=_t+(kr?1:0);an>0&&(jt=Jr(bn,an,jn));let Jn=Mt*qe+gt.left+q*xr*qe,ar=gt.right,Qn=gt.top+Rt*qe,sr=gt.bottom-jt*qe;const Fo=we.width+U*2;switch(J==null?void 0:J.hAlign){case"start":ar=Jn+Fo;break;case"end":Jn=ar-Fo;break;case"center":Jn=Math.floor((Jn+ar)/2)-Fo/2,ar=Jn+Fo;break}const Ao=we.height+B*2;switch(J==null?void 0:J.vAlign){case"start":sr=Qn+Ao;break;case"end":Qn=sr-Ao;break;case"center":Qn=Math.floor((Qn+sr)/2)-Ao/2,sr=Qn+Ao;break}Jn>_e.x?Fe=_e.x-Jn:ar<_e.x+_e.width&&(Fe=_e.x+_e.width-ar),Qn>_e.y?Te=_e.y-Qn:sr<_e.y+_e.height&&(Te=_e.y+_e.height-sr),F==="vertical"||typeof y=="number"&&y<ue?Fe=0:(F==="horizontal"||typeof A=="number"&&A>=bn-an)&&(Te=0),(Fe!==0||Te!==0)&&(qe!==1&&(Fe/=qe,Te/=qe),Ut.current.scrollTo(Fe+Ut.current.scrollLeft,Te+Ut.current.scrollTop))}}}},[q,_t,xr,Ut,Rt,ue,Jt,bn,kr,jn]),pl=d.useRef(ml),vl=d.useRef(M),Aa=d.useRef(je);pl.current=ml,vl.current=M,Aa.current=je;const si=d.useCallback(async(y,A=!0)=>{var ee;const F=St[y];if(((ee=F==null?void 0:F.trailingRowOptions)==null?void 0:ee.disabled)===!0)return;const U=ye==null?void 0:ye();let B,J=!0;U!==void 0&&(B=await U,B==="top"&&(J=!1),typeof B=="number"&&(J=!1));let Y=0;const Z=()=>{if(Aa.current<=je){Y<500&&window.setTimeout(Z,Y),Y=50+Y*2;return}const ne=typeof B=="number"?B:J?je:0;_o.current(y-q,ne),kt({cell:[y,ne],range:{x:y,y:ne,width:1,height:1}},!1,!1,"edit");const be=vl.current([y-q,ne]);be.allowOverlay&&Qi(be)&&be.readonly!==!0&&A&&window.setTimeout(()=>{pl.current(y,ne)},0)};Z()},[St,ye,q,je,kt]),To=d.useCallback(y=>{var F,U;const A=((U=(F=Jt[y])==null?void 0:F.trailingRowOptions)==null?void 0:U.targetColumn)??(Oe==null?void 0:Oe.targetColumn);if(typeof A=="number")return A+(Nn?1:0);if(typeof A=="object"){const B=w.indexOf(A);if(B>=0)return B+(Nn?1:0)}},[Jt,w,Nn,Oe==null?void 0:Oe.targetColumn]),Er=d.useRef(),li=d.useRef(),Li=d.useCallback((y,A)=>{var B;const[F,U]=A;return vr(Ue,(B=St[F])==null?void 0:B.themeOverride,zn==null?void 0:zn(U),y.themeOverride)},[zn,St,Ue]),{mapper:Nr}=Z0(Ve,b),Kn=Ve==null?void 0:Ve.navigationBehavior,_i=d.useCallback(y=>{var be,Le,we;const A=ca.value?y.metaKey:y.ctrlKey,F=A&&se==="multi",U=A&&rt==="multi",[B,J]=y.location,Y=K.columns,Z=K.rows,[ee,ne]=((be=K.current)==null?void 0:be.cell)??[];if(y.kind==="cell"){if(li.current=void 0,$r.current=[B,J],B===0&&Nn){if(Kt===!0&&J===je||Rn==="number"||se==="none")return;const Fe=Pn(y.location);if(Fe.kind!==Yn.Marker)return;if(We!==void 0){const qe=Ct(Fe);Fn((qe==null?void 0:qe.kind)===Yn.Marker);const _e=(Le=qe==null?void 0:qe.onClick)==null?void 0:Le.call(qe,{...y,cell:Fe,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,theme:Li(Fe,y.location),preventDefault:()=>{}});if(_e===void 0||_e.checked===Fe.checked)return}o(void 0),kn();const Te=Z.hasIndex(J),gt=Er.current;if(se==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&gt!==void 0&&Z.hasIndex(gt)){const qe=[Math.min(gt,J),Math.max(gt,J)+1];F||Ae==="multi"?ke(void 0,qe,!0):ke(mt.fromSingleSelection(qe),void 0,F)}else se==="multi"&&(F||y.isTouch||Ae==="multi")?Te?ke(Z.remove(J),void 0,!0):(ke(void 0,J,!0),Er.current=J):Te&&Z.length===1?ke(mt.empty(),void 0,A):(ke(mt.fromSingleSelection(J),void 0,A),Er.current=J)}else if(B>=q&&Kt&&J===je){const Fe=To(B);si(Fe??B)}else if(ee!==B||ne!==J){const Fe=Pn(y.location),Te=Ct(Fe);if((Te==null?void 0:Te.onSelect)!==void 0){let _e=!1;if(Te.onSelect({...y,cell:Fe,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,preventDefault:()=>_e=!0,theme:Li(Fe,y.location)}),_e)return}if(Kn==="block"&&Nr(J).isGroupHeader)return;const gt=kr&&J===je,qe=kr&&K!==void 0&&((we=K.current)==null?void 0:we.cell[1])===je;if((y.shiftKey||y.isLongTouch===!0)&&ee!==void 0&&ne!==void 0&&K.current!==void 0&&!qe){if(gt)return;const _e=Math.min(B,ee),Mt=Math.max(B,ee),jt=Math.min(J,ne),an=Math.max(J,ne);kt({...K.current,range:{x:_e,y:jt,width:Mt-_e+1,height:an-jt+1}},!0,A,"click"),Er.current=void 0,kn()}else kt({cell:[B,J],range:{x:B,y:J,width:1,height:1}},!0,A,"click"),Er.current=void 0,o(void 0),kn()}}else if(y.kind==="header")if($r.current=[B,J],o(void 0),Nn&&B===0)Er.current=void 0,li.current=void 0,se==="multi"&&(Z.length!==je?ke(mt.fromSingleSelection([0,je]),void 0,A):ke(mt.empty(),void 0,A),kn());else{const Fe=li.current;if(rt==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&Fe!==void 0&&Y.hasIndex(Fe)){const Te=[Math.min(Fe,B),Math.max(Fe,B)+1];U?Ge(void 0,Te,A):Ge(mt.fromSingleSelection(Te),void 0,A)}else U?(Y.hasIndex(B)?Ge(Y.remove(B),void 0,A):Ge(void 0,B,A),li.current=B):rt!=="none"&&(Ge(mt.fromSingleSelection(B),void 0,A),li.current=B);Er.current=void 0,kn()}else y.kind===qn?$r.current=[B,J]:y.kind===da&&!y.isMaybeScrollbar&&(Ce(qo,!1),o(void 0),kn(),xt==null||xt(),Er.current=void 0,li.current=void 0)},[se,rt,K,Nn,q,Kt,je,Rn,Pn,We,kn,Ae,Ct,Li,ke,To,si,Kn,Nr,kr,kt,Ge,Ce,xt]),Fi=d.useRef(!1),$r=d.useRef(),bl=d.useRef(zr),Bn=d.useRef(),Hf=d.useCallback(y=>{if(ui.current=!1,bl.current=en.current,y.button!==0&&y.button!==1){Bn.current=void 0;return}const A=performance.now();Bn.current={button:y.button,time:A,location:y.location},(y==null?void 0:y.kind)==="header"&&(Fi.current=!0);const F=y.kind==="cell"&&y.isFillHandle;!F&&y.kind!=="cell"&&y.isEdge||(u({previousSelection:K,fillHandle:F}),$r.current=void 0,!y.isTouch&&y.button===0&&!F?_i(y):!y.isTouch&&y.button===1&&($r.current=y.location))},[K,_i]),[Ha,za]=d.useState(),wl=d.useCallback(y=>{if(y.kind!==qn||rt!=="multi")return;const A=ca.value?y.metaKey:y.ctrlKey,[F]=y.location,U=K.columns;if(F<q)return;const B=St[F];let J=F,Y=F;for(let Z=F-1;Z>=q&&so(B.group,St[Z].group);Z--)J--;for(let Z=F+1;Z<St.length&&so(B.group,St[Z].group);Z++)Y++;if(kn(),A)if(U.hasAll([J,Y+1])){let Z=U;for(let ee=J;ee<=Y;ee++)Z=Z.remove(ee);Ge(Z,void 0,A)}else Ge(void 0,[J,Y+1],A);else Ge(mt.fromSingleSelection([J,Y+1]),void 0,A)},[rt,kn,K.columns,St,q,Ge]),ui=d.useRef(!1),Do=d.useCallback(async y=>{if(cn!==void 0&&xe!==void 0){const A=en.current.y,F=en.current.height;let U=cn({x:y,y:A,width:1,height:Math.min(F,je-A)},un.current.signal);typeof U!="object"&&(U=await U());const B=Jt[y-q],Y=document.createElement("canvas").getContext("2d",{alpha:!1});if(Y!==null){Y.font=Ue.baseFontFull;const Z=kd(Y,Ue,B,0,U,ir,Hr,!1,Ct);xe==null||xe(B,Z.width,y,Z.width)}}},[Jt,cn,Hr,Ue,ir,xe,q,je,Ct]),[zf,Va]=d.useState(),ci=d.useCallback(async(y,A)=>{var Z,ee;const F=(Z=y.current)==null?void 0:Z.range;if(F===void 0||cn===void 0||A.current===void 0)return;const U=A.current.range;if(R!==void 0){let ne=!1;if(R({fillDestination:{...U,x:U.x-q},patternSource:{...F,x:F.x-q},preventDefault:()=>ne=!0}),ne)return}let B=cn(F,un.current.signal);typeof B!="object"&&(B=await B());const J=B,Y=[];for(let ne=0;ne<U.width;ne++)for(let be=0;be<U.height;be++){const Le=[U.x+ne,U.y+be];if(fd(Le,F))continue;const we=J[be%F.height][ne%F.width];vi(we)||!Qi(we)||Y.push({location:Le,value:{...we}})}$n(Y),(ee=Tn.current)==null||ee.damage(Y.map(ne=>({cell:ne.location})))},[cn,$n,R,q]),yl=d.useCallback(()=>{if(K.current===void 0||K.current.range.width<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,width:1}}};ci(y,K)},[ci,K]),Cl=d.useCallback(()=>{if(K.current===void 0||K.current.range.height<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,height:1}}};ci(y,K)},[ci,K]),Vf=d.useCallback((y,A)=>{var be,Le;const F=s;if(u(void 0),_a(void 0),Va(void 0),Fi.current=!1,A)return;if((F==null?void 0:F.fillHandle)===!0&&K.current!==void 0&&((be=F.previousSelection)==null?void 0:be.current)!==void 0){if(Vr===void 0)return;const we={...K,current:{...K.current,range:Td(F.previousSelection.current.range,Vr)}};ci(F.previousSelection,we),Ce(we,!0);return}const[U,B]=y.location,[J,Y]=$r.current??[],Z=()=>{ui.current=!0},ee=we=>{var Te,gt,qe;const Fe=we.isTouch||J===U&&Y===B;if(Fe&&(O==null||O([U-q,B],{...we,preventDefault:Z})),we.button===1)return!ui.current;if(!ui.current){const _e=Pn(y.location),Mt=Ct(_e);if(Mt!==void 0&&Mt.onClick!==void 0&&Fe){const an=Mt.onClick({...we,cell:_e,posX:we.localEventX,posY:we.localEventY,bounds:we.bounds,theme:Li(_e,y.location),preventDefault:Z});an!==void 0&&!vi(an)&&mi(an)&&($n([{location:we.location,value:an}]),(Te=Tn.current)==null||Te.damage([{cell:we.location}]))}if(ui.current||K.current===void 0)return!1;let jt=!1;switch(_e.activationBehaviorOverride??it){case"double-click":case"second-click":{if(((qe=(gt=F==null?void 0:F.previousSelection)==null?void 0:gt.current)==null?void 0:qe.cell)===void 0)break;const[an,Jn]=K.current.cell,[ar,Qn]=F.previousSelection.current.cell;jt=U===an&&U===ar&&B===Jn&&B===Qn&&(we.isDoubleClick===!0||it==="second-click");break}case"single-click":{jt=!0;break}}if(jt)return S==null||S([U-q,B]),ai(we.bounds,!1),!0}return!1},ne=y.location[0]-q;if(y.isTouch){const we=en.current,Fe=bl.current;if(we.x!==Fe.x||we.y!==Fe.y)return;if(y.isLongTouch===!0){if(y.kind==="cell"&&no((Le=K.current)==null?void 0:Le.cell,y.location)){$==null||$([ne,y.location[1]],{...y,preventDefault:Z});return}else if(y.kind==="header"&&K.columns.hasIndex(U)){re==null||re(ne,{...y,preventDefault:Z});return}else if(y.kind===qn){if(ne<0)return;G==null||G(ne,{...y,preventDefault:Z});return}}y.kind==="cell"?ee(y)||_i(y):y.kind===qn?z==null||z(ne,{...y,preventDefault:Z}):(y.kind===Or&&(C==null||C(ne,{...y,preventDefault:Z})),_i(y));return}if(y.kind==="header"){if(ne<0)return;y.isEdge?y.isDoubleClick===!0&&Do(U):y.button===0&&U===J&&B===Y&&(C==null||C(ne,{...y,preventDefault:Z}))}if(y.kind===qn){if(ne<0)return;y.button===0&&U===J&&B===Y&&(z==null||z(ne,{...y,preventDefault:Z}),ui.current||wl(y))}y.kind==="cell"&&(y.button===0||y.button===1)&&ee(y),$r.current=void 0},[s,K,q,Vr,ci,Ce,O,Pn,Ct,it,Li,$n,S,ai,$,re,G,_i,z,C,Do,wl]),Nf=d.useCallback(y=>{const A={...y,location:[y.location[0]-q,y.location[1]]};Pe==null||Pe(A),s!==void 0&&y.buttons===0&&(u(void 0),_a(void 0),Va(void 0),Fi.current=!1),Va(F=>{var U;return Fi.current?[y.scrollEdge[0],0]:y.scrollEdge[0]===(F==null?void 0:F[0])&&y.scrollEdge[1]===F[1]?F:s===void 0||(((U=Bn.current)==null?void 0:U.location[0])??0)<q?void 0:y.scrollEdge})},[s,Pe,q]),$f=d.useCallback((y,A)=>{Qe==null||Qe(y-q,A)},[Qe,q]),Bf=d.useCallback((y,A)=>{vt==null||vt(y-q,A)},[vt,q]),or=(Tl=K==null?void 0:K.current)==null?void 0:Tl.cell,Wf=d.useCallback((y,A,F,U,B,J)=>{Mr.current=!1;let Y=or;Y!==void 0&&(Y=[Y[0]-q,Y[1]]);const Z=ue===0?void 0:{x:0,y:y.y,width:ue,height:y.height},ee=[];Z!==void 0&&ee.push(Z),_t>0&&(ee.push({x:y.x-q,y:je-_t,width:y.width,height:_t}),ue>0&&ee.push({x:0,y:je-_t,width:ue,height:_t}));const ne={x:y.x-q,y:y.y,width:y.width,height:Kt&&y.y+y.height>=je?y.height-1:y.height,tx:B,ty:J,extras:{selected:Y,freezeRegion:Z,freezeRegions:ee}};en.current=ne,Rr(ne),sn([A,F,U]),nn==null||nn(ne,ne.tx,ne.ty,ne.extras)},[or,q,Kt,je,ue,_t,Rr,nn]),Uf=Yr(Re,d.useCallback((y,A)=>{Re==null||Re(y-q,A-q),rt!=="none"&&Ge(mt.fromSingleSelection(A),void 0,!0)},[rt,Re,q,Ge])),Na=d.useRef(!1),qf=d.useCallback(y=>{if(y.location[0]===0&&q>0){y.preventDefault();return}ze==null||ze({...y,location:[y.location[0]-q,y.location[1]]}),y.defaultPrevented()||(Na.current=!0),u(void 0)},[ze,q]),Yf=d.useCallback(()=>{Na.current=!1},[]),Sl=Ve==null?void 0:Ve.selectionBehavior,Oo=d.useCallback(y=>{if(Sl!=="block-spanning")return;const{isGroupHeader:A,path:F,groupRows:U}=Nr(y);if(A)return[y,y];const B=F[F.length-1],J=y-B,Y=y+U-B-1;return[J,Y]},[Nr,Sl]),$a=d.useRef(),Ba=d.useCallback(y=>{var A,F,U;if(!Fd(y,$a.current)&&($a.current=y,!(((A=Bn==null?void 0:Bn.current)==null?void 0:A.button)!==void 0&&Bn.current.button>=1))){if(y.buttons!==0&&s!==void 0&&((F=Bn.current)==null?void 0:F.location[0])===0&&y.location[0]===0&&q===1&&se==="multi"&&s.previousSelection&&!s.previousSelection.rows.hasIndex(Bn.current.location[1])&&K.rows.hasIndex(Bn.current.location[1])){const B=Math.min(Bn.current.location[1],y.location[1]),J=Math.max(Bn.current.location[1],y.location[1])+1;ke(mt.fromSingleSelection([B,J]),void 0,!1)}if(y.buttons!==0&&s!==void 0&&K.current!==void 0&&!Na.current&&!Fi.current&&(wt==="rect"||wt==="multi-rect")){const[B,J]=K.current.cell;let[Y,Z]=y.location;if(Z<0&&(Z=en.current.y),s.fillHandle===!0&&((U=s.previousSelection)==null?void 0:U.current)!==void 0){const ee=s.previousSelection.current.range;Z=Math.min(Z,Kt?je-1:je);const ne=Vp(ee,Y,Z,fn);_a(ne)}else{if(Kt&&J===je)return;if(Kt&&Z===je)if(y.kind===da)Z--;else return;Y=Math.max(Y,q);const be=Oo(J);Z=be===void 0?Z:Un(Z,be[0],be[1]);const Le=Y-B,we=Z-J,Fe={x:Le>=0?B:Y,y:we>=0?J:Z,width:Math.abs(Le)+1,height:Math.abs(we)+1};kt({...K.current,range:Fe},!0,!1,"drag")}}Yt==null||Yt({...y,location:[y.location[0]-q,y.location[1]]})}},[s,q,se,K,wt,Yt,ke,Kt,je,fn,Oo,kt]),Xf=d.useCallback(()=>{var Y,Z;const y=$a.current;if(y===void 0)return;const[A,F]=y.scrollEdge;let[U,B]=y.location;const J=en.current;A===-1?U=((Z=(Y=J.extras)==null?void 0:Y.freezeRegion)==null?void 0:Z.x)??J.x:A===1&&(U=J.x+J.width),F===-1?B=Math.max(0,J.y):F===1&&(B=Math.min(je-1,J.y+J.height)),U=Un(U,0,St.length-1),B=Un(B,0,je-1),Ba({...y,location:[U,B]})},[St.length,Ba,je]);Y0(zf,Ut,Xf);const Zn=d.useCallback(y=>{if(K.current===void 0)return;const[A,F]=y,[U,B]=K.current.cell,J=K.current.range;let Y=J.x,Z=J.x+J.width,ee=J.y,ne=J.y+J.height;const[be,Le]=Oo(B)??[0,je-1],we=Le+1;if(F!==0)switch(F){case 2:{ne=we,ee=B,on(0,ne,"vertical");break}case-2:{ee=be,ne=B+1,on(0,ee,"vertical");break}case 1:{ee<B?(ee++,on(0,ee,"vertical")):(ne=Math.min(we,ne+1),on(0,ne,"vertical"));break}case-1:{ne>B+1?(ne--,on(0,ne,"vertical")):(ee=Math.max(be,ee-1),on(0,ee,"vertical"));break}default:ao()}if(A!==0)if(A===2)Z=St.length,Y=U,on(Z-1-q,0,"horizontal");else if(A===-2)Y=q,Z=U+1,on(Y-q,0,"horizontal");else{let Fe=[];if(cn!==void 0){const Te=cn({x:Y,y:ee,width:Z-Y-q,height:ne-ee},un.current.signal);typeof Te=="object"&&(Fe=nv(Te))}if(A===1){let Te=!1;if(Y<U){if(Fe.length>0){const gt=lr(Y+1,U+1).find(qe=>!Fe.includes(qe-q));gt!==void 0&&(Y=gt,Te=!0)}else Y++,Te=!0;Te&&on(Y,0,"horizontal")}Te||(Z=Math.min(St.length,Z+1),on(Z-1-q,0,"horizontal"))}else if(A===-1){let Te=!1;if(Z>U+1){if(Fe.length>0){const gt=lr(Z-1,U,-1).find(qe=>!Fe.includes(qe-q));gt!==void 0&&(Z=gt,Te=!0)}else Z--,Te=!0;Te&&on(Z-q,0,"horizontal")}Te||(Y=Math.max(q,Y-1),on(Y-q,0,"horizontal"))}else ao()}kt({cell:K.current.cell,range:{x:Y,y:ee,width:Z-Y,height:ne-ee}},!0,!1,"keyboard-select")},[cn,Oo,K,St.length,q,je,on,kt]),Wa=d.useRef(Mi);Wa.current=Mi;const Ir=d.useCallback((y,A,F,U)=>{const B=bn-(F?0:1);y=Un(y,q,Jt.length-1+q),A=Un(A,0,B);const J=or==null?void 0:or[0],Y=or==null?void 0:or[1];if(y===J&&A===Y)return!1;if(U&&K.current!==void 0){const Z=[...K.current.rangeStack];(K.current.range.width>1||K.current.range.height>1)&&Z.push(K.current.range),Ce({...K,current:{cell:[y,A],range:{x:y,y:A,width:1,height:1},rangeStack:Z}},!0)}else kt({cell:[y,A],range:{x:y,y:A,width:1,height:1}},!0,!1,"keyboard-nav");return c.current!==void 0&&c.current[0]===y&&c.current[1]===A&&(c.current=void 0),Wa.current&&on(y-q,A),!0},[bn,q,Jt.length,or,K,on,Ce,kt]),jf=d.useCallback((y,A)=>{(i==null?void 0:i.cell)!==void 0&&y!==void 0&&mi(y)&&($n([{location:i.cell,value:y}]),window.requestAnimationFrame(()=>{var B;(B=Tn.current)==null||B.damage([{cell:i.cell}])})),kn(!0),o(void 0);const[F,U]=A;if(K.current!==void 0&&(F!==0||U!==0)){const B=K.current.cell[1]===bn-1&&y!==void 0;Ir(Un(K.current.cell[0]+F,0,St.length-1),Un(K.current.cell[1]+U,0,bn-1),B,!1)}L==null||L(y,A)},[i==null?void 0:i.cell,kn,K,L,$n,bn,Ir,St.length]),Gf=d.useMemo(()=>`gdg-overlay-${tv++}`,[]),Br=d.useCallback(y=>{var F,U,B,J;kn();const A=[];for(let Y=y.x;Y<y.x+y.width;Y++)for(let Z=y.y;Z<y.y+y.height;Z++){const ee=M([Y-q,Z]);if(!ee.allowOverlay&&ee.kind!==te.Boolean)continue;let ne;if(ee.kind===te.Custom){const be=Ct(ee),Le=(F=be==null?void 0:be.provideEditor)==null?void 0:F.call(be,ee);(be==null?void 0:be.onDelete)!==void 0?ne=be.onDelete(ee):Og(Le)&&(ne=(U=Le==null?void 0:Le.deletedValue)==null?void 0:U.call(Le,ee))}else if(mi(ee)&&ee.allowOverlay||ee.kind===te.Boolean){const be=Ct(ee);ne=(B=be==null?void 0:be.onDelete)==null?void 0:B.call(be,ee)}ne!==void 0&&!vi(ne)&&mi(ne)&&A.push({location:[Y,Z],value:ne})}$n(A),(J=Tn.current)==null||J.damage(A.map(Y=>({cell:Y.location})))},[kn,M,Ct,$n,q]),Ai=i!==void 0,xl=d.useCallback(y=>{var gt,qe;const A=()=>{y.stopPropagation(),y.preventDefault()},F={didMatch:!1},{bounds:U}=y,B=K.columns,J=K.rows,Y=Vn;if(!Ai&&ut(Y.clear,y,F))Ce(qo,!1),xt==null||xt();else if(!Ai&&ut(Y.selectAll,y,F))Ce({columns:mt.empty(),rows:mt.empty(),current:{cell:((gt=K.current)==null?void 0:gt.cell)??[q,0],range:{x:q,y:0,width:w.length,height:je},rangeStack:[]}},!1);else if(ut(Y.search,y,F))(qe=a==null?void 0:a.current)==null||qe.focus({preventScroll:!0}),ii(!0);else if(ut(Y.delete,y,F)){const _e=(Ie==null?void 0:Ie(K))??!0;if(_e!==!1){const Mt=_e===!0?K:_e;if(Mt.current!==void 0){Br(Mt.current.range);for(const jt of Mt.current.rangeStack)Br(jt)}for(const jt of Mt.rows)Br({x:q,y:jt,width:w.length,height:1});for(const jt of Mt.columns)Br({x:jt,y:0,width:1,height:je})}}if(F.didMatch)return A(),!0;if(K.current===void 0)return!1;let[Z,ee]=K.current.cell;const[,ne]=K.current.cell;let be=!1,Le=!1;if(ut(Y.scrollToSelectedCell,y,F)?_o.current(Z-q,ee):rt!=="none"&&ut(Y.selectColumn,y,F)?B.hasIndex(Z)?Ge(B.remove(Z),void 0,!0):rt==="single"?Ge(mt.fromSingleSelection(Z),void 0,!0):Ge(void 0,Z,!0):se!=="none"&&ut(Y.selectRow,y,F)?J.hasIndex(ee)?ke(J.remove(ee),void 0,!0):se==="single"?ke(mt.fromSingleSelection(ee),void 0,!0):ke(void 0,ee,!0):!Ai&&U!==void 0&&ut(Y.activateCell,y,F)?ee===je&&Kt?window.setTimeout(()=>{const _e=To(Z);si(_e??Z)},0):(S==null||S([Z-q,ee]),ai(U,!0)):K.current.range.height>1&&ut(Y.downFill,y,F)?Cl():K.current.range.width>1&&ut(Y.rightFill,y,F)?yl():ut(Y.goToNextPage,y,F)?ee+=Math.max(1,en.current.height-4):ut(Y.goToPreviousPage,y,F)?ee-=Math.max(1,en.current.height-4):ut(Y.goToFirstCell,y,F)?(o(void 0),ee=0,Z=0):ut(Y.goToLastCell,y,F)?(o(void 0),ee=Number.MAX_SAFE_INTEGER,Z=Number.MAX_SAFE_INTEGER):ut(Y.selectToFirstCell,y,F)?(o(void 0),Zn([-2,-2])):ut(Y.selectToLastCell,y,F)?(o(void 0),Zn([2,2])):Ai?(ut(Y.closeOverlay,y,F)&&o(void 0),ut(Y.acceptOverlayDown,y,F)&&(o(void 0),ee++),ut(Y.acceptOverlayUp,y,F)&&(o(void 0),ee--),ut(Y.acceptOverlayLeft,y,F)&&(o(void 0),Z--),ut(Y.acceptOverlayRight,y,F)&&(o(void 0),Z++)):(ut(Y.goDownCell,y,F)?ee+=1:ut(Y.goUpCell,y,F)?ee-=1:ut(Y.goRightCell,y,F)?Z+=1:ut(Y.goLeftCell,y,F)?Z-=1:ut(Y.goDownCellRetainSelection,y,F)?(ee+=1,be=!0):ut(Y.goUpCellRetainSelection,y,F)?(ee-=1,be=!0):ut(Y.goRightCellRetainSelection,y,F)?(Z+=1,be=!0):ut(Y.goLeftCellRetainSelection,y,F)?(Z-=1,be=!0):ut(Y.goToLastRow,y,F)?ee=je-1:ut(Y.goToFirstRow,y,F)?ee=Number.MIN_SAFE_INTEGER:ut(Y.goToLastColumn,y,F)?Z=Number.MAX_SAFE_INTEGER:ut(Y.goToFirstColumn,y,F)?Z=Number.MIN_SAFE_INTEGER:(wt==="rect"||wt==="multi-rect")&&(ut(Y.selectGrowDown,y,F)?Zn([0,1]):ut(Y.selectGrowUp,y,F)?Zn([0,-1]):ut(Y.selectGrowRight,y,F)?Zn([1,0]):ut(Y.selectGrowLeft,y,F)?Zn([-1,0]):ut(Y.selectToLastRow,y,F)?Zn([0,2]):ut(Y.selectToFirstRow,y,F)?Zn([0,-2]):ut(Y.selectToLastColumn,y,F)?Zn([2,0]):ut(Y.selectToFirstColumn,y,F)&&Zn([-2,0])),Le=F.didMatch),Kn!==void 0&&Kn!=="normal"&&ee!==ne){const _e=Kn==="skip-up"||Kn==="skip"||Kn==="block",Mt=Kn==="skip-down"||Kn==="skip"||Kn==="block",jt=ee<ne;if(jt&&_e){for(;ee>=0&&Nr(ee).isGroupHeader;)ee--;ee<0&&(ee=ne)}else if(!jt&&Mt){for(;ee<je&&Nr(ee).isGroupHeader;)ee++;ee>=je&&(ee=ne)}}const Fe=Ir(Z,ee,!1,be),Te=F.didMatch;return Te&&(Fe||!Le||Gt)&&A(),Te},[Kn,Ai,K,Vn,rt,se,wt,q,Nr,je,Ir,Ce,xt,w.length,Ie,Gt,Br,Ge,ke,Kt,To,si,S,ai,Cl,yl,Zn]),Hi=d.useCallback(y=>{let A=!1;if(W!==void 0&&W({...y,cancel:()=>{A=!0}}),A||xl(y)||K.current===void 0)return;const[F,U]=K.current.cell,B=en.current;if(He&&!y.metaKey&&!y.ctrlKey&&K.current!==void 0&&y.key.length===1&&/[ -~]/g.test(y.key)&&y.bounds!==void 0&&Qi(M([F-q,Math.max(0,Math.min(U,je-1))]))){if((!Kt||U!==je)&&(B.y>U||U>B.y+B.height||B.x>F||F>B.x+B.width))return;ai(y.bounds,!0,y.key),y.stopPropagation(),y.preventDefault()}},[He,W,xl,K,M,q,je,Kt,ai]),Kf=d.useCallback((y,A)=>{const F=y.location[0]-q;if(y.kind==="header"&&(re==null||re(F,{...y,preventDefault:A})),y.kind===qn){if(F<0)return;G==null||G(F,{...y,preventDefault:A})}if(y.kind==="cell"){const[U,B]=y.location;$==null||$([F,B],{...y,preventDefault:A}),_m(K,y.location)||Ir(U,B,!1,!1)}},[K,$,G,re,q,Ir]),Ua=d.useCallback(async y=>{var Y,Z,ee;if(!Vn.paste)return;function A(ne,be,Le,we){var Te,gt;const Fe=typeof Le=="object"?(Le==null?void 0:Le.join(`
`))??"":(Le==null?void 0:Le.toString())??"";if(!vi(ne)&&Qi(ne)&&ne.readonly!==!0){const qe=E==null?void 0:E(Fe,ne);if(qe!==void 0&&mi(qe))return{location:be,value:qe};const _e=Ct(ne);if(_e===void 0)return;if(_e.kind===te.Custom){Fn(ne.kind===te.Custom);const Mt=(Te=_e.onPaste)==null?void 0:Te.call(_e,Fe,ne.data);return Mt===void 0?void 0:{location:be,value:{...ne,data:Mt}}}else{const Mt=(gt=_e.onPaste)==null?void 0:gt.call(_e,Fe,ne,{formatted:we,formattedString:typeof we=="string"?we:we==null?void 0:we.join(`
`),rawValue:Le});return Mt===void 0?void 0:(Fn(Mt.kind===ne.kind),{location:be,value:Mt})}}}const F=K.columns,U=K.rows,B=((Y=Ut.current)==null?void 0:Y.contains(document.activeElement))===!0||((Z=l.current)==null?void 0:Z.contains(document.activeElement))===!0;let J;if(K.current!==void 0?J=[K.current.range.x,K.current.range.y]:F.length===1?J=[F.first()??0,0]:U.length===1&&(J=[q,U.first()??0]),B&&J!==void 0){let ne,be;const Le="text/plain",we="text/html";if(navigator.clipboard.read!==void 0){const qe=await navigator.clipboard.read();for(const _e of qe){if(_e.types.includes(we)){const jt=await(await _e.getType(we)).text(),an=Du(jt);if(an!==void 0){ne=an;break}}_e.types.includes(Le)&&(be=await(await _e.getType(Le)).text())}}else if(navigator.clipboard.readText!==void 0)be=await navigator.clipboard.readText();else if(y!==void 0&&(y==null?void 0:y.clipboardData)!==null){if(y.clipboardData.types.includes(we)){const qe=y.clipboardData.getData(we);ne=Du(qe)}ne===void 0&&y.clipboardData.types.includes(Le)&&(be=y.clipboardData.getData(Le))}else return;const[Fe,Te]=J,gt=[];do{if(tt===void 0){const qe=Pn(J),_e=be??(ne==null?void 0:ne.map(jt=>jt.map(an=>an.rawValue).join("	")).join("	"))??"",Mt=A(qe,J,_e,void 0);Mt!==void 0&&gt.push(Mt);break}if(ne===void 0){if(be===void 0)return;ne=V0(be)}if(tt===!1||typeof tt=="function"&&(tt==null?void 0:tt([J[0]-q,J[1]],ne.map(qe=>qe.map(_e=>{var Mt;return((Mt=_e.rawValue)==null?void 0:Mt.toString())??""}))))!==!0)return;for(const[qe,_e]of ne.entries()){if(qe+Te>=je)break;for(const[Mt,jt]of _e.entries()){const an=[Mt+Fe,qe+Te],[Jn,ar]=an;if(Jn>=St.length||ar>=bn)continue;const Qn=Pn(an),sr=A(Qn,an,jt.rawValue,jt.formatted);sr!==void 0&&gt.push(sr)}}}while(!1);$n(gt),(ee=Tn.current)==null||ee.damage(gt.map(qe=>({cell:qe.location})))}},[E,Ct,Pn,K,Vn.paste,Ut,St.length,$n,bn,tt,q,je]);gn("paste",Ua,f,!1,!0);const zi=d.useCallback(async(y,A)=>{var Y,Z;if(!Vn.copy)return;const F=A===!0||((Y=Ut.current)==null?void 0:Y.contains(document.activeElement))===!0||((Z=l.current)==null?void 0:Z.contains(document.activeElement))===!0,U=K.columns,B=K.rows,J=(ee,ne)=>{if(!ve)Pu(ee,ne,y);else{const be=ne.map(Le=>({kind:te.Text,data:w[Le].title,displayData:w[Le].title,allowOverlay:!1}));Pu([be,...ee],ne,y)}};if(F&&cn!==void 0){if(K.current!==void 0){let ee=cn(K.current.range,un.current.signal);typeof ee!="object"&&(ee=await ee()),J(ee,lr(K.current.range.x-q,K.current.range.x+K.current.range.width-q))}else if(B!==void 0&&B.length>0){const ne=[...B].map(be=>{const Le=cn({x:q,y:be,width:w.length,height:1},un.current.signal);return typeof Le=="object"?Le[0]:Le().then(we=>we[0])});if(ne.some(be=>be instanceof Promise)){const be=await Promise.all(ne);J(be,lr(w.length))}else J(ne,lr(w.length))}else if(U.length>0){const ee=[],ne=[];for(const be of U){let Le=cn({x:be,y:0,width:1,height:je},un.current.signal);typeof Le!="object"&&(Le=await Le()),ee.push(Le),ne.push(be-q)}if(ee.length===1)J(ee[0],ne);else{const be=ee.reduce((Le,we)=>Le.map((Fe,Te)=>[...Fe,...we[Te]]));J(be,ne)}}}},[w,cn,K,Vn.copy,q,Ut,je,ve]);gn("copy",zi,f,!1,!1);const Zf=d.useCallback(async y=>{var F,U;if(!(!Vn.cut||!(((F=Ut.current)==null?void 0:F.contains(document.activeElement))===!0||((U=l.current)==null?void 0:U.contains(document.activeElement))===!0))&&(await zi(y),K.current!==void 0)){let B={current:{cell:K.current.cell,range:K.current.range,rangeStack:[]},rows:mt.empty(),columns:mt.empty()};const J=Ie==null?void 0:Ie(B);if(J===!1||(B=J===!0?B:J,B.current===void 0))return;Br(B.current.range)}},[Br,K,Vn.cut,zi,Ut,Ie]);gn("cut",Zf,f,!1,!1);const Jf=d.useCallback((y,A)=>{if(fe!==void 0){q!==0&&(y=y.map(B=>[B[0]-q,B[1]])),fe(y,A);return}if(y.length===0||A===-1)return;const[F,U]=y[A];c.current!==void 0&&c.current[0]===F&&c.current[1]===U||(c.current=[F,U],Ir(F,U,!1,!1))},[fe,q,Ir]),[Po,Lo]=((Dl=zt==null?void 0:zt.current)==null?void 0:Dl.cell)??[],_o=d.useRef(on);_o.current=on,d.useLayoutEffect(()=>{var y,A,F,U;Wa.current&&!Mr.current&&Po!==void 0&&Lo!==void 0&&(Po!==((A=(y=oe.current)==null?void 0:y.current)==null?void 0:A.cell[0])||Lo!==((U=(F=oe.current)==null?void 0:F.current)==null?void 0:U.cell[1]))&&_o.current(Po,Lo),Mr.current=!1},[Po,Lo]);const kl=K.current!==void 0&&(K.current.cell[0]>=St.length||K.current.cell[1]>=bn);d.useLayoutEffect(()=>{kl&&Ce(qo,!1)},[kl,Ce]);const Qf=d.useMemo(()=>Kt===!0&&(Oe==null?void 0:Oe.tint)===!0?mt.fromSingleSelection(bn-1):mt.empty(),[bn,Kt,Oe==null?void 0:Oe.tint]),eh=d.useCallback(y=>typeof at=="boolean"?at:(at==null?void 0:at(y-q))??!0,[q,at]),th=d.useMemo(()=>{if(Ha===void 0||l.current===null)return null;const{bounds:y,group:A}=Ha,F=l.current.getBoundingClientRect();return d.createElement(I0,{bounds:y,group:A,canvasBounds:F,onClose:()=>za(void 0),onFinish:U=>{za(void 0),ae==null||ae(A,U)}})},[ae,Ha]),nh=Math.min(St.length,ue+(Nn?1:0));d.useImperativeHandle(t,()=>({appendRow:(y,A)=>si(y+q,A),updateCells:y=>{var A;return q!==0&&(y=y.map(F=>({cell:[F.cell[0]+q,F.cell[1]]}))),(A=Tn.current)==null?void 0:A.damage(y)},getBounds:(y,A)=>{var F;if(!((l==null?void 0:l.current)===null||(Ut==null?void 0:Ut.current)===null)){if(y===void 0&&A===void 0){const U=l.current.getBoundingClientRect(),B=U.width/Ut.current.clientWidth;return{x:U.x-Ut.current.scrollLeft*B,y:U.y-Ut.current.scrollTop*B,width:Ut.current.scrollWidth*B,height:Ut.current.scrollHeight*B}}return(F=Tn.current)==null?void 0:F.getBounds((y??0)+q,A)}},focus:()=>{var y;return(y=Tn.current)==null?void 0:y.focus()},emit:async y=>{switch(y){case"delete":Hi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":Hi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":Hi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await zi(void 0,!0);break;case"paste":await Ua();break}},scrollTo:on,remeasureColumns:y=>{for(const A of y)Do(A+q)}}),[si,Do,Ut,zi,Hi,Ua,q,on]);const[Ml,Rl]=or??[],rh=d.useCallback(y=>{const[A,F]=y;if(F===-1){rt!=="none"&&(Ge(mt.fromSingleSelection(A),void 0,!1),kn());return}Ml===A&&Rl===F||(kt({cell:y,range:{x:A,y:F,width:1,height:1}},!0,!1,"keyboard-nav"),on(A,F))},[rt,kn,on,Ml,Rl,kt,Ge]),[ih,oh]=d.useState(!1),El=d.useRef(Qc(y=>{oh(y)},5)),ah=d.useCallback(()=>{El.current(!0),K.current===void 0&&K.columns.length===0&&K.rows.length===0&&s===void 0&&kt({cell:[q,ko],range:{x:q,y:ko,width:1,height:1}},!0,!1,"keyboard-select")},[ko,K,s,q,kt]),sh=d.useCallback(()=>{El.current(!1)},[]),[lh,uh]=d.useMemo(()=>{let y;const A=(Pt==null?void 0:Pt.scrollbarWidthOverride)??xs(),F=je+(Kt?1:0);if(typeof jn=="number")y=Rt+F*jn;else{let B=0;const J=Math.min(F,10);for(let Y=0;Y<J;Y++)B+=jn(Y);B=Math.floor(B/J),y=Rt+F*B}y+=A;const U=St.reduce((B,J)=>J.width+B,0)+A;return[`${Math.min(1e5,U)}px`,`${Math.min(1e5,y)}px`]},[St,Pt==null?void 0:Pt.scrollbarWidthOverride,jn,je,Kt,Rt]),ch=d.useMemo(()=>jm(Ue),[Ue]);return d.createElement(xd.Provider,{value:Ue},d.createElement(W0,{style:ch,className:X,inWidth:p??lh,inHeight:v??uh},d.createElement(k0,{fillHandle:Bt,drawFocusRing:Ar,experimental:Pt,fixedShadowX:Vt,fixedShadowY:Nt,getRowThemeOverride:zn,headerIcons:Cn,imageWindowLoader:nr,initialSize:Hn,isDraggable:V,onDragLeave:Je,onRowMoved:We,overscrollX:Da,overscrollY:Oa,preventDiagonalScrolling:Wt,rightElement:dn,rightElementProps:Zt,smoothScrollX:Sn,smoothScrollY:vn,className:X,enableGroups:xn,onCanvasFocused:ah,onCanvasBlur:sh,canvasRef:l,onContextMenu:Kf,theme:Ue,cellXOffset:Ff,cellYOffset:ko,accessibilityHeight:zr.height,onDragEnd:Yf,columns:St,nonGrowWidth:Gn,drawHeader:nt,onColumnProposeMove:I,drawCell:ot,disabledRows:Qf,freezeColumns:nh,lockColumns:q,firstColAccessible:q===0,getCellContent:Pn,minColumnWidth:ir,maxColumnWidth:Hr,searchInputRef:a,showSearch:Oi,onSearchClose:Pa,highlightRegions:Af,getCellsForSelection:cn,getGroupDetails:Fa,headerHeight:Di,isFocused:ih,groupHeaderHeight:xn?So:0,freezeTrailingRows:_t+(Kt&&(Oe==null?void 0:Oe.sticky)===!0?1:0),hasAppendRow:Kt,onColumnResize:xe,onColumnResizeEnd:ge,onColumnResizeStart:Me,onCellFocused:rh,onColumnMoved:Uf,onDragStart:qf,onHeaderMenuClick:$f,onHeaderIndicatorClick:Bf,onItemHovered:Ba,isFilling:(s==null?void 0:s.fillHandle)===!0,onMouseMove:Nf,onKeyDown:Hi,onKeyUp:ce,onMouseDown:Hf,onMouseUp:Vf,onDragOverCell:Xt,onDrop:Ot,onSearchResultsChanged:Jf,onVisibleRegionChanged:Wf,clientSize:ft,rowHeight:jn,searchResults:Q,searchValue:P,onSearchValueChange:H,rows:bn,scrollRef:Ut,selection:K,translateX:zr.tx,translateY:zr.ty,verticalBorder:eh,gridRef:Tn,getCellRenderer:Ct,resizeIndicator:Ta}),th,i!==void 0&&d.createElement(d.Suspense,{fallback:null},d.createElement(ev,{...i,validateCell:N,bloom:D,id:Gf,getCellRenderer:Ct,className:(Pt==null?void 0:Pt.isSubGrid)===!0?"click-outside-ignore":void 0,provideEditor:yn,imageEditorOverride:g,onFinishEditing:jf,markdownDivCreateNode:m,isOutsideClick:Sr,customEventTarget:Pt==null?void 0:Pt.eventTarget}))))},iv=d.forwardRef(rv),Nd=20;function Fu(e){const{cell:t,posX:n,posY:r,bounds:i,theme:o}=e,{width:a,height:l,x:s,y:u}=i,c=t.maxSize??Nd,f=Math.floor(i.y+l/2),g=nd(c,l,o.cellVerticalPadding),h=td(t.contentAlign??"center",s,a,o.cellHorizontalPadding,g),m=ed(h,f,g),p=rd(s+n,u+r,m);return Vs(t)&&p}const ov={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??"false"},kind:te.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>av(e,e.cell.data,Vs(e.cell),e.cell.maxSize??Nd,e.cell.hoverEffectIntensity??.35),onDelete:e=>({...e,data:!1}),onSelect:e=>{Fu(e)&&e.preventDefault()},onClick:e=>{if(Fu(e))return{...e.cell,data:Hd(e.cell.data)}},onPaste:(e,t)=>{let n=ia;return e.toLowerCase()==="true"?n=!0:e.toLowerCase()==="false"?n=!1:e.toLowerCase()==="indeterminate"&&(n=zs),n===t.data?void 0:{...t,data:n}}};function av(e,t,n,r,i){if(!n&&t===ia)return;const{ctx:o,hoverAmount:a,theme:l,rect:s,highlighted:u,hoverX:c,hoverY:f,cell:{contentAlign:g}}=e,{x:h,y:m,width:p,height:v}=s;let w=!1;if(i>0){let b=n?1-i+i*a:.4;if(t===ia&&(b*=a),b===0)return;b<1&&(w=!0,o.globalAlpha=b)}Gs(o,l,t,h,m,p,v,u,c,f,r,g),w&&(o.globalAlpha=1)}const sv=mn("div")({name:"BubblesOverlayEditorStyle",class:"gdg-b1ygi5by",propsAsIs:!1}),lv=e=>{const{bubbles:t}=e;return d.createElement(sv,null,t.map((n,r)=>d.createElement("div",{key:r,className:"boe-bubble"},n)),d.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},uv={getAccessibilityString:e=>id(e.data),kind:te.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i).width+r+20,0)+2*n.cellHorizontalPadding-4,draw:e=>dv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return d.createElement(lv,{bubbles:t.data})},onPaste:()=>{}},cv=4;function dv(e,t){const{rect:n,theme:r,ctx:i,highlighted:o}=e,{x:a,y:l,width:s,height:u}=n,c=20,f=8,g=cv;let h=a+r.cellHorizontalPadding;const m=[];for(const p of t){if(h>a+s)break;const v=Qr(p,i,r.baseFontFull).width;m.push({x:h,width:v}),h+=v+f*2+g}i.beginPath();for(const p of m)cr(i,p.x,l+(u-c)/2,p.width+f*2,c,r.roundingRadius??c/2);i.fillStyle=o?r.bgBubbleSelected:r.bgBubble,i.fill();for(const[p,v]of m.entries())i.beginPath(),i.fillStyle=r.textBubble,i.fillText(t[p],v.x+f,l+u/2+dr(i,r))}const fv=mn("div")({name:"DrilldownOverlayEditorStyle",class:"gdg-d4zsq0x",propsAsIs:!1}),hv=e=>{const{drilldowns:t}=e;return d.createElement(fv,null,t.map((n,r)=>d.createElement("div",{key:r,className:"doe-bubble"},n.img!==void 0&&d.createElement("img",{src:n.img}),d.createElement("div",null,n.text))))},gv={getAccessibilityString:e=>id(e.data.map(t=>t.text)),kind:te.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i.text).width+r+20+(i.img!==void 0?18:0),0)+2*n.cellHorizontalPadding-4,draw:e=>vv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return d.createElement(hv,{drilldowns:t.data})},onPaste:()=>{}},mv=4,cs={};function pv(e,t,n,r){const i=Math.ceil(window.devicePixelRatio),o=5,a=n-o*2,l=4,s=n*i,u=r+o,c=r*3,f=(c+o*2)*i,g=`${e},${t},${i},${n}`;if(cs[g]!==void 0)return{el:cs[g],height:s,width:f,middleWidth:l*i,sideWidth:u*i,padding:o*i,dpr:i};const h=document.createElement("canvas"),m=h.getContext("2d");return m===null?null:(h.width=f,h.height=s,m.scale(i,i),cs[g]=h,m.beginPath(),cr(m,o,o,c,a,r),m.shadowColor="rgba(24, 25, 34, 0.4)",m.shadowBlur=1,m.fillStyle=e,m.fill(),m.shadowColor="rgba(24, 25, 34, 0.3)",m.shadowOffsetY=1,m.shadowBlur=5,m.fillStyle=e,m.fill(),m.shadowOffsetY=0,m.shadowBlur=0,m.shadowBlur=0,m.beginPath(),cr(m,o+.5,o+.5,c,a,r),m.strokeStyle=t,m.lineWidth=1,m.stroke(),{el:h,height:s,width:f,sideWidth:u*i,middleWidth:r*i,padding:o*i,dpr:i})}function vv(e,t){const{rect:n,theme:r,ctx:i,imageLoader:o,col:a,row:l}=e,{x:s,width:u}=n,c=r.baseFontFull,f=bd(i,c),g=Math.min(n.height,Math.max(16,Math.ceil(f*r.lineHeight)*2)),h=Math.floor(n.y+(n.height-g)/2),m=g-10,p=8,v=mv;let w=s+r.cellHorizontalPadding;const b=r.roundingRadius??6,M=pv(r.bgCell,r.drilldownBorder,g,b),O=[];for(const S of t){if(w>s+u)break;const L=Qr(S.text,i,c).width;let E=0;S.img!==void 0&&o.loadOrGetImage(S.img,a,l)!==void 0&&(E=m-8+4);const x=L+E+p*2;O.push({x:w,width:x}),w+=x+v}if(M!==null){const{el:S,height:R,middleWidth:L,sideWidth:E,width:x,dpr:_,padding:D}=M,C=E/_,I=D/_;for(const T of O){const k=Math.floor(T.x),z=Math.floor(T.width),$=z-(C-I)*2;i.imageSmoothingEnabled=!1,i.drawImage(S,0,0,E,R,k-I,h,C,g),$>0&&i.drawImage(S,E,0,L,R,k+(C-I),h,$,g),i.drawImage(S,x-E,0,E,R,k+z-(C-I),h,C,g),i.imageSmoothingEnabled=!0}}i.beginPath();for(const[S,R]of O.entries()){const L=t[S];let E=R.x+p;if(L.img!==void 0){const x=o.loadOrGetImage(L.img,a,l);if(x!==void 0){const _=m-8;let D=0,C=0,I=x.width,T=x.height;I>T?(D+=(I-T)/2,I=T):T>I&&(C+=(T-I)/2,T=I),i.beginPath(),cr(i,E,h+g/2-_/2,_,_,r.roundingRadius??3),i.save(),i.clip(),i.drawImage(x,D,C,I,T,E,h+g/2-_/2,_,_),i.restore(),E+=_+4}}i.beginPath(),i.fillStyle=r.textBubble,i.fillText(L.text,E,h+g/2+dr(i,r))}}const bv={getAccessibilityString:e=>e.data.join(", "),kind:te.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>wv(e,e.cell.displayData??e.cell.data,e.cell.rounding??e.theme.roundingRadius??4,e.cell.contentAlign),measure:(e,t)=>t.data.length*50,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:r}=e,i=r??im;return d.createElement(i,{urls:t.data,canWrite:t.readonly!==!0,onCancel:n,onChange:o=>{n({...t,data:[o]})}})},onPaste:(e,t)=>{e=e.trim();const r=e.split(",").map(i=>{try{return new URL(i),i}catch{return}}).filter(i=>i!==void 0);if(!(r.length===t.data.length&&r.every((i,o)=>i===t.data[o])))return{...t,data:r}}},ds=4;function wv(e,t,n,r){const{rect:i,col:o,row:a,theme:l,ctx:s,imageLoader:u}=e,{x:c,y:f,height:g,width:h}=i,m=g-l.cellVerticalPadding*2,p=[];let v=0;for(let b=0;b<t.length;b++){const M=t[b];if(M.length===0)continue;const O=u.loadOrGetImage(M,o,a);if(O!==void 0){p[b]=O;const S=O.width*(m/O.height);v+=S+ds}}if(v===0)return;v-=ds;let w=c+l.cellHorizontalPadding;r==="right"?w=Math.floor(c+h-l.cellHorizontalPadding-v):r==="center"&&(w=Math.floor(c+h/2-v/2));for(const b of p){if(b===void 0)continue;const M=b.width*(m/b.height);n>0&&(s.beginPath(),cr(s,w,f+l.cellVerticalPadding,M,m,n),s.save(),s.clip()),s.drawImage(b,w,f+l.cellVerticalPadding,M,m),n>0&&s.restore(),w+=M+ds}}function yv(e,t){let n=e*49632+t*325176;return n^=n<<13,n^=n>>17,n^=n<<5,n/4294967295*2}const Cv={getAccessibilityString:()=>"",kind:te.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:e=>{const{cell:t,col:n,row:r,ctx:i,rect:o,theme:a}=e;if(t.skeletonWidth===void 0||t.skeletonWidth===0)return;let l=t.skeletonWidth;t.skeletonWidthVariability!==void 0&&t.skeletonWidthVariability>0&&(l+=Math.round(yv(n,r)*t.skeletonWidthVariability));const s=a.cellHorizontalPadding;l+s*2>=o.width&&(l=o.width-s*2-1);const u=t.skeletonHeight??Math.min(18,o.height-2*a.cellVerticalPadding);cr(i,o.x+s,o.y+(o.height-u)/2,l,u,a.roundingRadius??3),i.fillStyle=jr(a.textDark,.1),i.fill()},onPaste:()=>{}},Sv=()=>e=>e.targetWidth,Au=mn("div")({name:"MarkdownOverlayEditorStyle",class:"gdg-m1pnx84e",propsAsIs:!1,vars:{"m1pnx84e-0":[Sv(),"px"]}}),xv=e=>{const{value:t,onChange:n,forceEditMode:r,createNode:i,targetRect:o,onFinish:a,validatedSelection:l}=e,s=t.data,u=t.readonly===!0,[c,f]=d.useState(s===""||r),g=d.useCallback(()=>{f(m=>!m)},[]),h=s?"gdg-ml-6":"";return c?d.createElement(Au,{targetWidth:o.width-20},d.createElement(Zr,{autoFocus:!0,highlight:!1,validatedSelection:l,value:s,onKeyDown:m=>{m.key==="Enter"&&m.stopPropagation()},onChange:n}),d.createElement("div",{className:`gdg-edit-icon gdg-checkmark-hover ${h}`,onClick:()=>a(t)},d.createElement(Zg,null))):d.createElement(Au,{targetWidth:o.width},d.createElement(xm,{contents:s,createNode:i}),!u&&d.createElement(d.Fragment,null,d.createElement("div",{className:"spacer"}),d.createElement("div",{className:`gdg-edit-icon gdg-edit-hover ${h}`,onClick:g},d.createElement($s,null))),d.createElement("textarea",{className:"gdg-md-edit-textarea gdg-input",autoFocus:!0}))},kv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:mo,measure:(e,t,n)=>{const r=t.data.split(`
`)[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:e=>ur(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:r,onFinishedEditing:i,markdownDivCreateNode:o,forceEditMode:a,validatedSelection:l}=e;return d.createElement(xv,{onFinish:i,targetRect:r,value:n,validatedSelection:l,onChange:s=>t({...n,data:s.target.value}),forceEditMode:a,createNode:o})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},Mv={getAccessibilityString:e=>e.row.toString(),kind:Yn.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:Rv,measure:()=>44,draw:e=>Iv(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle,e.cell.checkboxStyle),onClick:e=>{const{bounds:t,cell:n,posX:r,posY:i}=e,{width:o,height:a}=t,l=n.drawHandle?7+(o-7)/2:o/2,s=a/2;if(Math.abs(r-l)<=10&&Math.abs(i-s)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}};function Rv(e,t){const{ctx:n,theme:r}=e,i=r.markerFontFull,o=t??{};return(o==null?void 0:o.font)!==i&&(n.font=i,o.font=i),o.deprep=Ev,n.textAlign="center",o}function Ev(e){const{ctx:t}=e;t.textAlign="start"}function Iv(e,t,n,r,i,o){const{ctx:a,rect:l,hoverAmount:s,theme:u}=e,{x:c,y:f,width:g,height:h}=l,m=n?1:r==="checkbox-visible"?.6+.4*s:s;if(r!=="number"&&m>0){a.globalAlpha=m;const p=7*(n?s:1);if(Gs(a,u,n,i?c+p:c,f,i?g-p:g,h,!0,void 0,void 0,18,"center",o),i){a.globalAlpha=s,a.beginPath();for(const v of[3,6])for(const w of[-5,-1,3])a.rect(c+v,f+h/2+w,2,2);a.fillStyle=u.textLight,a.fill(),a.beginPath()}a.globalAlpha=1}if(r==="number"||r==="both"&&!n){const p=t.toString(),v=u.markerFontFull,w=c+g/2;r==="both"&&s!==0&&(a.globalAlpha=1-s),a.fillStyle=u.textLight,a.font=v,a.fillText(p,w,f+h/2+dr(a,v)),s!==0&&(a.globalAlpha=1)}}const Tv={getAccessibilityString:()=>"",kind:Yn.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>Dv(e,e.cell.hint,e.cell.icon),onPaste:()=>{}};function Dv(e,t,n){const{ctx:r,rect:i,hoverAmount:o,theme:a,spriteManager:l}=e,{x:s,y:u,width:c,height:f}=i;r.beginPath(),r.globalAlpha=o,r.rect(s+1,u+1,c,f-2),r.fillStyle=a.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();const g=t!=="";let h=0;if(n!==void 0){const p=f-8,v=s+8/2,w=u+8/2;l.drawSprite(n,"normal",r,v,w,p,a,g?1:o),h=p}else{h=24;const m=12,p=g?m:o*m,v=g?0:(1-o)*m*.5,w=a.cellHorizontalPadding+4;p>0&&(r.moveTo(s+w+v,u+f/2),r.lineTo(s+w+v+p,u+f/2),r.moveTo(s+w+v+p*.5,u+f/2-p*.5),r.lineTo(s+w+v+p*.5,u+f/2+p*.5),r.lineWidth=2,r.strokeStyle=a.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=a.textMedium,r.fillText(t,h+s+a.cellHorizontalPadding+.5,u+f/2+dr(r,a)),r.beginPath()}function $d(e,t,n,r,i,o,a){e.textBaseline="alphabetic";const l=Ov(e,i,r,t,(n==null?void 0:n.fullSize)??!1);e.beginPath(),cr(e,l.x,l.y,l.width,l.height,t.roundingRadius??4),e.globalAlpha=o,e.fillStyle=(n==null?void 0:n.bgColor)??jr(t.textDark,.1),e.fill(),e.globalAlpha=1,e.fillStyle=t.textDark,e.textBaseline="middle",a==null||a("text")}function Ov(e,t,n,r,i){const o=r.cellHorizontalPadding,a=r.cellVerticalPadding;if(i)return{x:t.x+o/2,y:t.y+a/2+1,width:t.width-o,height:t.height-a-1};const l=Qr(n,e,r.baseFontFull,"alphabetic"),s=t.height-a,u=Math.min(s,l.actualBoundingBoxAscent*2.5);return{x:t.x+o/2,y:t.y+(t.height-u)/2+1,width:l.width+o*3,height:u-1}}const Pv=d.lazy(async()=>await Fs(()=>import("./number-overlay-editor.Dx0XqCkD.js"),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),Lv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Number,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,useLabel:!0,drawPrep:mo,draw:e=>{const{hoverAmount:t,cell:n,ctx:r,theme:i,rect:o,overrideCursor:a}=e,{hoverEffect:l,displayData:s,hoverEffectTheme:u}=n;l===!0&&t>0&&$d(r,i,u,s,o,t,a),ur(e,e.cell.displayData,e.cell.contentAlign)},measure:(e,t,n)=>e.measureText(t.displayData).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return d.createElement(d.Suspense,{fallback:null},d.createElement(Pv,{highlight:t,disabled:r.readonly===!0,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:o=>n({...r,data:Number.isNaN(o.floatValue??0)?0:o.floatValue})}))},onPaste:(e,t,n)=>{const r=typeof n.rawValue=="number"?n.rawValue:Number.parseFloat(typeof n.rawValue=="string"?n.rawValue:e);if(!(Number.isNaN(r)||t.data===r))return{...t,data:r,displayData:n.formattedString??t.displayData}}},_v={getAccessibilityString:()=>"",measure:()=>108,kind:te.Protected,needsHover:!1,needsHoverPosition:!1,draw:Fv,onPaste:()=>{}};function Fv(e){const{ctx:t,theme:n,rect:r}=e,{x:i,y:o,height:a}=r;t.beginPath();const l=2.5;let s=i+n.cellHorizontalPadding+l;const u=o+a/2,c=Math.cos(tu(30))*l,f=Math.sin(tu(30))*l;for(let g=0;g<12;g++)t.moveTo(s,u-l),t.lineTo(s,u+l),t.moveTo(s+c,u-f),t.lineTo(s-c,u+f),t.moveTo(s-c,u-f),t.lineTo(s+c,u+f),s+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()}const Av={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>mo(e,t,e.theme.textLight),draw:e=>ur(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+n.cellHorizontalPadding*2,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return At.createElement(Zr,{highlight:t,autoFocus:r.readonly!==!0,disabled:r.readonly!==!1,value:r.data,validatedSelection:i,onChange:o=>n({...r,data:o.target.value})})},onPaste:()=>{}},Hv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Text,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,drawPrep:mo,useLabel:!0,draw:e=>{const{cell:t,hoverAmount:n,hyperWrapping:r,ctx:i,rect:o,theme:a,overrideCursor:l}=e,{displayData:s,contentAlign:u,hoverEffect:c,allowWrapping:f,hoverEffectTheme:g}=t;c===!0&&n>0&&$d(i,a,g,s,o,n,l),ur(e,s,u,f,r)},measure:(e,t,n)=>{const r=t.displayData.split(`
`,t.allowWrapping===!0?void 0:1);let i=0;for(const o of r)i=Math.max(i,e.measureText(o).width);return i+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:e.allowWrapping===!0,editor:t=>{const{isHighlighted:n,onChange:r,value:i,validatedSelection:o}=t;return d.createElement(Zr,{style:e.allowWrapping===!0?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:i.readonly!==!0,disabled:i.readonly===!0,altNewline:!0,value:i.data,validatedSelection:o,onChange:a=>r({...i,data:a.target.value})})}}),onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},zv=mn("div")({name:"UriOverlayEditorStyle",class:"gdg-u1rrojo",propsAsIs:!1}),Vv=e=>{const{uri:t,onChange:n,forceEditMode:r,readonly:i,validatedSelection:o,preview:a}=e,[l,s]=d.useState(!i&&(t===""||r)),u=d.useCallback(()=>{s(!0)},[]);return l?d.createElement(Zr,{validatedSelection:o,highlight:!0,autoFocus:!0,value:t,onChange:n}):d.createElement(zv,null,d.createElement("a",{className:"gdg-link-area",href:t,target:"_blank",rel:"noopener noreferrer"},a),!i&&d.createElement("div",{className:"gdg-edit-icon",onClick:u},d.createElement($s,null)),d.createElement("textarea",{className:"gdg-input",autoFocus:!0}))};function Bd(e,t,n,r){let i=n.cellHorizontalPadding;const o=t.height/2-e.actualBoundingBoxAscent/2,a=e.width,l=e.actualBoundingBoxAscent;return r==="right"?i=t.width-a-n.cellHorizontalPadding:r==="center"&&(i=t.width/2-a/2),{x:i,y:o,width:a,height:l}}function Hu(e){const{cell:t,bounds:n,posX:r,posY:i,theme:o}=e,a=t.displayData??t.data;if(t.hoverEffect!==!0||t.onClickUri===void 0)return!1;const l=pd(a,o.baseFontFull);if(l===void 0)return!1;const s=Bd(l,n,o,t.contentAlign);return Xr({x:s.x-4,y:s.y-4,width:s.width+8,height:s.height+8},r,i)}const Nv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Uri,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!0,useLabel:!0,drawPrep:mo,draw:e=>{const{cell:t,theme:n,overrideCursor:r,hoverX:i,hoverY:o,rect:a,ctx:l}=e,s=t.displayData??t.data,u=t.hoverEffect===!0;if(r!==void 0&&u&&i!==void 0&&o!==void 0){const c=Qr(s,l,n.baseFontFull),f=Bd(c,a,n,t.contentAlign),{x:g,y:h,width:m,height:p}=f;if(i>=g-4&&i<=g-4+m+8&&o>=h-4&&o<=h-4+p+8){const v=dr(l,n.baseFontFull);r("pointer");const w=5,b=h-v;l.beginPath(),l.moveTo(a.x+g,Math.floor(a.y+b+p+w)+.5),l.lineTo(a.x+g+m,Math.floor(a.y+b+p+w)+.5),l.strokeStyle=n.linkColor,l.stroke(),l.save(),l.fillStyle=e.cellFillColor,ur({...e,rect:{...a,x:a.x-1}},s,t.contentAlign),ur({...e,rect:{...a,x:a.x-2}},s,t.contentAlign),ur({...e,rect:{...a,x:a.x+1}},s,t.contentAlign),ur({...e,rect:{...a,x:a.x+2}},s,t.contentAlign),l.restore()}}l.fillStyle=u?n.linkColor:n.textDark,ur(e,s,t.contentAlign)},onSelect:e=>{Hu(e)&&e.preventDefault()},onClick:e=>{var r;const{cell:t}=e;Hu(e)&&((r=t.onClickUri)==null||r.call(t,e))},measure:(e,t,n)=>e.measureText(t.displayData??t.data).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:""}),provideEditor:e=>t=>{const{onChange:n,value:r,forceEditMode:i,validatedSelection:o}=t;return d.createElement(Vv,{forceEditMode:r.readonly!==!0&&(i||e.hoverEffect===!0&&e.onClickUri!==void 0),uri:r.data,preview:r.displayData??r.data,validatedSelection:o,readonly:r.readonly===!0,onChange:a=>n({...r,data:a.target.value})})},onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},$v=[Mv,Tv,ov,uv,gv,bv,Cv,kv,Lv,_v,Av,Hv,Nv];var fs,zu;function Bv(){if(zu)return fs;zu=1;var e=Jc(),t=Tc(),n="Expected a function";function r(i,o,a){var l=!0,s=!0;if(typeof i!="function")throw new TypeError(n);return t(a)&&(l="leading"in a?!!a.leading:l,s="trailing"in a?!!a.trailing:s),e(i,o,{leading:l,maxWait:o,trailing:s})}return fs=r,fs}var Wv=Bv();const Uv=wr(Wv),hs=[];class qv extends yd{constructor(){super(...arguments);dt(this,"imageLoaded",()=>{});dt(this,"loadedLocations",[]);dt(this,"cache",{});dt(this,"sendLoaded",Uv(()=>{this.imageLoaded(new io(this.loadedLocations)),this.loadedLocations=[]},20));dt(this,"clearOutOfWindow",()=>{const n=Object.keys(this.cache);for(const r of n){const i=this.cache[r];let o=!1;for(let a=0;a<i.cells.length;a++){const l=i.cells[a];if(this.isInWindow(l)){o=!0;break}}o?i.cells=i.cells.filter(this.isInWindow):(i.cancel(),delete this.cache[r])}})}setCallback(n){this.imageLoaded=n}loadImage(n,r,i,o){let a=!1;const l=hs.pop()??new Image;let s=!1;const u={img:void 0,cells:[tr(r,i)],url:n,cancel:()=>{s||(s=!0,hs.length<12?hs.unshift(l):a||(l.src=""))}},c=new Promise(f=>l.addEventListener("load",()=>f(null)));requestAnimationFrame(async()=>{try{l.src=n,await c,await l.decode();const f=this.cache[o];if(f!==void 0&&!s){f.img=l;for(const g of f.cells)this.loadedLocations.push(js(g));a=!0,this.sendLoaded()}}catch{u.cancel()}}),this.cache[o]=u}loadOrGetImage(n,r,i){const o=n,a=this.cache[o];if(a!==void 0){const l=tr(r,i);return a.cells.includes(l)||a.cells.push(l),a.img}else this.loadImage(n,r,i,o)}}const Yv=(e,t)=>{const n=d.useMemo(()=>({...Ap,...e.headerIcons}),[e.headerIcons]),r=d.useMemo(()=>e.imageWindowLoader??new qv,[e.imageWindowLoader]);return d.createElement(iv,{...e,renderers:$v,headerIcons:n,ref:t,imageWindowLoader:r})},Xv=d.forwardRef(Yv);function Vu(e,t){const n=d.useRef(null),r=d.useRef(),i=d.useCallback(()=>{n.current&&(clearTimeout(n.current),n.current=null)},[]);return d.useEffect(()=>i,[i]),{debouncedCallback:d.useCallback((...a)=>{r.current=a,i(),n.current=setTimeout(()=>{r.current&&(e(...r.current),r.current=void 0)},t)},[e,t,i]),cancel:i}}const Wd=xi("div",{target:"ee4nolw0"})(({theme:e})=>({paddingTop:e.spacing.xs,paddingBottom:e.spacing.xs})),Pr=xi("div",{target:"ee4nolw1"})(({theme:e,isActive:t,hasSubmenu:n})=>({display:"flex",alignItems:"center",justifyContent:"flex-start",gap:e.spacing.sm,paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm,paddingTop:e.spacing.twoXS,paddingBottom:e.spacing.twoXS,cursor:"pointer",backgroundColor:t?e.colors.darkenedBgMix15:void 0,"&:hover":{backgroundColor:e.colors.darkenedBgMix15},minWidth:e.sizes.minMenuWidth,...n&&{justifyContent:"space-between","& > :first-of-type":{display:"flex",alignItems:"center",gap:e.spacing.sm}}})),jv=xi("div",{target:"ee4nolw2"})(({theme:e})=>({height:e.sizes.borderWidth,backgroundColor:e.colors.borderColor,marginTop:e.spacing.xs,marginBottom:e.spacing.xs})),Nu=[{format:"",label:"Automatic",icon:":material/123:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"plain",label:"Plain",icon:":material/speed_1_75:"},{format:"compact",label:"Compact",icon:":material/1k:"},{format:"dollar",label:"Dollar",icon:":material/attach_money:"},{format:"euro",label:"Euro",icon:":material/euro:"},{format:"yen",label:"Yen",icon:":material/currency_yen:"},{format:"percent",label:"Percent",icon:":material/percent:"},{format:"scientific",label:"Scientific",icon:":material/experiment:"},{format:"accounting",label:"Accounting",icon:":material/finance_chip:"}],Gv={number:Nu,progress:Nu,datetime:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"},{format:"calendar",label:"Calendar",icon:":material/today:"}],date:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"}],time:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"}]};function Kv({columnKind:e,isOpen:t,onMouseEnter:n,onMouseLeave:r,onChangeFormat:i,onCloseMenu:o,children:a}){const l=Gr(),{colors:s,fontSizes:u,radii:c,fontWeights:f}=l,g=Gv[e]||[];return g.length===0?lt(Pc,{}):lt(xa,{triggerType:Lc.hover,returnFocus:!0,autoFocus:!0,focusLock:!0,isOpen:t,onMouseEnter:n,onMouseLeave:r,ignoreBoundary:!0,content:lt(Wd,{role:"menu",children:g.map(h=>_n(Pr,{onClick:()=>{i(h.format),o()},role:"menuitem",children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:h.icon}),h.label]},h.format))}),placement:Ca.right,showArrow:!1,popoverMargin:2,overrides:{Body:{props:{"data-testid":"stDataFrameColumnFormattingMenu"},style:{borderTopLeftRadius:c.default,borderTopRightRadius:c.default,borderBottomLeftRadius:c.default,borderBottomRightRadius:c.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${l.sizes.borderWidth} solid ${l.colors.borderColor}`}},Inner:{style:{backgroundColor:Sa(l)?s.bgColor:s.secondaryBg,color:s.bodyText,fontSize:u.sm,fontWeight:f.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:a})}const Zv=d.memo(Kv);function Jv({top:e,left:t,isColumnPinned:n,onPinColumn:r,onUnpinColumn:i,onCloseMenu:o,onSortColumn:a,onHideColumn:l,columnKind:s,onChangeFormat:u,onAutosize:c}){const f=Gr(),[g,h]=d.useState(!1),{colors:m,fontSizes:p,radii:v,fontWeights:w}=f;d.useEffect(()=>{function M(O){O.preventDefault()}return document.addEventListener("wheel",M,{passive:!1}),document.addEventListener("touchmove",M,{passive:!1}),()=>{document.removeEventListener("wheel",M),document.removeEventListener("touchmove",M)}},[]);const b=d.useCallback(()=>{o()},[o]);return lt(xa,{autoFocus:!0,"aria-label":"Dataframe column menu",content:_n(Wd,{children:[a&&_n(Pc,{children:[_n(Pr,{onClick:()=>{a("asc"),b()},role:"menuitem",children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_upward:"}),"Sort ascending"]}),_n(Pr,{onClick:()=>{a("desc"),b()},role:"menuitem",children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_downward:"}),"Sort descending"]}),lt(jv,{})]}),u&&lt(Zv,{columnKind:s,isOpen:g,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),onChangeFormat:u,onCloseMenu:b,children:_n(Pr,{onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),isActive:g,hasSubmenu:!0,children:[_n("div",{children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/format_list_numbered:"}),"Format"]}),lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/chevron_right:"})]})}),c&&_n(Pr,{onClick:()=>{c(),b()},children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrows_outward:"}),"Autosize"]}),n&&_n(Pr,{onClick:()=>{i(),b()},children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep_off:"}),"Unpin column"]}),!n&&_n(Pr,{onClick:()=>{r(),b()},children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep:"}),"Pin column"]}),l&&_n(Pr,{onClick:()=>{l(),b()},children:[lt(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/visibility_off:"}),"Hide column"]})]}),placement:Ca.bottomRight,accessibilityType:_c.menu,showArrow:!1,popoverMargin:Wn("0.375rem"),onClickOutside:g?void 0:b,onEsc:b,overrides:{Body:{props:{"data-testid":"stDataFrameColumnMenu"},style:{paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{border:`${f.sizes.borderWidth} solid ${f.colors.borderColor}`,backgroundColor:Sa(f)?m.bgColor:m.secondaryBg,color:m.bodyText,fontSize:p.sm,fontWeight:w.normal,borderTopLeftRadius:v.default,borderTopRightRadius:v.default,borderBottomLeftRadius:v.default,borderBottomRightRadius:v.default,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:!0,children:lt("div",{"data-testid":"stDataFrameColumnMenuTarget",style:{position:"fixed",top:e,left:t,visibility:"hidden",transform:"unset"}})})}const Qv=d.memo(Jv),e1="(index)",t1=({label:e,initialValue:t,onChange:n})=>{const r=Gr();return lt(mg,{checked:t,onChange:i=>{n(i.target.checked)},"aria-label":e,checkmarkType:gg.default,labelPlacement:hg.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:r.spacing.none,marginTop:r.spacing.none,paddingLeft:r.spacing.md,paddingRight:r.spacing.md,paddingTop:r.spacing.twoXS,paddingBottom:r.spacing.twoXS,backgroundColor:i?r.colors.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Checkmark:{style:({$isFocusVisible:i,$checked:o})=>{const a=o?r.colors.primary:r.colors.fadedText40;return{outline:0,width:r.sizes.checkbox,height:r.sizes.checkbox,marginTop:r.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&o?`0 0 0 0.2rem ${gi(r.colors.primary,.5)}`:"",borderLeftWidth:r.sizes.borderWidth,borderRightWidth:r.sizes.borderWidth,borderTopWidth:r.sizes.borderWidth,borderBottomWidth:r.sizes.borderWidth,borderLeftColor:a,borderRightColor:a,borderTopColor:a,borderBottomColor:a}}},Label:{style:{lineHeight:r.lineHeights.small,paddingLeft:r.spacing.sm,position:"relative",color:r.colors.bodyText,fontSize:r.fontSizes.sm,fontWeight:r.fontWeights.normal}}},children:e})},n1=({columns:e,columnOrder:t,setColumnOrder:n,hideColumn:r,showColumn:i,children:o,isOpen:a,onClose:l})=>{const s=Gr();return lt(xa,{triggerType:Lc.click,placement:Ca.bottomRight,autoFocus:!0,focusLock:!0,content:()=>lt("div",{style:{paddingTop:s.spacing.sm,paddingBottom:s.spacing.sm},children:e.map(u=>{const c=t.length&&!u.isIndex?!t.includes(u.id)&&!t.includes(u.name):!1;return lt(t1,{label:!u.title&&u.isIndex?e1:u.title,initialValue:!(u.isHidden===!0||c),onChange:f=>{f?(i(u.id),c&&n(g=>[...g,u.id])):r(u.id)}},u.id)})}),isOpen:a,onClickOutside:l,onClick:()=>a?l():void 0,onEsc:l,ignoreBoundary:!1,overrides:{Body:{props:{"data-testid":"stDataFrameColumnVisibilityMenu"},style:{borderTopLeftRadius:s.radii.default,borderTopRightRadius:s.radii.default,borderBottomLeftRadius:s.radii.default,borderBottomRightRadius:s.radii.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${s.sizes.borderWidth} solid ${s.colors.borderColor}`}},Inner:{style:{backgroundColor:Sa(s)?s.colors.bgColor:s.colors.secondaryBg,color:s.colors.bodyText,fontSize:s.fontSizes.sm,fontWeight:s.fontWeights.normal,minWidth:s.sizes.minMenuWidth,maxWidth:`calc(${s.sizes.minMenuWidth} * 2)`,maxHeight:s.sizes.maxDropdownHeight,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:lt("div",{children:o})})},r1=d.memo(n1);var i1=kh();const o1=wr(i1);var ta={exports:{}};/*! Moment Duration Format v2.2.2
 *  https://github.com/jsmreese/moment-duration-format
 *  Date: 2018-02-16
 *
 *  Duration format plugin function for the Moment.js library
 *  http://momentjs.com/
 *
 *  Copyright 2018 John Madhavan-Reese
 *  Released under the MIT license
 */var a1=ta.exports,$u;function s1(){return $u||($u=1,function(e,t){(function(n,r){try{e.exports=r(Mh)}catch{e.exports=r}n&&(n.momentDurationFormatSetup=n.moment?r(n.moment):r)})(a1,function(n){var r=!1,i=!1,o=!1,a=!1,l="escape years months weeks days hours minutes seconds milliseconds general".split(" "),s=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function u(H,P){return P.length>H.length?!1:H.indexOf(P)!==-1}function c(H){for(var P="";H;)P+="0",H-=1;return P}function f(H){for(var P=H.split("").reverse(),W=0,ce=!0;ce&&W<P.length;)W?P[W]==="9"?P[W]="0":(P[W]=(parseInt(P[W],10)+1).toString(),ce=!1):(parseInt(P[W],10)<5&&(ce=!1),P[W]="0"),W+=1;return ce&&P.push("1"),P.reverse().join("")}function g(H,P){var W=L(k(P).sort(),function(De){return De+":"+P[De]}).join(","),ce=H+"+"+W;return g.cache[ce]||(g.cache[ce]=Intl.NumberFormat(H,P)),g.cache[ce]}g.cache={};function h(H,P,W){var ce=P.useToLocaleString,De=P.useGrouping,He=De&&P.grouping.slice(),ye=P.maximumSignificantDigits,Re=P.minimumIntegerDigits||1,Se=P.fractionDigits||0,Et=P.groupingSeparator,wt=P.decimalSeparator;if(ce&&W){var rt={minimumIntegerDigits:Re,useGrouping:De};if(Se&&(rt.maximumFractionDigits=Se,rt.minimumFractionDigits=Se),ye&&H>0&&(rt.maximumSignificantDigits=ye),o){if(!a){var se=T({},P);se.useGrouping=!1,se.decimalSeparator=".",H=parseFloat(h(H,se),10)}return g(W,rt).format(H)}else{if(!i){var se=T({},P);se.useGrouping=!1,se.decimalSeparator=".",H=parseFloat(h(H,se),10)}return H.toLocaleString(W,rt)}}var et;ye?et=H.toPrecision(ye+1):et=H.toFixed(Se+1);var me,he,pe,ze=et.split("e");pe=ze[1]||"",ze=ze[0].split("."),he=ze[1]||"",me=ze[0]||"";var Pe=me.length,tt=he.length,ve=Pe+tt,ue=me+he;(ye&&ve===ye+1||!ye&&tt===Se+1)&&(ue=f(ue),ue.length===ve+1&&(Pe=Pe+1),tt&&(ue=ue.slice(0,-1)),me=ue.slice(0,Pe),he=ue.slice(Pe)),ye&&(he=he.replace(/0*$/,""));var it=parseInt(pe,10);it>0?he.length<=it?(he=he+c(it-he.length),me=me+he,he=""):(me=me+he.slice(0,it),he=he.slice(it)):it<0&&(he=c(Math.abs(it)-me.length)+me+he,me="0"),ye||(he=he.slice(0,Se),he.length<Se&&(he=he+c(Se-he.length)),me.length<Re&&(me=c(Re-me.length)+me));var Ae="";if(De){ze=me;for(var Qe;ze.length;)He.length&&(Qe=He.shift()),Ae&&(Ae=Et+Ae),Ae=ze.slice(-Qe)+Ae,ze=ze.slice(0,-Qe)}else Ae=me;return he&&(Ae=Ae+wt+he),Ae}function m(H,P){return H.label.length>P.label.length?-1:H.label.length<P.label.length?1:0}function p(H,P){var W=[];return R(k(P),function(ce){if(ce.slice(0,15)==="_durationLabels"){var De=ce.slice(15).toLowerCase();R(k(P[ce]),function(He){He.slice(0,1)===H&&W.push({type:De,key:He,label:P[ce][He]})})}}),W}function v(H,P,W){return P===1&&W===null?H:H+H}var w={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:v};function b(H){return Object.prototype.toString.call(H)==="[object Array]"}function M(H){return Object.prototype.toString.call(H)==="[object Object]"}function O(H,P){for(var W=H.length;W-=1;)if(P(H[W]))return H[W]}function S(H,P){var W=0,ce=H&&H.length||0,De;for(typeof P!="function"&&(De=P,P=function(He){return He===De});W<ce;){if(P(H[W]))return H[W];W+=1}}function R(H,P){var W=0,ce=H.length;if(!(!H||!ce))for(;W<ce;){if(P(H[W],W)===!1)return;W+=1}}function L(H,P){var W=0,ce=H.length,De=[];if(!H||!ce)return De;for(;W<ce;)De[W]=P(H[W],W),W+=1;return De}function E(H,P){return L(H,function(W){return W[P]})}function x(H){var P=[];return R(H,function(W){W&&P.push(W)}),P}function _(H){var P=[];return R(H,function(W){S(P,W)||P.push(W)}),P}function D(H,P){var W=[];return R(H,function(ce){R(P,function(De){ce===De&&W.push(ce)})}),_(W)}function C(H,P){var W=[];return R(H,function(ce,De){if(!P(ce))return W=H.slice(De),!1}),W}function I(H,P){var W=H.slice().reverse();return C(W,P).reverse()}function T(H,P){for(var W in P)P.hasOwnProperty(W)&&(H[W]=P[W]);return H}function k(H){var P=[];for(var W in H)H.hasOwnProperty(W)&&P.push(W);return P}function z(H,P){var W=0,ce=H.length;if(!H||!ce)return!1;for(;W<ce;){if(P(H[W],W)===!0)return!0;W+=1}return!1}function $(H){var P=[];return R(H,function(W){P=P.concat(W)}),P}function X(){var H=0;try{H.toLocaleString("i")}catch(P){return P.name==="RangeError"}return!1}function re(H){return H(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})==="3.6"}function j(H){var P=!0;return P=P&&H(1,"en",{minimumIntegerDigits:1})==="1",P=P&&H(1,"en",{minimumIntegerDigits:2})==="01",P=P&&H(1,"en",{minimumIntegerDigits:3})==="001",!(!P||(P=P&&H(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0})==="100",P=P&&H(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1})==="100.0",P=P&&H(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2})==="99.99",P=P&&H(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3})==="99.990",!P)||(P=P&&H(99.99,"en",{maximumSignificantDigits:1})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:2})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:3})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:4})==="99.99",P=P&&H(99.99,"en",{maximumSignificantDigits:5})==="99.99",!P)||(P=P&&H(1e3,"en",{useGrouping:!0})==="1,000",P=P&&H(1e3,"en",{useGrouping:!1})==="1000",!P))}function G(){var H=[].slice.call(arguments),P={},W;if(R(H,function(ye,Re){if(!Re){if(!b(ye))throw"Expected array as the first argument to durationsFormat.";W=ye}if(typeof ye=="string"||typeof ye=="function"){P.template=ye;return}if(typeof ye=="number"){P.precision=ye;return}M(ye)&&T(P,ye)}),!W||!W.length)return[];P.returnMomentTypes=!0;var ce=L(W,function(ye){return ye.format(P)}),De=D(l,_(E($(ce),"type"))),He=P.largest;return He&&(De=De.slice(0,He)),P.returnMomentTypes=!1,P.outputTypes=De,L(W,function(ye){return ye.format(P)})}function ae(){var H=[].slice.call(arguments),P=T({},this.format.defaults),W=this.asMilliseconds(),ce=this.asMonths();typeof this.isValid=="function"&&this.isValid()===!1&&(W=0,ce=0);var De=W<0,He=n.duration(Math.abs(W),"milliseconds"),ye=n.duration(Math.abs(ce),"months");R(H,function(V){if(typeof V=="string"||typeof V=="function"){P.template=V;return}if(typeof V=="number"){P.precision=V;return}M(V)&&T(P,V)});var Re={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},Se={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};P.types=l;var Et=function(V){return S(l,function(Je){return Se[Je].test(V)})},wt=new RegExp(L(l,function(V){return Se[V].source}).join("|"),"g");P.duration=this;var rt=typeof P.template=="function"?P.template.apply(P):P.template,se=P.outputTypes,et=P.returnMomentTypes,me=P.largest,he=[];se||(b(P.stopTrim)&&(P.stopTrim=P.stopTrim.join("")),P.stopTrim&&R(P.stopTrim.match(wt),function(V){var Je=Et(V);Je==="escape"||Je==="general"||he.push(Je)}));var pe=n.localeData();pe||(pe={}),R(k(w),function(V){if(typeof w[V]=="function"){pe[V]||(pe[V]=w[V]);return}pe["_"+V]||(pe["_"+V]=w[V])}),R(k(pe._durationTimeTemplates),function(V){rt=rt.replace("_"+V+"_",pe._durationTimeTemplates[V])});var ze=P.userLocale||n.locale(),Pe=P.useLeftUnits,tt=P.usePlural,ve=P.precision,ue=P.forceLength,it=P.useGrouping,Ae=P.trunc,Qe=P.useSignificantDigits&&ve>0,vt=Qe?P.precision:0,ht=vt,Ve=P.minValue,Dt=!1,Yt=P.maxValue,xt=!1,Lt=P.useToLocaleString,nn=P.groupingSeparator,zt=P.decimalSeparator,ln=P.grouping;Lt=Lt&&(r||o);var It=P.trim;b(It)&&(It=It.join(" ")),It===null&&(me||Yt||Qe)&&(It="all"),(It===null||It===!0||It==="left"||It==="right")&&(It="large"),It===!1&&(It="");var yt=function(V){return V.test(It)},Dn=/large/,yn=/small/,Oe=/both/,_t=/mid/,fn=/^all|[^sm]all/,hn=/final/,pn=me>0||z([Dn,Oe,fn],yt),at=z([yn,Oe,fn],yt),Xt=z([_t,fn],yt),Ot=z([hn,fn],yt),rn=L(rt.match(wt),function(V,Je){var We=Et(V);return V.slice(0,1)==="*"&&(V=V.slice(1),We!=="escape"&&We!=="general"&&he.push(We)),{index:Je,length:V.length,text:"",token:We==="escape"?V.replace(Se.escape,"$1"):V,type:We==="escape"||We==="general"?null:We}}),Tt={index:0,length:0,token:"",text:"",type:null},Ft=[];Pe&&rn.reverse(),R(rn,function(V){if(V.type){(Tt.type||Tt.text)&&Ft.push(Tt),Tt=V;return}Pe?Tt.text=V.token+Tt.text:Tt.text+=V.token}),(Tt.type||Tt.text)&&Ft.push(Tt),Pe&&Ft.reverse();var Be=D(l,_(x(E(Ft,"type"))));if(!Be.length)return E(Ft,"text").join("");Be=L(Be,function(V,Je){var We=Je+1===Be.length,de=!Je,st;V==="years"||V==="months"?st=ye.as(V):st=He.as(V);var Wt=Math.floor(st),dn=st-Wt,Zt=S(Ft,function(Gt){return V===Gt.type});return de&&Yt&&st>Yt&&(xt=!0),We&&Ve&&Math.abs(P.duration.as(V))<Ve&&(Dt=!0),de&&ue===null&&Zt.length>1&&(ue=!0),He.subtract(Wt,V),ye.subtract(Wt,V),{rawValue:st,wholeValue:Wt,decimalValue:We?dn:0,isSmallest:We,isLargest:de,type:V,tokenLength:Zt.length}});var Bt=Ae?Math.floor:Math.round,Pt=function(V,Je){var We=Math.pow(10,Je);return Bt(V*We)/We},Vt=!1,Nt=!1,Cn=function(V,Je){var We={useGrouping:it,groupingSeparator:nn,decimalSeparator:zt,grouping:ln,useToLocaleString:Lt};return Qe&&(vt<=0?(V.rawValue=0,V.wholeValue=0,V.decimalValue=0):(We.maximumSignificantDigits=vt,V.significantDigits=vt)),xt&&!Nt&&(V.isLargest?(V.wholeValue=Yt,V.decimalValue=0):(V.wholeValue=0,V.decimalValue=0)),Dt&&!Nt&&(V.isSmallest?(V.wholeValue=Ve,V.decimalValue=0):(V.wholeValue=0,V.decimalValue=0)),V.isSmallest||V.significantDigits&&V.significantDigits-V.wholeValue.toString().length<=0?ve<0?V.value=Pt(V.wholeValue,ve):ve===0?V.value=Bt(V.wholeValue+V.decimalValue):Qe?(Ae?V.value=Pt(V.rawValue,vt-V.wholeValue.toString().length):V.value=V.rawValue,V.wholeValue&&(vt-=V.wholeValue.toString().length)):(We.fractionDigits=ve,Ae?V.value=V.wholeValue+Pt(V.decimalValue,ve):V.value=V.wholeValue+V.decimalValue):Qe&&V.wholeValue?(V.value=Math.round(Pt(V.wholeValue,V.significantDigits-V.wholeValue.toString().length)),vt-=V.wholeValue.toString().length):V.value=V.wholeValue,V.tokenLength>1&&(ue||Vt)&&(We.minimumIntegerDigits=V.tokenLength,Nt&&We.maximumSignificantDigits<V.tokenLength&&delete We.maximumSignificantDigits),!Vt&&(V.value>0||It===""||S(he,V.type)||S(se,V.type))&&(Vt=!0),V.formattedValue=h(V.value,We,ze),We.useGrouping=!1,We.decimalSeparator=".",V.formattedValueEn=h(V.value,We,"en"),V.tokenLength===2&&V.type==="milliseconds"&&(V.formattedValueMS=h(V.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),V};if(Be=L(Be,Cn),Be=x(Be),Be.length>1){var nr=function(V){return S(Be,function(Je){return Je.type===V})},Hn=function(V){var Je=nr(V.type);Je&&R(V.targets,function(We){var de=nr(We.type);de&&parseInt(Je.formattedValueEn,10)===We.value&&(Je.rawValue=0,Je.wholeValue=0,Je.decimalValue=0,de.rawValue+=1,de.wholeValue+=1,de.decimalValue=0,de.formattedValueEn=de.wholeValue.toString(),Nt=!0)})};R(s,Hn)}return Nt&&(Vt=!1,vt=ht,Be=L(Be,Cn),Be=x(Be)),se&&!(xt&&!P.trim)?(Be=L(Be,function(V){return S(se,function(Je){return V.type===Je})?V:null}),Be=x(Be)):(pn&&(Be=C(Be,function(V){return!V.isSmallest&&!V.wholeValue&&!S(he,V.type)})),me&&Be.length&&(Be=Be.slice(0,me)),at&&Be.length>1&&(Be=I(Be,function(V){return!V.wholeValue&&!S(he,V.type)&&!V.isLargest})),Xt&&(Be=L(Be,function(V,Je){return Je>0&&Je<Be.length-1&&!V.wholeValue?null:V}),Be=x(Be)),Ot&&Be.length===1&&!Be[0].wholeValue&&!(!Ae&&Be[0].isSmallest&&Be[0].rawValue<Ve)&&(Be=[])),et?Be:(R(Ft,function(V){var Je=Re[V.type],We=S(Be,function(Gt){return Gt.type===V.type});if(!(!Je||!We)){var de=We.formattedValueEn.split(".");de[0]=parseInt(de[0],10),de[1]?de[1]=parseFloat("0."+de[1],10):de[1]=null;var st=pe.durationPluralKey(Je,de[0],de[1]),Wt=p(Je,pe),dn=!1,Zt={};R(pe._durationLabelTypes,function(Gt){var Sn=S(Wt,function(vn){return vn.type===Gt.type&&vn.key===st});Sn&&(Zt[Sn.type]=Sn.label,u(V.text,Gt.string)&&(V.text=V.text.replace(Gt.string,Sn.label),dn=!0))}),tt&&!dn&&(Wt.sort(m),R(Wt,function(Gt){if(Zt[Gt.type]===Gt.label)return u(V.text,Gt.label)?!1:void 0;if(u(V.text,Gt.label))return V.text=V.text.replace(Gt.label,Zt[Gt.type]),!1}))}}),Ft=L(Ft,function(V){if(!V.type)return V.text;var Je=S(Be,function(de){return de.type===V.type});if(!Je)return"";var We="";return Pe&&(We+=V.text),(De&&xt||!De&&Dt)&&(We+="< ",xt=!1,Dt=!1),(De&&Dt||!De&&xt)&&(We+="> ",xt=!1,Dt=!1),De&&(Je.value>0||It===""||S(he,Je.type)||S(se,Je.type))&&(We+="-",De=!1),V.type==="milliseconds"&&Je.formattedValueMS?We+=Je.formattedValueMS:We+=Je.formattedValue,Pe||(We+=V.text),We}),Ft.join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function ie(){var H=this.duration,P=function(He){return H._data[He]},W=S(this.types,P),ce=O(this.types,P);switch(W){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(W===ce)return"d __";case"weeks":return W===ce?"w __":(this.trim===null&&(this.trim="both"),"w __, d __, h __");case"months":if(W===ce)return"M __";case"years":return W===ce?"y __":(this.trim===null&&(this.trim="both"),"y __, M __, d __");default:return this.trim===null&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function le(H){if(!H)throw"Moment Duration Format init cannot find moment instance.";H.duration.format=G,H.duration.fn.format=ae,H.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:ie,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},H.updateLocale("en",w)}var fe=function(H,P,W){return H.toLocaleString(P,W)};r=X()&&j(fe),i=r&&re(fe);var Q=function(H,P,W){if(typeof window<"u"&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(P,W).format(H)};return o=j(Q),a=o&&re(Q),le(n),le})}(ta)),ta.exports}s1();const l1=["true","t","yes","y","on","1"],u1=["false","f","no","n","off","0"];function Ht(e,t=""){return{kind:te.Text,readonly:!0,allowOverlay:!0,data:e,displayData:e,errorDetails:t,isError:!0,style:"faded"}}function yi(e){return Object.hasOwn(e,"isError")&&e.isError}function c1(e){return Object.hasOwn(e,"tooltip")&&e.tooltip!==""}function Ma(e){return Object.hasOwn(e,"isMissingValue")&&e.isMissingValue}function Ts(e=!1){return e?{kind:te.Loading,allowOverlay:!1,isMissingValue:!0}:{kind:te.Loading,allowOverlay:!1}}function d1(e,t){const n=t?"faded":"normal";return{kind:te.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function Ds(e){return{id:e.id,title:e.title,hasMenu:!1,menuIcon:"dots",themeOverride:e.themeOverride,icon:e.icon,group:e.group,...e.isStretched&&!e.isPinned&&{grow:1},...e.width&&{width:e.width}}}function po(e,t){return $e(e)?t||{}:$e(t)?e||{}:Fc(e,t)}function Ud(e){if($e(e))return[];if(typeof e=="number"||typeof e=="boolean")return[e];if(typeof e=="string"){if(e==="")return[];if(e.trim().startsWith("[")&&e.trim().endsWith("]"))try{return JSON.parse(e)}catch{return[e]}else return e.split(",")}try{const t=JSON.parse(JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r));return Array.isArray(t)?t.map(n=>["string","number","boolean","null"].includes(typeof n)?n:bt(n)):[bt(t)]}catch{return[bt(e)]}}function f1(e){return e&&e.startsWith("{")&&e.endsWith("}")}function bt(e){try{try{return o1(e)}catch{return JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r)}}catch{return`[${typeof e}]`}}function qd(e){if($e(e))return null;if(typeof e=="boolean")return e;const t=bt(e).toLowerCase().trim();if(t==="")return null;if(l1.includes(t))return!0;if(u1.includes(t))return!1}function uo(e){if($e(e))return null;if(Array.isArray(e))return NaN;if(typeof e=="string"){if(e.trim().length===0)return null;try{const t=Zi.unformat(e.trim());if(pt(t))return t}catch{}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function ha(e){if($e(e))return"";if(typeof e=="string")return e;try{return JSON.stringify(e,(t,n)=>typeof n=="bigint"?Number(n):n)}catch{return bt(e)}}function h1(e){if(e===0||Math.abs(e)>=1e-4)return 4;const n=e.toExponential().split("e");return Math.abs(parseInt(n[1],10))}function fi(e,t={}){const n=navigator.languages;try{return new Intl.NumberFormat(n,t).format(e)}catch(r){if(r instanceof RangeError)return new Intl.NumberFormat(void 0,t).format(e);throw r}}function ga(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":$e(t)||t===""?pt(n)?(n===0&&(e=Math.round(e)),Zi(e).format({thousandSeparated:!1,mantissa:n,trimMantissa:!1})):Zi(e).format({thousandSeparated:!1,mantissa:h1(e),trimMantissa:!0}):t==="plain"?Zi(e).format({thousandSeparated:!1,mantissa:20,trimMantissa:!0}):t==="localized"?fi(e):t==="percent"?fi(e,{style:"percent",minimumFractionDigits:0,maximumFractionDigits:2}):t==="dollar"?fi(e,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol",maximumFractionDigits:2}):t==="euro"?fi(e,{style:"currency",currency:"EUR",maximumFractionDigits:2}):t==="yen"?fi(e,{style:"currency",currency:"JPY",maximumFractionDigits:0}):["compact","scientific","engineering"].includes(t)?fi(e,{notation:t}):t==="accounting"?Zi(e).format({thousandSeparated:!0,negative:"parenthesis",mantissa:2,trimMantissa:!1}):vg.sprintf(t,e)}function Bu(e,t,n="datetime"){if(t==="localized"){const r=navigator.languages,i=n==="time"?void 0:"medium",o=n==="date"?void 0:"medium";try{return new Intl.DateTimeFormat(r,{dateStyle:i,timeStyle:o}).format(e.toDate())}catch(a){if(a instanceof RangeError)return new Intl.DateTimeFormat(void 0,{dateStyle:i,timeStyle:o}).format(e.toDate());throw a}}else{if(t==="distance")return e.fromNow();if(t==="calendar")return e.calendar();if(t==="iso8601")return n==="date"?e.format("YYYY-MM-DD"):n==="time"?e.format("HH:mm:ss.SSS[Z]"):e.toISOString()}return e.format(t)}function Yo(e){if($e(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if(typeof e=="string"&&e.trim().length===0)return null;try{const t=Number(e);if(!isNaN(t)){let n=t;t>=10**18?n=t/1e3**3:t>=10**15?n=t/1e3**2:t>=10**12&&(n=t/1e3);const r=qr.unix(n).utc();if(r.isValid())return r.toDate()}if(typeof e=="string"){const n=qr.utc(e);if(n.isValid())return n.toDate();const r=qr.utc(e,[qr.HTML5_FMT.TIME_MS,qr.HTML5_FMT.TIME_SECONDS,qr.HTML5_FMT.TIME]);if(r.isValid())return r.toDate()}}catch{return}}function Yd(e){if(e%1===0)return 0;let t=e.toString();return t.indexOf("e")!==-1&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),t.indexOf(".")===-1?0:t.split(".")[1].length}function g1(e,t){if(!Number.isFinite(e))return e;if(t<=0)return Math.trunc(e);const n=10**t,r=e*n,i=Number.EPSILON*Math.abs(r)*10;return Math.trunc(r+Math.sign(r)*i)/n}const m1=new RegExp(/(\r\n|\n|\r)/gm);function co(e){return e.indexOf(`
`)!==-1?e.replace(m1," "):e}function p1(e,t){if($e(t))return"";try{const n=t.match(e);return n&&n[1]!==void 0?decodeURIComponent(n[1].replace(/\+/g,"%20")):t}catch{return t}}const v1=xi("div",{target:"e48kivf0"})(({theme:e})=>({overflowY:"auto",padding:e.spacing.sm,".react-json-view .copy-icon svg":{fontSize:"0.9em !important",marginRight:`${e.spacing.threeXS} !important`,verticalAlign:"middle !important"}})),Xd=({jsonValue:e,theme:t})=>{let n;if(e)try{n=typeof e=="string"?Xa.parse(e):Xa.parse(Xa.stringify(e))}catch{n=void 0}return $e(n)?lt(Zr,{highlight:!0,autoFocus:!1,disabled:!0,value:ha(e)??"",onChange:()=>{}}):lt(v1,{"data-testid":"stJsonColumnViewer",children:lt(Rh,{src:n,collapsed:2,theme:Eh(t.bgCell)>.5?"rjv-default":"monokai",displayDataTypes:!1,displayObjectSize:!1,name:!1,enableClipboard:!0,style:{fontFamily:t.fontFamily,fontSize:t.baseFontStyle,backgroundColor:t.bgCell,whiteSpace:"pre-wrap"}})})},b1=e=>{const t=e.theme,n=e.value.data;return lt(Xd,{jsonValue:n.value||n.displayValue,theme:t})},w1=e=>{const t=e.theme,n=e.value;return lt(Xd,{jsonValue:n.data,theme:t})},y1={kind:te.Custom,isMatch:e=>e.data.kind==="json-cell",draw:(e,t)=>{const{value:n,displayValue:r}=t.data;return qs(e,r??ha(n)??"",t.contentAlign),!0},measure:(e,t,n)=>{const{value:r,displayValue:i}=t.data,o=i??ha(r)??"";return(o?e.measureText(o).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:b1})},C1="line_chart",S1="area_chart",x1="bar_chart";function Js(e,t,n){const r=po({y_min:null,y_max:null},t.columnTypeOptions),i={kind:te.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[r.y_min??0,r.y_max??1]}};return{...t,kind:e,sortMode:"default",isEditable:!1,getCell(o){if($e(o))return Ts();const a=Ud(o),l=[];let s=[];if(a.length===0)return Ts();let u=Number.MIN_SAFE_INTEGER,c=Number.MAX_SAFE_INTEGER;for(let h=0;h<a.length;h++){const m=uo(a[h]);if(Number.isNaN(m)||$e(m))return Ht(bt(a),`The value cannot be interpreted as a numeric array. ${bt(m)} is not a number.`);m>u&&(u=m),m<c&&(c=m),l.push(m)}let f,g;if(a.length===1){let h;u<=0?h=u===0?1:0:h=u,f=r.y_max??h,g=r.y_min??(u>=0?0:u)}else f=r.y_max??u,g=r.y_min??c;return $e(g)||$e(f)||Number.isNaN(g)||Number.isNaN(f)||g>=f?Ht("Invalid min/max y-axis configuration",`The y_min (${g}) and y_max (${f}) configuration options must be valid numbers.`):(l.length>0&&(u>f||c<g)?s=l.map(h=>u-c===0?u>(f||1)?f:g:(f-g)*((h-c)/(u-c))+g):s=l,{...i,copyData:l.join(","),data:{...i.data,values:s,displayValues:l.map(h=>ga(h)),yAxis:[g,f]},isMissingValue:$e(o)})},getCellValue(o){var a,l;return o.kind===te.Loading||((a=o.data)==null?void 0:a.values)===void 0?null:(l=o.data)==null?void 0:l.values}}}function jd(e){return Js(C1,e,"line")}jd.isEditableType=!1;function Gd(e){return Js(x1,e,"bar")}Gd.isEditableType=!1;function Kd(e){return Js(S1,e,"area")}Kd.isEditableType=!1;function Qs(e){const t={kind:te.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:"normal"};return{...e,kind:"checkbox",sortMode:"default",getCell(n){let r=null;return r=qd(n),r===void 0?Ht(bt(n),"The value cannot be interpreted as boolean."):{...t,data:r,isMissingValue:$e(r)}},getCellValue(n){return n.data===void 0?null:n.data}}}Qs.isEditableType=!0;function Wu(e,t){return t.startsWith("+")||t.startsWith("-")?e=e.utcOffset(t,!1):e=e.tz(t),e}function el(e,t,n,r,i,o,a){var h,m;const l=po({format:n,step:r,timezone:a},t.columnTypeOptions);let s;if(pt(l.timezone))try{s=((h=Wu(Ol(),l.timezone))==null?void 0:h.utcOffset())||void 0}catch{}let u;pt(l.min_value)&&(u=Yo(l.min_value)||void 0);let c;pt(l.max_value)&&(c=Yo(l.max_value)||void 0);const f={kind:te.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isPinned?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:((m=l.step)==null?void 0:m.toString())||"1",format:i,min:u,max:c}},g=p=>{const v=Yo(p);return v===null?!t.isRequired:!(v===void 0||pt(u)&&o(v)<o(u)||pt(c)&&o(v)>o(c))};return{...t,kind:e,sortMode:"default",validateInput:g,getCell(p,v){if(v===!0){const S=g(p);if(S===!1)return Ht(bt(p),"Invalid input.");S instanceof Date&&(p=S)}const w=Yo(p);let b="",M="",O=s;if(w===void 0)return Ht(bt(p),"The value cannot be interpreted as a datetime object.");if(w!==null){let S=Ol.utc(w);if(!S.isValid())return Ht(bt(w),`Invalid moment date. This should never happen. Please report this bug. 
Error: ${S.toString()}`);if(l.timezone){try{S=Wu(S,l.timezone)}catch(R){return Ht(S.toISOString(),`Failed to adjust to the provided timezone: ${l.timezone}. 
Error: ${R}`)}O=S.utcOffset()}try{M=Bu(S,l.format||n,e)}catch(R){return Ht(S.toISOString(),`Failed to format the date for rendering with: ${l.format}. 
Error: ${R}`)}b=Bu(S,n,e)}return{...f,copyData:b,isMissingValue:$e(w),data:{...f.data,date:w,displayDate:M,timezoneOffset:O}}},getCellValue(p){var v;return $e((v=p==null?void 0:p.data)==null?void 0:v.date)?null:o(p.data.date)}}}function tl(e){var i,o,a;let t="YYYY-MM-DD HH:mm:ss";((i=e.columnTypeOptions)==null?void 0:i.step)>=60?t="YYYY-MM-DD HH:mm":((o=e.columnTypeOptions)==null?void 0:o.step)<1&&(t="YYYY-MM-DD HH:mm:ss.SSS");const n=Ih(e.arrowType),r=pt(n)||pt((a=e==null?void 0:e.columnTypeOptions)==null?void 0:a.timezone);return el("datetime",e,r?t+"Z":t,1,"datetime-local",l=>r?l.toISOString():l.toISOString().replace("Z",""),n)}tl.isEditableType=!0;function nl(e){var n,r;let t="HH:mm:ss";return((n=e.columnTypeOptions)==null?void 0:n.step)>=60?t="HH:mm":((r=e.columnTypeOptions)==null?void 0:r.step)<1&&(t="HH:mm:ss.SSS"),el("time",e,t,1,"time",i=>i.toISOString().split("T")[1].replace("Z",""))}nl.isEditableType=!0;function rl(e){return el("date",e,"YYYY-MM-DD",1,"date",t=>t.toISOString().split("T")[0])}rl.isEditableType=!0;function Zd(e){const t={kind:te.Image,data:[],displayData:[],readonly:!0,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:"normal"};return{...e,kind:"image",sortMode:"default",isEditable:!1,getCell(n){const r=pt(n)?[bt(n)]:[];return{...t,data:r,isMissingValue:!pt(n),displayData:r}},getCellValue(n){return n.data===void 0||n.data.length===0?null:n.data[0]}}}Zd.isEditableType=!1;function Jd(e){const t={kind:te.Custom,allowOverlay:!0,contentAlign:e.contentAlignment,readonly:!0,style:e.isPinned?"faded":"normal",copyData:"",data:{kind:"json-cell",value:""}};return{...e,kind:"json",sortMode:"default",isEditable:!1,getCell(n){try{const r=pt(n)?co(ha(n)):"";return{...t,copyData:r,isMissingValue:$e(n),data:{...t.data,value:n,displayValue:r}}}catch(r){return Ht(bt(n),`The value cannot be interpreted as a JSON string. Error: ${r}`)}},getCellValue(n){var r;return((r=n.data)==null?void 0:r.value)??null}}}Jd.isEditableType=!1;function Qd(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(a){n=`Invalid validate regex: ${t.validate}.
Error: ${a}`}let r;if(!$e(t.display_text)&&t.display_text.includes("(")&&t.display_text.includes(")"))try{r=new RegExp(t.display_text,"us")}catch{r=void 0}const i={kind:te.Uri,readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal",hoverEffect:!0,data:"",displayData:"",copyData:""},o=a=>{if($e(a))return!e.isRequired;const l=bt(a);return!(t.max_chars&&l.length>t.max_chars||n instanceof RegExp&&n.test(l)===!1)};return{...e,kind:"link",sortMode:"default",validateInput:o,getCell(a,l){if($e(a))return{...i,data:null,isMissingValue:!0,onClickUri:()=>{}};const s=a;if(typeof n=="string")return Ht(bt(s),n);if(l&&o(s)===!1)return Ht(bt(s),"Invalid input.");let u="";return s&&(r!==void 0?u=p1(r,s):u=t.display_text||s),{...i,data:s,displayData:u,isMissingValue:$e(s),onClickUri:c=>{window.open(s.startsWith("www.")?`https://${s}`:s,"_blank","noopener,noreferrer"),c.preventDefault()},copyData:s}},getCellValue(a){return $e(a.data)?null:a.data}}}Qd.isEditableType=!0;function il(e){const t={kind:te.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal"};return{...e,kind:"list",sortMode:"default",isEditable:!1,getCell(n){const r=$e(n)?[]:Ud(n);return{...t,data:r,isMissingValue:$e(n),copyData:$e(n)?"":bt(r.map(i=>typeof i=="string"&&i.includes(",")?i.replace(/,/g," "):i))}},getCellValue(n){return $e(n.data)||Ma(n)?null:n.data}}}il.isEditableType=!1;function ol(e){const t=po({step:Ac(e.arrowType)?1:void 0,min_value:Th(e.arrowType)?0:void 0},e.columnTypeOptions),n=!t.format&&(Dh(e.arrowType)||Oh(e.arrowType)),r=$e(t.min_value)||t.min_value<0,i=pt(t.step)&&!Number.isNaN(t.step)?Yd(t.step):void 0,o={kind:te.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||n?"left":"right",style:e.isPinned?"faded":"normal",allowNegative:r,fixedDecimals:i,thousandSeparator:""},a=l=>{let s=uo(l);if($e(s))return!e.isRequired;if(Number.isNaN(s))return!1;let u=!1;return pt(t.max_value)&&s>t.max_value&&(s=t.max_value,u=!0),pt(t.min_value)&&s<t.min_value?!1:u?s:!0};return{...e,kind:"number",sortMode:"smart",validateInput:a,getCell(l,s){if(s===!0){const f=a(l);if(f===!1)return Ht(bt(l),"Invalid input.");typeof f=="number"&&(l=f)}let u=uo(l),c="";if(pt(u)){if(Number.isNaN(u))return Ht(bt(l),"The value cannot be interpreted as a number.");if(pt(i)&&(u=g1(u,i)),Number.isInteger(u)&&!Number.isSafeInteger(u))return Ht(bt(l),"The value is larger than the maximum supported integer values in number columns (2^53).");try{n?c=Ss(u,e.arrowType):c=ga(u,t.format,i)}catch(f){return Ht(bt(u),pt(t.format)?`Failed to format the number based on the provided format configuration: (${t.format}). Error: ${f}`:`Failed to format the number. Error: ${f}`)}}return{...o,data:u,displayData:c,isMissingValue:$e(u),copyData:$e(u)?"":bt(u)}},getCellValue(l){return l.data===void 0?null:l.data}}}ol.isEditableType=!0;function fo(e){const t={kind:te.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!0,style:e.isPinned?"faded":"normal"};return{...e,kind:"object",sortMode:"default",isEditable:!1,getCell(n){try{const r=pt(n)?bt(n):null,i=pt(r)?co(r):"";return{...t,data:r,displayData:i,isMissingValue:$e(n)}}catch(r){return Ht(bt(n),`The value cannot be interpreted as a string. Error: ${r}`)}},getCellValue(n){return n.data===void 0?null:n.data}}}fo.isEditableType=!1;function ef(e){const t=Ac(e.arrowType),n=po({min_value:0,max_value:t?100:1,step:t?1:.01,format:t?"%3d%%":"percent"},e.columnTypeOptions);let r;try{r=ga(n.max_value,n.format)}catch{r=bt(n.max_value)}const i=$e(n.step)||Number.isNaN(n.step)?void 0:Yd(n.step),o={kind:te.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,readonly:!0,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step,value:n.min_value,label:String(n.min_value),measureLabel:r}};return{...e,kind:"progress",sortMode:"smart",isEditable:!1,getCell(a){if($e(a))return Ts();if($e(n.min_value)||$e(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return Ht("Invalid min/max parameters",`The min_value (${n.min_value}) and max_value (${n.max_value}) parameters must be valid numbers.`);if($e(n.step)||Number.isNaN(n.step))return Ht("Invalid step parameter",`The step parameter (${n.step}) must be a valid number.`);const l=uo(a);if(Number.isNaN(l)||$e(l))return Ht(bt(a),"The value cannot be interpreted as a number.");if(Number.isInteger(l)&&!Number.isSafeInteger(l))return Ht(bt(a),"The value is larger than the maximum supported integer values in number columns (2^53).");let s="";try{s=ga(l,n.format,i)}catch(c){return Ht(bt(l),pt(n.format)?`Failed to format the number based on the provided format configuration: (${n.format}). Error: ${c}`:`Failed to format the number. Error: ${c}`)}const u=Math.min(n.max_value,Math.max(n.min_value,l));return{...o,isMissingValue:$e(a),copyData:String(l),data:{...o.data,value:u,label:s}}},getCellValue(a){var l,s;return a.kind===te.Loading||((l=a.data)==null?void 0:l.value)===void 0?null:(s=a.data)==null?void 0:s.value}}}ef.isEditableType=!1;function al(e){let t="string";const n=po({options:Hc(e.arrowType)?[!0,!1]:e.arrowType.categoricalOptions??[]},e.columnTypeOptions),r=new Set(n.options.map(o=>typeof o));r.size===1&&(r.has("number")||r.has("bigint")?t="number":r.has("boolean")&&(t="boolean"));const i={kind:te.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isPinned?"faded":"normal",data:{kind:"dropdown-cell",allowedValues:[...e.isRequired!==!0?[null]:[],...n.options.filter(o=>o!==null&&o!=="").map(o=>bt(o))],value:""}};return{...e,kind:"selectbox",sortMode:"default",getCell(o,a){let l=null;return pt(o)&&o!==""&&(l=bt(o)),a&&!i.data.allowedValues.includes(l)?Ht(bt(l),"The value is not part of the allowed options."):{...i,isMissingValue:l===null,copyData:l||"",data:{...i.data,value:l}}},getCellValue(o){var a,l,s,u,c;return $e((a=o.data)==null?void 0:a.value)||((l=o.data)==null?void 0:l.value)===""?null:t==="number"?uo((s=o.data)==null?void 0:s.value)??null:t==="boolean"?qd((u=o.data)==null?void 0:u.value)??null:(c=o.data)==null?void 0:c.value}}}al.isEditableType=!0;function sl(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(o){n=`Invalid validate regex: ${t.validate}.
Error: ${o}`}const r={kind:te.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!e.isEditable,style:e.isPinned?"faded":"normal"},i=o=>{if($e(o))return!e.isRequired;let a=bt(o),l=!1;return t.max_chars&&a.length>t.max_chars&&(a=a.slice(0,t.max_chars),l=!0),n instanceof RegExp&&n.test(a)===!1?!1:l?a:!0};return{...e,kind:"text",sortMode:"default",validateInput:i,getCell(o,a){if(typeof n=="string")return Ht(bt(o),n);if(a){const l=i(o);if(l===!1)return Ht(bt(o),"Invalid input.");typeof l=="string"&&(o=l)}try{const l=pt(o)?bt(o):null,s=pt(l)?co(l):"";return{...r,isMissingValue:$e(l),data:l,displayData:s}}catch(l){return Ht("Incompatible value",`The value cannot be interpreted as string. Error: ${l}`)}},getCellValue(o){return o.data===void 0?null:o.data}}}sl.isEditableType=!0;const Uu=xi("img",{target:"e17fx5ar0"})({maxWidth:"100%",maxHeight:"37.5rem",objectFit:"scale-down"}),k1=({urls:e})=>{const t=e&&e.length>0?e[0]:"";return t.startsWith("http")?lt("a",{href:t,target:"_blank",rel:"noreferrer noopener",children:lt(Uu,{src:t})}):lt(Uu,{src:t})},qu=new Map(Object.entries({object:fo,text:sl,checkbox:Qs,selectbox:al,list:il,number:ol,link:Qd,datetime:tl,date:rl,time:nl,line_chart:jd,bar_chart:Gd,area_chart:Kd,image:Zd,progress:ef,json:Jd})),M1=[y1];var R1=zc();const E1=wr(R1);var gs,Yu;function I1(){if(Yu)return gs;Yu=1;var e=Ph(),t=Lh(),n=_h(),r=zc(),i=Fh(),o=Ah(),a=Hh(),l=zh(),s="[object Map]",u="[object Set]",c=Object.prototype,f=c.hasOwnProperty;function g(h){if(h==null)return!0;if(i(h)&&(r(h)||typeof h=="string"||typeof h.splice=="function"||o(h)||l(h)||n(h)))return!h.length;var m=t(h);if(m==s||m==u)return!h.size;if(a(h))return!e(h).length;for(var p in h)if(f.call(h,p))return!1;return!0}return gs=g,gs}var T1=I1();const D1=wr(T1);function Xu(e,t,n){const r=new RegExp(`${e}[,\\s].*{(?:[^}]*[\\s;]{1})?${t}:\\s*([^;}]+)[;]?.*}`,"gm");n=n.replace(/{/g," {");const i=r.exec(n);if(i)return i[1].trim()}function O1(e,t,n){const r={},i=Xu(t,"color",n);i&&(r.textDark=i,e.kind===te.Bubble&&(r.textBubble=i),e.kind===te.Uri&&(r.linkColor=i));const o=Xu(t,"background-color",n);return o&&(r.bgCell=o),o==="yellow"&&i===void 0&&(r.textDark="#31333F"),r?{...e,themeOverride:r}:e}function P1(e){return Uh(e)||qh(e)?sl:Yh(e)?tl:Vc(e)?nl:Xh(e)?rl:jh(e)||Gh(e)?fo:Hc(e)?Qs:Kh(e)?ol:Zh(e)?al:Jh(e)?il:fo}function tf(e){const t=e.length>0?e[e.length-1]:"",n=e.length>1?e.slice(0,-1).filter(r=>r!=="").join(" / "):void 0;return{title:t,group:n}}function ll(e){return{group:void 0,isEditable:!1,isIndex:!1,isPinned:!1,isHidden:!1,isStretched:!1,...e}}function L1(e,t){const n=e.columnNames.map(l=>l[t]),{title:r,group:i}=tf(n),o=e.columnTypes[t];let a=!0;return Wh(o)&&(a=!1),ll({id:`_index-${t}`,indexNumber:t,name:r,title:r,group:i,isEditable:a,arrowType:o,isIndex:!0,isPinned:!0})}function _1(e,t){const n=e.columnNames.map(a=>a[t]),{title:r,group:i}=tf(n),o=e.columnTypes[t];return ll({id:`_column-${r}-${t}`,indexNumber:t,name:r,isEditable:!0,title:r,arrowType:o,group:i})}function nf(){return ll({id:"_empty-index",indexNumber:0,title:"",name:"",isEditable:!1,isIndex:!0,isPinned:!0,arrowType:{type:Qh.INDEX,arrowField:new Vh("",new Nh,!0),pandasType:void 0}})}function ju(e){const t=[],{dimensions:n}=e,r=n.numIndexColumns,i=n.numDataColumns;if(r===0&&i===0)return t.push(nf()),t;for(let o=0;o<r;o++)t.push(L1(e,o));for(let o=0;o<i;o++)t.push(_1(e,o+r));return t}function F1(e,t,n,r=void 0){var o,a,l,s,u,c;let i;if(e.kind==="object"||e.kind==="json")i=e.getCell(pt(t.content)?co(Ss(t.content,t.contentType)):null);else if(["time","date","datetime"].includes(e.kind)&&pt(t.content)&&(typeof t.content=="number"||typeof t.content=="bigint")){let f;Vc(t.contentType)&&pt((a=(o=t.field)==null?void 0:o.type)==null?void 0:a.unit)?f=$h(t.content,t.field):f=qr.utc(Number(t.content)).toDate(),i=e.getCell(f)}else if(Bh(t.contentType)){const f=$e(t.content)?null:Ss(t.content,t.contentType);i=e.getCell(f)}else i=e.getCell(t.content);if(yi(i))return i;if(!e.isEditable){if(n&&pt(n==null?void 0:n.displayContent)){const f=co(n.displayContent);i.kind===te.Text?i={...i,displayData:f}:i.kind===te.Number&&$e((l=e.columnTypeOptions)==null?void 0:l.format)?i={...i,displayData:f}:i.kind===te.Uri&&$e((s=e.columnTypeOptions)==null?void 0:s.display_text)?i={...i,displayData:f}:i.kind===te.Custom&&((u=i.data)==null?void 0:u.kind)==="date-picker-cell"&&$e((c=e.columnTypeOptions)==null?void 0:c.format)&&(i={...i,data:{...i.data,displayDate:f}})}r&&(n!=null&&n.cssId)&&(i=O1(i,n.cssId,r))}return i}const na="_index",Gu="_pos:",Ku={small:75,medium:200,large:400},rf=As.getLogger("useColumnLoader");function A1(e){if(!$e(e)){if(typeof e=="number")return e;if(e in Ku)return Ku[e]}}const Xo=(e,t)=>pg(e,t,(r,i)=>{if(E1(i))return i});function Zu(e,t){if(!t)return e;let n={};return e.isIndex&&t.has(na)&&(n=Xo(n,t.get(na)??{})),t.has(`${Gu}${e.indexNumber}`)&&(n=Xo(n,t.get(`${Gu}${e.indexNumber}`)??{})),t.has(e.name)&&e.name!==na&&(n=Xo(n,t.get(e.name)??{})),t.has(e.id)&&(n=Xo(n,t.get(e.id)??{})),D1(n)?e:Fc({...e},{title:n.label,width:A1(n.width),isEditable:pt(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isPinned:n.pinned,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help})}function H1(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return rf.error(t),new Map}}function Ju(e){var r;const t=(r=e.columnTypeOptions)==null?void 0:r.type;let n;return pt(t)&&(qu.has(t)?n=qu.get(t):rf.warn(`Unknown column type configured in column configuration: ${t}`)),$e(n)&&(n=P1(e.arrowType)),n}function z1(e,t,n,r){const i=Gr(),o=d.useMemo(()=>H1(e.columns),[e.columns]),[a,l]=d.useState(o);d.useEffect(()=>{l(o)},[o]);const s=e.useContainerWidth||pt(e.width)&&e.width>0,u=pt(e.rowHeight)&&e.rowHeight>Wn("4rem"),c=d.useMemo(()=>ju(t).map(g=>{let h={...g,...Zu(g,a),isStretched:s};const m=Ju(h);return(e.editingMode===Mn.EditingMode.READ_ONLY||n||m.isEditableType===!1)&&(h={...h,isEditable:!1}),e.editingMode!==Mn.EditingMode.READ_ONLY&&h.isEditable==!0&&(h={...h,icon:"editable"},h.isRequired&&e.editingMode===Mn.EditingMode.DYNAMIC&&(h={...h,isHidden:!1})),m(h,i)}),[t,a,s,e.editingMode,n,i]);return{columns:d.useMemo(()=>{const g=ju(t).map(v=>{let w={...v,...Zu(v,a),isStretched:s,isWrappingAllowed:u};const b=Ju(w);return(e.editingMode===Mn.EditingMode.READ_ONLY||n||b.isEditableType===!1)&&(w={...w,isEditable:!1}),e.editingMode!==Mn.EditingMode.READ_ONLY&&w.isEditable==!0&&(w={...w,icon:"editable"},w.isRequired&&e.editingMode===Mn.EditingMode.DYNAMIC&&(w={...w,isHidden:!1})),b(w,i)}).filter(v=>!v.isHidden),h=[],m=[];r!=null&&r.length?(g.forEach(v=>{v.isIndex&&!r.includes(v.name)&&!r.includes(v.id)&&v.isPinned!==!1&&h.push(v)}),r.forEach(v=>{const w=g.find(b=>b.name===v||b.id===v);w&&(w.isPinned?h.push(w):m.push(w))})):g.forEach(v=>{v.isPinned?h.push(v):m.push(v)});const p=[...h,...m];return p.length>0?p:[fo(nf())]},[t,a,u,s,n,e.editingMode,r,i]),allColumns:c,setColumnConfigMapping:l}}function oo(e){return e.isIndex?na:$e(e.name)?"":e.name}class jo{constructor(t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=t}toJson(t){const n=new Map;t.forEach(o=>{n.set(o.indexNumber,o)});const r={edited_rows:{},added_rows:[],deleted_rows:[]};return this.editedCells.forEach((o,a,l)=>{const s={};o.forEach((u,c)=>{const f=n.get(c);f&&(s[oo(f)]=f.getCellValue(u))}),r.edited_rows[a]=s}),this.addedRows.forEach(o=>{const a={};let l=!1;o.forEach((s,u,c)=>{const f=n.get(u);if(f){const g=f.getCellValue(s);f.isRequired&&f.isEditable&&Ma(s)&&(l=!0),pt(g)&&(a[oo(f)]=g)}}),l||r.added_rows.push(a)}),r.deleted_rows=this.deletedRows,JSON.stringify(r,(o,a)=>a===void 0?null:a)}fromJson(t,n){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const r=JSON.parse(t),i=new Map;n.forEach(a=>{i.set(a.indexNumber,a)});const o=new Map;n.forEach(a=>{o.set(oo(a),a)}),Object.keys(r.edited_rows).forEach(a=>{const l=Number(a),s=r.edited_rows[a];Object.keys(s).forEach(u=>{var g;const c=s[u],f=o.get(u);if(f){const h=f.getCell(c);h&&(this.editedCells.has(l)||this.editedCells.set(l,new Map),(g=this.editedCells.get(l))==null||g.set(f.indexNumber,h))}})}),r.added_rows.forEach(a=>{const l=new Map;n.forEach(s=>{l.set(s.indexNumber,s.getCell(null))}),Object.keys(a).forEach(s=>{const u=a[s],c=o.get(s);if(c){const f=c.getCell(u);f&&l.set(c.indexNumber,f)}}),this.addedRows.push(l)}),this.deletedRows=r.deleted_rows}isAddedRow(t){return t>=this.numRows}getCell(t,n){if(this.isAddedRow(n))return this.addedRows[n-this.numRows].get(t);const r=this.editedCells.get(n);if(r!==void 0)return r.get(t)}setCell(t,n,r){if(this.isAddedRow(n)){if(n-this.numRows>=this.addedRows.length)return;this.addedRows[n-this.numRows].set(t,r)}else this.editedCells.get(n)===void 0&&this.editedCells.set(n,new Map),this.editedCells.get(n).set(t,r)}addRow(t){this.addedRows.push(t)}deleteRows(t){t.sort((n,r)=>r-n).forEach(n=>{this.deleteRow(n)})}deleteRow(t){if(!($e(t)||t<0)){if(this.isAddedRow(t)){this.addedRows.splice(t-this.numRows,1);return}this.deletedRows.includes(t)||(this.deletedRows.push(t),this.deletedRows=this.deletedRows.sort((n,r)=>n-r)),this.editedCells.delete(t)}}getOriginalRowIndex(t){let n=t;for(let r=0;r<this.deletedRows.length&&!(this.deletedRows[r]>n);r++)n+=1;return n}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}}const ho=({columnId:e,columnConfigMapping:t,updatedProps:n})=>{const r=new Map(t),i=r.get(e),o={...i||{},...n||{}};return(i!=null&&i.type_config||n!=null&&n.type_config)&&(o.type_config={...(i==null?void 0:i.type_config)||{},...(n==null?void 0:n.type_config)||{}}),r.set(e,o),r};function V1(e){return{changeColumnFormat:d.useCallback((n,r)=>{e(i=>ho({columnId:n,columnConfigMapping:i,updatedProps:{type_config:{format:r}}}))},[e])}}function N1(e,t,n,r,i,o){const a=d.useMemo(()=>e.filter(c=>c.isPinned).reduce((c,f)=>c+(f.width??r*2),0)>n*.6,[e,n,r]),l=t||a?0:e.filter(c=>c.isPinned).length,s=d.useCallback(c=>{o(f=>ho({columnId:c,columnConfigMapping:f,updatedProps:{pinned:!1}})),i(!0,!1)},[i,o]);return{pinColumn:d.useCallback(c=>{o(f=>ho({columnId:c,columnConfigMapping:f,updatedProps:{pinned:!0}})),i(!0,!1)},[i,o]),unpinColumn:s,freezeColumns:l}}function $1(e,t,n,r,i){return{onColumnMoved:d.useCallback((a,l)=>{const s=[...e],[u]=s.splice(a,1);s.splice(l,0,u),l<t&&!u.isPinned?n(u.id):l>=t&&u.isPinned&&r(u.id),i(s.map(c=>c.id))},[e,t,n,r,i])}}function B1(e){const[t,n]=d.useState(()=>new Map),r=d.useCallback((o,a,l,s)=>{o.id&&n(new Map(t).set(o.id,s))},[t]);return{columns:d.useMemo(()=>e.map(o=>o.id&&t.has(o.id)&&t.get(o.id)!==void 0?{...o,width:t.get(o.id),grow:0}:o),[e,t]),onColumnResize:r}}function W1(e){var t,n;switch(e.kind){case te.Number:return((t=e.data)==null?void 0:t.toString())??"";case te.Boolean:return((n=e.data)==null?void 0:n.toString())??"";case te.Markdown:case te.RowID:case te.Text:case te.Uri:return e.data??"";case te.Bubble:case te.Image:return e.data.join("");case te.Drilldown:return e.data.map(r=>r.text).join("");case te.Protected:case te.Loading:return"";case te.Custom:return e.copyData}}function Qu(e){if(typeof e=="number")return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function U1(e,t){return e=Qu(e),t=Qu(t),typeof e=="string"&&typeof t=="string"?e.localeCompare(t):typeof e=="number"&&typeof t=="number"?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}function q1(e,t){return e>t?1:e===t?0:-1}function Y1(e){const{sort:t,rows:n,getCellContent:r}=e;let i=t===void 0?void 0:e.columns.findIndex(u=>t.column===u||u.id!==void 0&&t.column.id===u.id);i===-1&&(i=void 0);const o=(t==null?void 0:t.direction)??"asc",a=d.useMemo(()=>{if(i===void 0)return;const u=new Array(n),c=[i,0];for(let g=0;g<n;g++)c[1]=g,u[g]=W1(r(c));let f;return(t==null?void 0:t.mode)==="raw"?f=lr(n).sort((g,h)=>q1(u[g],u[h])):(t==null?void 0:t.mode)==="smart"?f=lr(n).sort((g,h)=>U1(u[g],u[h])):f=lr(n).sort((g,h)=>u[g].localeCompare(u[h])),o==="desc"&&f.reverse(),f},[r,n,t==null?void 0:t.mode,o,i]),l=d.useCallback(u=>a===void 0?u:a[u],[a]),s=d.useCallback(([u,c])=>a===void 0?r([u,c]):(c=a[c],r([u,c])),[r,a]);return a===void 0?{getCellContent:e.getCellContent,getOriginalIndex:l}:{getOriginalIndex:l,getCellContent:s}}function X1(e,t){return t===void 0?e:e.map(n=>n.id===t.column.id?{...n,title:t.direction==="asc"?`↑ ${n.title}`:`↓ ${n.title}`}:n)}function j1(e,t,n){const[r,i]=d.useState(),{getCellContent:o,getOriginalIndex:a}=Y1({columns:t.map(u=>Ds(u)),getCellContent:n,rows:e,sort:r}),l=d.useMemo(()=>X1(t,r),[t,r]),s=d.useCallback((u,c,f)=>{const g=l[u];let h;c==="auto"?(h="asc",r&&r.column.id===g.id&&(r.direction==="asc"?h="desc":h=void 0)):h=c,h===void 0||f&&h===(r==null?void 0:r.direction)?i(void 0):i({column:Ds(g),direction:h,mode:g.sortMode})},[r,l]);return{columns:l,sortColumn:s,getOriginalIndex:a,getCellContent:o}}function G1(e,t){const n=d.useCallback(i=>{t(o=>ho({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!0}})),e(!0,!1)},[e,t]),r=d.useCallback(i=>{t(o=>ho({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!1}})),e(!0,!1)},[e,t]);return{hideColumn:n,showColumn:r}}function K1(){return{provideEditor:d.useCallback(t=>{if(t.kind===te.Text&&t.readonly&&f1(t.data))return{editor:w1}},[])}}const Z1={kind:te.Custom,isMatch:e=>e.data.kind==="sparkline-cell",needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{const{ctx:n,theme:r,rect:i,hoverAmount:o,hoverX:a}=e;let{values:l,yAxis:s,color:u,graphKind:c="area",displayValues:f,hideAxis:g}=t.data;const[h,m]=s;if(l.length===0)return!0;l=l.map(R=>Math.min(1,Math.max(0,(R-h)/(m-h))));const p=r.cellHorizontalPadding,v=p+i.x,w=i.y+3,b=i.height-6,M=i.width-p*2,O=m-h,S=m<=0?w:h>=0?w+b:w+b*(m/O);if(!g&&h<=0&&m>=0&&(n.beginPath(),n.moveTo(v,S),n.lineTo(v+M,S),n.globalAlpha=.4,n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.globalAlpha=1),c==="bar"){n.beginPath();const R=2,L=(l.length-1)*R,E=(M-L)/l.length;let x=v;for(const _ of l){const D=w+b-_*b;n.moveTo(x,S),n.lineTo(x+E,S),n.lineTo(x+E,D),n.lineTo(x,D),x+=E+R}n.fillStyle=t.data.color??r.accentColor,n.fill()}else{l.length===1&&(l=[l[0],l[0]],f&&(f=[f[0],f[0]])),n.beginPath();const R=(i.width-16)/(l.length-1),L=l.map((x,_)=>({x:v+R*_,y:w+b-x*b}));n.moveTo(L[0].x,L[0].y);let E=0;if(L.length>2)for(E=1;E<L.length-2;E++){const x=(L[E].x+L[E+1].x)/2,_=(L[E].y+L[E+1].y)/2;n.quadraticCurveTo(L[E].x,L[E].y,x,_)}if(n.quadraticCurveTo(L[E].x,L[E].y,L[E+1].x,L[E+1].y),n.strokeStyle=u??r.accentColor,n.lineWidth=1+o*.5,n.stroke(),n.lineTo(i.x+i.width-p,S),n.lineTo(i.x+p,S),n.closePath(),c==="area"){n.globalAlpha=.2+.2*o;const x=n.createLinearGradient(0,w,0,w+b*1.4);x.addColorStop(0,u??r.accentColor);const[_,D,C]=la(u??r.accentColor);x.addColorStop(1,`rgba(${_}, ${D}, ${C}, 0)`),n.fillStyle=x,n.fill(),n.globalAlpha=1}if(a!==void 0&&(c==="line"||c==="area")&&f!==void 0){n.beginPath();const x=Math.min(l.length-1,Math.max(0,Math.round((a-p)/R)));n.moveTo(v+x*R,i.y+1),n.lineTo(v+x*R,i.y+i.height),n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.save(),n.font=`8px ${r.fontFamily}`,n.fillStyle=r.textMedium,n.textBaseline="top",n.fillText(f[x],v,i.y+r.cellVerticalPadding),n.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t};function ec(e,t,n,r,i,o){if(!(r<=0||i<=0)){if(typeof o=="number"&&o<=0){e.rect(t,n,r,i);return}typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},o.tl=Math.max(0,o.tl),o.tr=Math.max(0,o.tr),o.br=Math.max(0,o.br),o.bl=Math.max(0,o.bl),e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}}function tc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Ye(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?tc(Object(n),!0).forEach(function(r){Ji(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tc(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var J1=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function Q1(e){var t=e.defaultInputValue,n=t===void 0?"":t,r=e.defaultMenuIsOpen,i=r===void 0?!1:r,o=e.defaultValue,a=o===void 0?null:o,l=e.inputValue,s=e.menuIsOpen,u=e.onChange,c=e.onInputChange,f=e.onMenuClose,g=e.onMenuOpen,h=e.value,m=yr(e,J1),p=d.useState(l!==void 0?l:n),v=pr(p,2),w=v[0],b=v[1],M=d.useState(s!==void 0?s:i),O=pr(M,2),S=O[0],R=O[1],L=d.useState(h!==void 0?h:a),E=pr(L,2),x=E[0],_=E[1],D=d.useCallback(function(X,re){typeof u=="function"&&u(X,re),_(X)},[u]),C=d.useCallback(function(X,re){var j;typeof c=="function"&&(j=c(X,re)),b(j!==void 0?j:X)},[c]),I=d.useCallback(function(){typeof g=="function"&&g(),R(!0)},[g]),T=d.useCallback(function(){typeof f=="function"&&f(),R(!1)},[f]),k=l!==void 0?l:w,z=s!==void 0?s:S,$=h!==void 0?h:x;return Ye(Ye({},m),{},{inputValue:k,menuIsOpen:z,onChange:D,onInputChange:C,onMenuClose:T,onMenuOpen:I,value:$})}function eb(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}const tb=Math.min,nb=Math.max,ma=Math.round,Go=Math.floor,pa=e=>({x:e,y:e});function rb(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function Ra(){return typeof window<"u"}function of(e){return sf(e)?(e.nodeName||"").toLowerCase():"#document"}function br(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function af(e){var t;return(t=(sf(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function sf(e){return Ra()?e instanceof Node||e instanceof br(e).Node:!1}function ib(e){return Ra()?e instanceof Element||e instanceof br(e).Element:!1}function ul(e){return Ra()?e instanceof HTMLElement||e instanceof br(e).HTMLElement:!1}function nc(e){return!Ra()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof br(e).ShadowRoot}function lf(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=cl(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function ob(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ab(e){return["html","body","#document"].includes(of(e))}function cl(e){return br(e).getComputedStyle(e)}function sb(e){if(of(e)==="html")return e;const t=e.assignedSlot||e.parentNode||nc(e)&&e.host||af(e);return nc(t)?t.host:t}function uf(e){const t=sb(e);return ab(t)?e.ownerDocument?e.ownerDocument.body:e.body:ul(t)&&lf(t)?t:uf(t)}function va(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=uf(e),o=i===((r=e.ownerDocument)==null?void 0:r.body),a=br(i);if(o){const l=Os(a);return t.concat(a,a.visualViewport||[],lf(i)?i:[],l&&n?va(l):[])}return t.concat(i,va(i,[],n))}function Os(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function lb(e){const t=cl(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=ul(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,l=ma(n)!==o||ma(r)!==a;return l&&(n=o,r=a),{width:n,height:r,$:l}}function dl(e){return ib(e)?e:e.contextElement}function rc(e){const t=dl(e);if(!ul(t))return pa(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=lb(t);let a=(o?ma(n.width):n.width)/r,l=(o?ma(n.height):n.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const ub=pa(0);function cb(e){const t=br(e);return!ob()||!t.visualViewport?ub:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function db(e,t,n){return!1}function ic(e,t,n,r){t===void 0&&(t=!1);const i=e.getBoundingClientRect(),o=dl(e);let a=pa(1);t&&(a=rc(e));const l=db()?cb(o):pa(0);let s=(i.left+l.x)/a.x,u=(i.top+l.y)/a.y,c=i.width/a.x,f=i.height/a.y;if(o){const g=br(o),h=r;let m=g,p=Os(m);for(;p&&r&&h!==m;){const v=rc(p),w=p.getBoundingClientRect(),b=cl(p),M=w.left+(p.clientLeft+parseFloat(b.paddingLeft))*v.x,O=w.top+(p.clientTop+parseFloat(b.paddingTop))*v.y;s*=v.x,u*=v.y,c*=v.x,f*=v.y,s+=M,u+=O,m=br(p),p=Os(m)}}return rb({width:c,height:f,x:s,y:u})}function fb(e,t){let n=null,r;const i=af(e);function o(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function a(l,s){l===void 0&&(l=!1),s===void 0&&(s=1),o();const{left:u,top:c,width:f,height:g}=e.getBoundingClientRect();if(l||t(),!f||!g)return;const h=Go(c),m=Go(i.clientWidth-(u+f)),p=Go(i.clientHeight-(c+g)),v=Go(u),b={rootMargin:-h+"px "+-m+"px "+-p+"px "+-v+"px",threshold:nb(0,tb(1,s))||1};let M=!0;function O(S){const R=S[0].intersectionRatio;if(R!==s){if(!M)return a();R?a(!1,R):r=setTimeout(()=>{a(!1,1e-7)},1e3)}M=!1}try{n=new IntersectionObserver(O,{...b,root:i.ownerDocument})}catch{n=new IntersectionObserver(O,b)}n.observe(e)}return a(!0),o}function hb(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:s=!1}=r,u=dl(e),c=i||o?[...u?va(u):[],...va(t)]:[];c.forEach(w=>{i&&w.addEventListener("scroll",n,{passive:!0}),o&&w.addEventListener("resize",n)});const f=u&&l?fb(u,n):null;let g=-1,h=null;a&&(h=new ResizeObserver(w=>{let[b]=w;b&&b.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var M;(M=h)==null||M.observe(t)})),n()}),u&&!s&&h.observe(u),h.observe(t));let m,p=s?ic(e):null;s&&v();function v(){const w=ic(e);p&&(w.x!==p.x||w.y!==p.y||w.width!==p.width||w.height!==p.height)&&n(),p=w,m=requestAnimationFrame(v)}return n(),()=>{var w;c.forEach(b=>{i&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),f==null||f(),(w=h)==null||w.disconnect(),h=null,s&&cancelAnimationFrame(m)}}var Ps=d.useLayoutEffect,gb=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],ba=function(){};function mb(e,t){return t?t[0]==="-"?e+t:e+"__"+t:e}function pb(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=[].concat(r);if(t&&e)for(var a in t)t.hasOwnProperty(a)&&t[a]&&o.push("".concat(mb(e,a)));return o.filter(function(l){return l}).map(function(l){return String(l).trim()}).join(" ")}var oc=function(t){return Mb(t)?t.filter(Boolean):eg(t)==="object"&&t!==null?[t]:[]},cf=function(t){t.className,t.clearValue,t.cx,t.getStyles,t.getClassNames,t.getValue,t.hasValue,t.isMulti,t.isRtl,t.options,t.selectOption,t.selectProps,t.setValue,t.theme;var n=yr(t,gb);return Ye({},n)},tn=function(t,n,r){var i=t.cx,o=t.getStyles,a=t.getClassNames,l=t.className;return{css:o(n,t),className:i(r??{},a(n,t),l)}};function Ea(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function vb(e){return Ea(e)?window.innerHeight:e.clientHeight}function df(e){return Ea(e)?window.pageYOffset:e.scrollTop}function wa(e,t){if(Ea(e)){window.scrollTo(0,t);return}e.scrollTop=t}function bb(e){var t=getComputedStyle(e),n=t.position==="absolute",r=/(auto|scroll)/;if(t.position==="fixed")return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),!(n&&t.position==="static")&&r.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}function wb(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function Ko(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:ba,i=df(e),o=t-i,a=10,l=0;function s(){l+=a;var u=wb(l,i,o,n);wa(e,u),l<n?window.requestAnimationFrame(s):r(e)}s()}function ac(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),i=t.offsetHeight/3;r.bottom+i>n.bottom?wa(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):r.top-i<n.top&&wa(e,Math.max(t.offsetTop-i,0))}function yb(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}function sc(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function Cb(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var ff=!1,Sb={get passive(){return ff=!0}},Zo=typeof window<"u"?window:{};Zo.addEventListener&&Zo.removeEventListener&&(Zo.addEventListener("p",ba,Sb),Zo.removeEventListener("p",ba,!1));var xb=ff;function kb(e){return e!=null}function Mb(e){return Array.isArray(e)}function Jo(e,t,n){return e?t:n}var Rb=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=Object.entries(t).filter(function(a){var l=pr(a,1),s=l[0];return!r.includes(s)});return o.reduce(function(a,l){var s=pr(l,2),u=s[0],c=s[1];return a[u]=c,a},{})},Eb=["children","innerProps"],Ib=["children","innerProps"];function Tb(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,i=e.placement,o=e.shouldScroll,a=e.isFixedPosition,l=e.controlHeight,s=bb(n),u={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return u;var c=s.getBoundingClientRect(),f=c.height,g=n.getBoundingClientRect(),h=g.bottom,m=g.height,p=g.top,v=n.offsetParent.getBoundingClientRect(),w=v.top,b=a?window.innerHeight:vb(s),M=df(s),O=parseInt(getComputedStyle(n).marginBottom,10),S=parseInt(getComputedStyle(n).marginTop,10),R=w-S,L=b-p,E=R+M,x=f-M-p,_=h-b+M+O,D=M+p-S,C=160;switch(i){case"auto":case"bottom":if(L>=m)return{placement:"bottom",maxHeight:t};if(x>=m&&!a)return o&&Ko(s,_,C),{placement:"bottom",maxHeight:t};if(!a&&x>=r||a&&L>=r){o&&Ko(s,_,C);var I=a?L-O:x-O;return{placement:"bottom",maxHeight:I}}if(i==="auto"||a){var T=t,k=a?R:E;return k>=r&&(T=Math.min(k-O-l,t)),{placement:"top",maxHeight:T}}if(i==="bottom")return o&&wa(s,_),{placement:"bottom",maxHeight:t};break;case"top":if(R>=m)return{placement:"top",maxHeight:t};if(E>=m&&!a)return o&&Ko(s,D,C),{placement:"top",maxHeight:t};if(!a&&E>=r||a&&R>=r){var z=t;return(!a&&E>=r||a&&R>=r)&&(z=a?R-S:E-S),o&&Ko(s,D,C),{placement:"top",maxHeight:z}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return u}function Db(e){var t={bottom:"top",top:"bottom"};return e?t[e]:"bottom"}var hf=function(t){return t==="auto"?"bottom":t},Ob=function(t,n){var r,i=t.placement,o=t.theme,a=o.borderRadius,l=o.spacing,s=o.colors;return Ye((r={label:"menu"},Ji(r,Db(i),"100%"),Ji(r,"position","absolute"),Ji(r,"width","100%"),Ji(r,"zIndex",1),r),n?{}:{backgroundColor:s.neutral0,borderRadius:a,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:l.menuGutter,marginTop:l.menuGutter})},gf=d.createContext(null),Pb=function(t){var n=t.children,r=t.minMenuHeight,i=t.maxMenuHeight,o=t.menuPlacement,a=t.menuPosition,l=t.menuShouldScrollIntoView,s=t.theme,u=d.useContext(gf)||{},c=u.setPortalPlacement,f=d.useRef(null),g=d.useState(i),h=pr(g,2),m=h[0],p=h[1],v=d.useState(null),w=pr(v,2),b=w[0],M=w[1],O=s.spacing.controlHeight;return Ps(function(){var S=f.current;if(S){var R=a==="fixed",L=l&&!R,E=Tb({maxHeight:i,menuEl:S,minHeight:r,placement:o,shouldScroll:L,isFixedPosition:R,controlHeight:O});p(E.maxHeight),M(E.placement),c==null||c(E.placement)}},[i,o,a,l,r,c,O]),n({ref:f,placerProps:Ye(Ye({},t),{},{placement:b||hf(o),maxHeight:m})})},Lb=function(t){var n=t.children,r=t.innerRef,i=t.innerProps;return Ne("div",Ke({},tn(t,"menu",{menu:!0}),{ref:r},i),n)},_b=Lb,Fb=function(t,n){var r=t.maxHeight,i=t.theme.spacing.baseUnit;return Ye({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},n?{}:{paddingBottom:i,paddingTop:i})},Ab=function(t){var n=t.children,r=t.innerProps,i=t.innerRef,o=t.isMulti;return Ne("div",Ke({},tn(t,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:i},r),n)},mf=function(t,n){var r=t.theme,i=r.spacing.baseUnit,o=r.colors;return Ye({textAlign:"center"},n?{}:{color:o.neutral40,padding:"".concat(i*2,"px ").concat(i*3,"px")})},Hb=mf,zb=mf,Vb=function(t){var n=t.children,r=n===void 0?"No options":n,i=t.innerProps,o=yr(t,Eb);return Ne("div",Ke({},tn(Ye(Ye({},o),{},{children:r,innerProps:i}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),i),r)},Nb=function(t){var n=t.children,r=n===void 0?"Loading...":n,i=t.innerProps,o=yr(t,Ib);return Ne("div",Ke({},tn(Ye(Ye({},o),{},{children:r,innerProps:i}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),i),r)},$b=function(t){var n=t.rect,r=t.offset,i=t.position;return{left:n.left,position:i,top:r,width:n.width,zIndex:1}},Bb=function(t){var n=t.appendTo,r=t.children,i=t.controlElement,o=t.innerProps,a=t.menuPlacement,l=t.menuPosition,s=d.useRef(null),u=d.useRef(null),c=d.useState(hf(a)),f=pr(c,2),g=f[0],h=f[1],m=d.useMemo(function(){return{setPortalPlacement:h}},[]),p=d.useState(null),v=pr(p,2),w=v[0],b=v[1],M=d.useCallback(function(){if(i){var L=yb(i),E=l==="fixed"?0:window.pageYOffset,x=L[g]+E;(x!==(w==null?void 0:w.offset)||L.left!==(w==null?void 0:w.rect.left)||L.width!==(w==null?void 0:w.rect.width))&&b({offset:x,rect:L})}},[i,l,g,w==null?void 0:w.offset,w==null?void 0:w.rect.left,w==null?void 0:w.rect.width]);Ps(function(){M()},[M]);var O=d.useCallback(function(){typeof u.current=="function"&&(u.current(),u.current=null),i&&s.current&&(u.current=hb(i,s.current,M,{elementResize:"ResizeObserver"in window}))},[i,M]);Ps(function(){O()},[O]);var S=d.useCallback(function(L){s.current=L,O()},[O]);if(!n&&l!=="fixed"||!w)return null;var R=Ne("div",Ke({ref:S},tn(Ye(Ye({},t),{},{offset:w.offset,position:l,rect:w.rect}),"menuPortal",{"menu-portal":!0}),o),r);return Ne(gf.Provider,{value:m},n?Nc.createPortal(R,n):R)},Wb=function(t){var n=t.isDisabled,r=t.isRtl;return{label:"container",direction:r?"rtl":void 0,pointerEvents:n?"none":void 0,position:"relative"}},Ub=function(t){var n=t.children,r=t.innerProps,i=t.isDisabled,o=t.isRtl;return Ne("div",Ke({},tn(t,"container",{"--is-disabled":i,"--is-rtl":o}),r),n)},qb=function(t,n){var r=t.theme.spacing,i=t.isMulti,o=t.hasValue,a=t.selectProps.controlShouldRenderValue;return Ye({alignItems:"center",display:i&&o&&a?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},n?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(r.baseUnit*2,"px")})},Yb=function(t){var n=t.children,r=t.innerProps,i=t.isMulti,o=t.hasValue;return Ne("div",Ke({},tn(t,"valueContainer",{"value-container":!0,"value-container--is-multi":i,"value-container--has-value":o}),r),n)},Xb=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},jb=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ke({},tn(t,"indicatorsContainer",{indicators:!0}),r),n)},lc,Gb=["size"],Kb=["innerProps","isRtl","size"],Zb={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},pf=function(t){var n=t.size,r=yr(t,Gb);return Ne("svg",Ke({height:n,width:n,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:Zb},r))},fl=function(t){return Ne(pf,Ke({size:20},t),Ne("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},vf=function(t){return Ne(pf,Ke({size:20},t),Ne("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},bf=function(t,n){var r=t.isFocused,i=t.theme,o=i.spacing.baseUnit,a=i.colors;return Ye({label:"indicatorContainer",display:"flex",transition:"color 150ms"},n?{}:{color:r?a.neutral60:a.neutral20,padding:o*2,":hover":{color:r?a.neutral80:a.neutral40}})},Jb=bf,Qb=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ke({},tn(t,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),n||Ne(vf,null))},ew=bf,tw=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ke({},tn(t,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),n||Ne(fl,null))},nw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing.baseUnit,a=i.colors;return Ye({label:"indicatorSeparator",alignSelf:"stretch",width:1},n?{}:{backgroundColor:r?a.neutral10:a.neutral20,marginBottom:o*2,marginTop:o*2})},rw=function(t){var n=t.innerProps;return Ne("span",Ke({},n,tn(t,"indicatorSeparator",{"indicator-separator":!0})))},iw=tg(lc||(lc=eb([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),ow=function(t,n){var r=t.isFocused,i=t.size,o=t.theme,a=o.colors,l=o.spacing.baseUnit;return Ye({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:i,lineHeight:1,marginRight:i,textAlign:"center",verticalAlign:"middle"},n?{}:{color:r?a.neutral60:a.neutral20,padding:l*2})},ms=function(t){var n=t.delay,r=t.offset;return Ne("span",{css:$c({animation:"".concat(iw," 1s ease-in-out ").concat(n,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},aw=function(t){var n=t.innerProps,r=t.isRtl,i=t.size,o=i===void 0?4:i,a=yr(t,Kb);return Ne("div",Ke({},tn(Ye(Ye({},a),{},{innerProps:n,isRtl:r,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),n),Ne(ms,{delay:0,offset:r}),Ne(ms,{delay:160,offset:!0}),Ne(ms,{delay:320,offset:!r}))},sw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.theme,a=o.colors,l=o.borderRadius,s=o.spacing;return Ye({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:s.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},n?{}:{backgroundColor:r?a.neutral5:a.neutral0,borderColor:r?a.neutral10:i?a.primary:a.neutral20,borderRadius:l,borderStyle:"solid",borderWidth:1,boxShadow:i?"0 0 0 1px ".concat(a.primary):void 0,"&:hover":{borderColor:i?a.primary:a.neutral30}})},lw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.innerRef,a=t.innerProps,l=t.menuIsOpen;return Ne("div",Ke({ref:o},tn(t,"control",{control:!0,"control--is-disabled":r,"control--is-focused":i,"control--menu-is-open":l}),a,{"aria-disabled":r||void 0}),n)},uw=lw,cw=["data"],dw=function(t,n){var r=t.theme.spacing;return n?{}:{paddingBottom:r.baseUnit*2,paddingTop:r.baseUnit*2}},fw=function(t){var n=t.children,r=t.cx,i=t.getStyles,o=t.getClassNames,a=t.Heading,l=t.headingProps,s=t.innerProps,u=t.label,c=t.theme,f=t.selectProps;return Ne("div",Ke({},tn(t,"group",{group:!0}),s),Ne(a,Ke({},l,{selectProps:f,theme:c,getStyles:i,getClassNames:o,cx:r}),u),Ne("div",null,n))},hw=function(t,n){var r=t.theme,i=r.colors,o=r.spacing;return Ye({label:"group",cursor:"default",display:"block"},n?{}:{color:i.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:o.baseUnit*3,paddingRight:o.baseUnit*3,textTransform:"uppercase"})},gw=function(t){var n=cf(t);n.data;var r=yr(n,cw);return Ne("div",Ke({},tn(t,"groupHeading",{"group-heading":!0}),r))},mw=fw,pw=["innerRef","isDisabled","isHidden","inputClassName"],vw=function(t,n){var r=t.isDisabled,i=t.value,o=t.theme,a=o.spacing,l=o.colors;return Ye(Ye({visibility:r?"hidden":"visible",transform:i?"translateZ(0)":""},bw),n?{}:{margin:a.baseUnit/2,paddingBottom:a.baseUnit/2,paddingTop:a.baseUnit/2,color:l.neutral80})},wf={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},bw={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Ye({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},wf)},ww=function(t){return Ye({label:"input",color:"inherit",background:0,opacity:t?0:1,width:"100%"},wf)},yw=function(t){var n=t.cx,r=t.value,i=cf(t),o=i.innerRef,a=i.isDisabled,l=i.isHidden,s=i.inputClassName,u=yr(i,pw);return Ne("div",Ke({},tn(t,"input",{"input-container":!0}),{"data-value":r||""}),Ne("input",Ke({className:n({input:!0},s),ref:o,style:ww(l),disabled:a},u)))},Cw=yw,Sw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,a=r.colors;return Ye({label:"multiValue",display:"flex",minWidth:0},n?{}:{backgroundColor:a.neutral10,borderRadius:o/2,margin:i.baseUnit/2})},xw=function(t,n){var r=t.theme,i=r.borderRadius,o=r.colors,a=t.cropWithEllipsis;return Ye({overflow:"hidden",textOverflow:a||a===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},n?{}:{borderRadius:i/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},kw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,a=r.colors,l=t.isFocused;return Ye({alignItems:"center",display:"flex"},n?{}:{borderRadius:o/2,backgroundColor:l?a.dangerLight:void 0,paddingLeft:i.baseUnit,paddingRight:i.baseUnit,":hover":{backgroundColor:a.dangerLight,color:a.danger}})},yf=function(t){var n=t.children,r=t.innerProps;return Ne("div",r,n)},Mw=yf,Rw=yf;function Ew(e){var t=e.children,n=e.innerProps;return Ne("div",Ke({role:"button"},n),t||Ne(fl,{size:14}))}var Iw=function(t){var n=t.children,r=t.components,i=t.data,o=t.innerProps,a=t.isDisabled,l=t.removeProps,s=t.selectProps,u=r.Container,c=r.Label,f=r.Remove;return Ne(u,{data:i,innerProps:Ye(Ye({},tn(t,"multiValue",{"multi-value":!0,"multi-value--is-disabled":a})),o),selectProps:s},Ne(c,{data:i,innerProps:Ye({},tn(t,"multiValueLabel",{"multi-value__label":!0})),selectProps:s},n),Ne(f,{data:i,innerProps:Ye(Ye({},tn(t,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(n||"option")},l),selectProps:s}))},Tw=Iw,Dw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.isSelected,a=t.theme,l=a.spacing,s=a.colors;return Ye({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},n?{}:{backgroundColor:o?s.primary:i?s.primary25:"transparent",color:r?s.neutral20:o?s.neutral0:"inherit",padding:"".concat(l.baseUnit*2,"px ").concat(l.baseUnit*3,"px"),":active":{backgroundColor:r?void 0:o?s.primary:s.primary50}})},Ow=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.isSelected,a=t.innerRef,l=t.innerProps;return Ne("div",Ke({},tn(t,"option",{option:!0,"option--is-disabled":r,"option--is-focused":i,"option--is-selected":o}),{ref:a,"aria-disabled":r},l),n)},Pw=Ow,Lw=function(t,n){var r=t.theme,i=r.spacing,o=r.colors;return Ye({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},n?{}:{color:o.neutral50,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},_w=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ke({},tn(t,"placeholder",{placeholder:!0}),r),n)},Fw=_w,Aw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing,a=i.colors;return Ye({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n?{}:{color:r?a.neutral40:a.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},Hw=function(t){var n=t.children,r=t.isDisabled,i=t.innerProps;return Ne("div",Ke({},tn(t,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),i),n)},zw=Hw,Cf={ClearIndicator:tw,Control:uw,DropdownIndicator:Qb,DownChevron:vf,CrossIcon:fl,Group:mw,GroupHeading:gw,IndicatorsContainer:jb,IndicatorSeparator:rw,Input:Cw,LoadingIndicator:aw,Menu:_b,MenuList:Ab,MenuPortal:Bb,LoadingMessage:Nb,NoOptionsMessage:Vb,MultiValue:Tw,MultiValueContainer:Mw,MultiValueLabel:Rw,MultiValueRemove:Ew,Option:Pw,Placeholder:Fw,SelectContainer:Ub,SingleValue:zw,ValueContainer:Yb},Vw=function(t){return Ye(Ye({},Cf),t.components)},uc=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function Nw(e,t){return!!(e===t||uc(e)&&uc(t))}function $w(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!Nw(e[n],t[n]))return!1;return!0}function Bw(e,t){t===void 0&&(t=$w);var n=null;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;var a=e.apply(this,i);return n={lastResult:a,lastArgs:i,lastThis:this},a}return r.clear=function(){n=null},r}var Ww={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},Uw=function(t){return Ne("span",Ke({css:Ww},t))},cc=Uw,qw={guidance:function(t){var n=t.isSearchable,r=t.isMulti,i=t.tabSelectsValue,o=t.context,a=t.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(i?", press Tab to select the option and exit the menu":"",".");case"input":return a?"".concat(t["aria-label"]||"Select"," is focused ").concat(n?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(t){var n=t.action,r=t.label,i=r===void 0?"":r,o=t.labels,a=t.isDisabled;switch(n){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(i,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return a?"option ".concat(i," is disabled. Select another option."):"option ".concat(i,", selected.");default:return""}},onFocus:function(t){var n=t.context,r=t.focused,i=t.options,o=t.label,a=o===void 0?"":o,l=t.selectValue,s=t.isDisabled,u=t.isSelected,c=t.isAppleDevice,f=function(p,v){return p&&p.length?"".concat(p.indexOf(v)+1," of ").concat(p.length):""};if(n==="value"&&l)return"value ".concat(a," focused, ").concat(f(l,r),".");if(n==="menu"&&c){var g=s?" disabled":"",h="".concat(u?" selected":"").concat(g);return"".concat(a).concat(h,", ").concat(f(i,r),".")}return""},onFilter:function(t){var n=t.inputValue,r=t.resultsMessage;return"".concat(r).concat(n?" for search term "+n:"",".")}},Yw=function(t){var n=t.ariaSelection,r=t.focusedOption,i=t.focusedValue,o=t.focusableOptions,a=t.isFocused,l=t.selectValue,s=t.selectProps,u=t.id,c=t.isAppleDevice,f=s.ariaLiveMessages,g=s.getOptionLabel,h=s.inputValue,m=s.isMulti,p=s.isOptionDisabled,v=s.isSearchable,w=s.menuIsOpen,b=s.options,M=s.screenReaderStatus,O=s.tabSelectsValue,S=s.isLoading,R=s["aria-label"],L=s["aria-live"],E=d.useMemo(function(){return Ye(Ye({},qw),f||{})},[f]),x=d.useMemo(function(){var k="";if(n&&E.onChange){var z=n.option,$=n.options,X=n.removedValue,re=n.removedValues,j=n.value,G=function(P){return Array.isArray(P)?null:P},ae=X||z||G(j),ie=ae?g(ae):"",le=$||re||void 0,fe=le?le.map(g):[],Q=Ye({isDisabled:ae&&p(ae,l),label:ie,labels:fe},n);k=E.onChange(Q)}return k},[n,E,p,l,g]),_=d.useMemo(function(){var k="",z=r||i,$=!!(r&&l&&l.includes(r));if(z&&E.onFocus){var X={focused:z,label:g(z),isDisabled:p(z,l),isSelected:$,options:o,context:z===r?"menu":"value",selectValue:l,isAppleDevice:c};k=E.onFocus(X)}return k},[r,i,g,p,E,o,l,c]),D=d.useMemo(function(){var k="";if(w&&b.length&&!S&&E.onFilter){var z=M({count:o.length});k=E.onFilter({inputValue:h,resultsMessage:z})}return k},[o,h,w,E,b,M,S]),C=(n==null?void 0:n.action)==="initial-input-focus",I=d.useMemo(function(){var k="";if(E.guidance){var z=i?"value":w?"menu":"input";k=E.guidance({"aria-label":R,context:z,isDisabled:r&&p(r,l),isMulti:m,isSearchable:v,tabSelectsValue:O,isInitialFocus:C})}return k},[R,r,i,m,p,v,w,E,l,O,C]),T=Ne(d.Fragment,null,Ne("span",{id:"aria-selection"},x),Ne("span",{id:"aria-focused"},_),Ne("span",{id:"aria-results"},D),Ne("span",{id:"aria-guidance"},I));return Ne(d.Fragment,null,Ne(cc,{id:u},C&&T),Ne(cc,{"aria-live":L,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},a&&!C&&T))},Xw=Yw,Ls=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],jw=new RegExp("["+Ls.map(function(e){return e.letters}).join("")+"]","g"),Sf={};for(var ps=0;ps<Ls.length;ps++)for(var vs=Ls[ps],bs=0;bs<vs.letters.length;bs++)Sf[vs.letters[bs]]=vs.base;var xf=function(t){return t.replace(jw,function(n){return Sf[n]})},Gw=Bw(xf),dc=function(t){return t.replace(/^\s+|\s+$/g,"")},Kw=function(t){return"".concat(t.label," ").concat(t.value)},Zw=function(t){return function(n,r){if(n.data.__isNew__)return!0;var i=Ye({ignoreCase:!0,ignoreAccents:!0,stringify:Kw,trim:!0,matchFrom:"any"},t),o=i.ignoreCase,a=i.ignoreAccents,l=i.stringify,s=i.trim,u=i.matchFrom,c=s?dc(r):r,f=s?dc(l(n)):l(n);return o&&(c=c.toLowerCase(),f=f.toLowerCase()),a&&(c=Gw(c),f=xf(f)),u==="start"?f.substr(0,c.length)===c:f.indexOf(c)>-1}},Jw=["innerRef"];function Qw(e){var t=e.innerRef,n=yr(e,Jw),r=Rb(n,"onExited","in","enter","exit","appear");return Ne("input",Ke({ref:t},r,{css:$c({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var ey=function(t){t.cancelable&&t.preventDefault(),t.stopPropagation()};function ty(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,i=e.onTopArrive,o=e.onTopLeave,a=d.useRef(!1),l=d.useRef(!1),s=d.useRef(0),u=d.useRef(null),c=d.useCallback(function(v,w){if(u.current!==null){var b=u.current,M=b.scrollTop,O=b.scrollHeight,S=b.clientHeight,R=u.current,L=w>0,E=O-S-M,x=!1;E>w&&a.current&&(r&&r(v),a.current=!1),L&&l.current&&(o&&o(v),l.current=!1),L&&w>E?(n&&!a.current&&n(v),R.scrollTop=O,x=!0,a.current=!0):!L&&-w>M&&(i&&!l.current&&i(v),R.scrollTop=0,x=!0,l.current=!0),x&&ey(v)}},[n,r,i,o]),f=d.useCallback(function(v){c(v,v.deltaY)},[c]),g=d.useCallback(function(v){s.current=v.changedTouches[0].clientY},[]),h=d.useCallback(function(v){var w=s.current-v.changedTouches[0].clientY;c(v,w)},[c]),m=d.useCallback(function(v){if(v){var w=xb?{passive:!1}:!1;v.addEventListener("wheel",f,w),v.addEventListener("touchstart",g,w),v.addEventListener("touchmove",h,w)}},[h,g,f]),p=d.useCallback(function(v){v&&(v.removeEventListener("wheel",f,!1),v.removeEventListener("touchstart",g,!1),v.removeEventListener("touchmove",h,!1))},[h,g,f]);return d.useEffect(function(){if(t){var v=u.current;return m(v),function(){p(v)}}},[t,m,p]),function(v){u.current=v}}var fc=["boxSizing","height","overflow","paddingRight","position"],hc={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function gc(e){e.preventDefault()}function mc(e){e.stopPropagation()}function pc(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;e===0?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function vc(){return"ontouchstart"in window||navigator.maxTouchPoints}var bc=!!(typeof window<"u"&&window.document&&window.document.createElement),Ki=0,hi={capture:!1,passive:!1};function ny(e){var t=e.isEnabled,n=e.accountForScrollbars,r=n===void 0?!0:n,i=d.useRef({}),o=d.useRef(null),a=d.useCallback(function(s){if(bc){var u=document.body,c=u&&u.style;if(r&&fc.forEach(function(m){var p=c&&c[m];i.current[m]=p}),r&&Ki<1){var f=parseInt(i.current.paddingRight,10)||0,g=document.body?document.body.clientWidth:0,h=window.innerWidth-g+f||0;Object.keys(hc).forEach(function(m){var p=hc[m];c&&(c[m]=p)}),c&&(c.paddingRight="".concat(h,"px"))}u&&vc()&&(u.addEventListener("touchmove",gc,hi),s&&(s.addEventListener("touchstart",pc,hi),s.addEventListener("touchmove",mc,hi))),Ki+=1}},[r]),l=d.useCallback(function(s){if(bc){var u=document.body,c=u&&u.style;Ki=Math.max(Ki-1,0),r&&Ki<1&&fc.forEach(function(f){var g=i.current[f];c&&(c[f]=g)}),u&&vc()&&(u.removeEventListener("touchmove",gc,hi),s&&(s.removeEventListener("touchstart",pc,hi),s.removeEventListener("touchmove",mc,hi)))}},[r]);return d.useEffect(function(){if(t){var s=o.current;return a(s),function(){l(s)}}},[t,a,l]),function(s){o.current=s}}var ry=function(t){var n=t.target;return n.ownerDocument.activeElement&&n.ownerDocument.activeElement.blur()},iy={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function oy(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,i=r===void 0?!0:r,o=e.onBottomArrive,a=e.onBottomLeave,l=e.onTopArrive,s=e.onTopLeave,u=ty({isEnabled:i,onBottomArrive:o,onBottomLeave:a,onTopArrive:l,onTopLeave:s}),c=ny({isEnabled:n}),f=function(h){u(h),c(h)};return Ne(d.Fragment,null,n&&Ne("div",{onClick:ry,css:iy}),t(f))}var ay={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},sy=function(t){var n=t.name,r=t.onFocus;return Ne("input",{required:!0,name:n,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:ay,value:"",onChange:function(){}})},ly=sy;function hl(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function uy(){return hl(/^iPhone/i)}function kf(){return hl(/^Mac/i)}function cy(){return hl(/^iPad/i)||kf()&&navigator.maxTouchPoints>1}function dy(){return uy()||cy()}function fy(){return kf()||dy()}var hy=function(t){return t.label},gy=function(t){return t.label},my=function(t){return t.value},py=function(t){return!!t.isDisabled},vy={clearIndicator:ew,container:Wb,control:sw,dropdownIndicator:Jb,group:dw,groupHeading:hw,indicatorsContainer:Xb,indicatorSeparator:nw,input:vw,loadingIndicator:ow,loadingMessage:zb,menu:Ob,menuList:Fb,menuPortal:$b,multiValue:Sw,multiValueLabel:xw,multiValueRemove:kw,noOptionsMessage:Hb,option:Dw,placeholder:Lw,singleValue:Aw,valueContainer:qb},by={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},wy=4,Mf=4,yy=38,Cy=Mf*2,Sy={baseUnit:Mf,controlHeight:yy,menuGutter:Cy},ws={borderRadius:wy,colors:by,spacing:Sy},xy={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:sc(),captureMenuScroll:!sc(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:Zw(),formatGroupLabel:hy,getOptionLabel:gy,getOptionValue:my,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:py,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!Cb(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(t){var n=t.count;return"".concat(n," result").concat(n!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function wc(e,t,n,r){var i=If(e,t,n),o=Tf(e,t,n),a=Ef(e,t),l=ya(e,t);return{type:"option",data:t,isDisabled:i,isSelected:o,label:a,value:l,index:r}}function ra(e,t){return e.options.map(function(n,r){if("options"in n){var i=n.options.map(function(a,l){return wc(e,a,t,l)}).filter(function(a){return Cc(e,a)});return i.length>0?{type:"group",data:n,options:i,index:r}:void 0}var o=wc(e,n,t,r);return Cc(e,o)?o:void 0}).filter(kb)}function Rf(e){return e.reduce(function(t,n){return n.type==="group"?t.push.apply(t,Hs(n.options.map(function(r){return r.data}))):t.push(n.data),t},[])}function yc(e,t){return e.reduce(function(n,r){return r.type==="group"?n.push.apply(n,Hs(r.options.map(function(i){return{data:i.data,id:"".concat(t,"-").concat(r.index,"-").concat(i.index)}}))):n.push({data:r.data,id:"".concat(t,"-").concat(r.index)}),n},[])}function ky(e,t){return Rf(ra(e,t))}function Cc(e,t){var n=e.inputValue,r=n===void 0?"":n,i=t.data,o=t.isSelected,a=t.label,l=t.value;return(!Of(e)||!o)&&Df(e,{label:a,value:l,data:i},r)}function My(e,t){var n=e.focusedValue,r=e.selectValue,i=r.indexOf(n);if(i>-1){var o=t.indexOf(n);if(o>-1)return n;if(i<t.length)return t[i]}return null}function Ry(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}var ys=function(t,n){var r,i=(r=t.find(function(o){return o.data===n}))===null||r===void 0?void 0:r.id;return i||null},Ef=function(t,n){return t.getOptionLabel(n)},ya=function(t,n){return t.getOptionValue(n)};function If(e,t,n){return typeof e.isOptionDisabled=="function"?e.isOptionDisabled(t,n):!1}function Tf(e,t,n){if(n.indexOf(t)>-1)return!0;if(typeof e.isOptionSelected=="function")return e.isOptionSelected(t,n);var r=ya(e,t);return n.some(function(i){return ya(e,i)===r})}function Df(e,t,n){return e.filterOption?e.filterOption(t,n):!0}var Of=function(t){var n=t.hideSelectedOptions,r=t.isMulti;return n===void 0?r:n},Ey=1,Pf=function(e){yg(n,e);var t=xg(n);function n(r){var i;if(Sg(this,n),i=t.call(this,r),i.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},i.blockOptionHover=!1,i.isComposing=!1,i.commonProps=void 0,i.initialTouchX=0,i.initialTouchY=0,i.openAfterFocus=!1,i.scrollToFocusedOptionOnUpdate=!1,i.userIsDragging=void 0,i.isAppleDevice=fy(),i.controlRef=null,i.getControlRef=function(s){i.controlRef=s},i.focusedOptionRef=null,i.getFocusedOptionRef=function(s){i.focusedOptionRef=s},i.menuListRef=null,i.getMenuListRef=function(s){i.menuListRef=s},i.inputRef=null,i.getInputRef=function(s){i.inputRef=s},i.focus=i.focusInput,i.blur=i.blurInput,i.onChange=function(s,u){var c=i.props,f=c.onChange,g=c.name;u.name=g,i.ariaOnChange(s,u),f(s,u)},i.setValue=function(s,u,c){var f=i.props,g=f.closeMenuOnSelect,h=f.isMulti,m=f.inputValue;i.onInputChange("",{action:"set-value",prevInputValue:m}),g&&(i.setState({inputIsHiddenAfterUpdate:!h}),i.onMenuClose()),i.setState({clearFocusValueOnUpdate:!0}),i.onChange(s,{action:u,option:c})},i.selectOption=function(s){var u=i.props,c=u.blurInputOnSelect,f=u.isMulti,g=u.name,h=i.state.selectValue,m=f&&i.isOptionSelected(s,h),p=i.isOptionDisabled(s,h);if(m){var v=i.getOptionValue(s);i.setValue(h.filter(function(w){return i.getOptionValue(w)!==v}),"deselect-option",s)}else if(!p)f?i.setValue([].concat(Hs(h),[s]),"select-option",s):i.setValue(s,"select-option");else{i.ariaOnChange(s,{action:"select-option",option:s,name:g});return}c&&i.blurInput()},i.removeValue=function(s){var u=i.props.isMulti,c=i.state.selectValue,f=i.getOptionValue(s),g=c.filter(function(m){return i.getOptionValue(m)!==f}),h=Jo(u,g,g[0]||null);i.onChange(h,{action:"remove-value",removedValue:s}),i.focusInput()},i.clearValue=function(){var s=i.state.selectValue;i.onChange(Jo(i.props.isMulti,[],null),{action:"clear",removedValues:s})},i.popValue=function(){var s=i.props.isMulti,u=i.state.selectValue,c=u[u.length-1],f=u.slice(0,u.length-1),g=Jo(s,f,f[0]||null);c&&i.onChange(g,{action:"pop-value",removedValue:c})},i.getFocusedOptionId=function(s){return ys(i.state.focusableOptionsWithIds,s)},i.getFocusableOptionsWithIds=function(){return yc(ra(i.props,i.state.selectValue),i.getElementId("option"))},i.getValue=function(){return i.state.selectValue},i.cx=function(){for(var s=arguments.length,u=new Array(s),c=0;c<s;c++)u[c]=arguments[c];return pb.apply(void 0,[i.props.classNamePrefix].concat(u))},i.getOptionLabel=function(s){return Ef(i.props,s)},i.getOptionValue=function(s){return ya(i.props,s)},i.getStyles=function(s,u){var c=i.props.unstyled,f=vy[s](u,c);f.boxSizing="border-box";var g=i.props.styles[s];return g?g(f,u):f},i.getClassNames=function(s,u){var c,f;return(c=(f=i.props.classNames)[s])===null||c===void 0?void 0:c.call(f,u)},i.getElementId=function(s){return"".concat(i.state.instancePrefix,"-").concat(s)},i.getComponents=function(){return Vw(i.props)},i.buildCategorizedOptions=function(){return ra(i.props,i.state.selectValue)},i.getCategorizedOptions=function(){return i.props.menuIsOpen?i.buildCategorizedOptions():[]},i.buildFocusableOptions=function(){return Rf(i.buildCategorizedOptions())},i.getFocusableOptions=function(){return i.props.menuIsOpen?i.buildFocusableOptions():[]},i.ariaOnChange=function(s,u){i.setState({ariaSelection:Ye({value:s},u)})},i.onMenuMouseDown=function(s){s.button===0&&(s.stopPropagation(),s.preventDefault(),i.focusInput())},i.onMenuMouseMove=function(s){i.blockOptionHover=!1},i.onControlMouseDown=function(s){if(!s.defaultPrevented){var u=i.props.openMenuOnClick;i.state.isFocused?i.props.menuIsOpen?s.target.tagName!=="INPUT"&&s.target.tagName!=="TEXTAREA"&&i.onMenuClose():u&&i.openMenu("first"):(u&&(i.openAfterFocus=!0),i.focusInput()),s.target.tagName!=="INPUT"&&s.target.tagName!=="TEXTAREA"&&s.preventDefault()}},i.onDropdownIndicatorMouseDown=function(s){if(!(s&&s.type==="mousedown"&&s.button!==0)&&!i.props.isDisabled){var u=i.props,c=u.isMulti,f=u.menuIsOpen;i.focusInput(),f?(i.setState({inputIsHiddenAfterUpdate:!c}),i.onMenuClose()):i.openMenu("first"),s.preventDefault()}},i.onClearIndicatorMouseDown=function(s){s&&s.type==="mousedown"&&s.button!==0||(i.clearValue(),s.preventDefault(),i.openAfterFocus=!1,s.type==="touchend"?i.focusInput():setTimeout(function(){return i.focusInput()}))},i.onScroll=function(s){typeof i.props.closeMenuOnScroll=="boolean"?s.target instanceof HTMLElement&&Ea(s.target)&&i.props.onMenuClose():typeof i.props.closeMenuOnScroll=="function"&&i.props.closeMenuOnScroll(s)&&i.props.onMenuClose()},i.onCompositionStart=function(){i.isComposing=!0},i.onCompositionEnd=function(){i.isComposing=!1},i.onTouchStart=function(s){var u=s.touches,c=u&&u.item(0);c&&(i.initialTouchX=c.clientX,i.initialTouchY=c.clientY,i.userIsDragging=!1)},i.onTouchMove=function(s){var u=s.touches,c=u&&u.item(0);if(c){var f=Math.abs(c.clientX-i.initialTouchX),g=Math.abs(c.clientY-i.initialTouchY),h=5;i.userIsDragging=f>h||g>h}},i.onTouchEnd=function(s){i.userIsDragging||(i.controlRef&&!i.controlRef.contains(s.target)&&i.menuListRef&&!i.menuListRef.contains(s.target)&&i.blurInput(),i.initialTouchX=0,i.initialTouchY=0)},i.onControlTouchEnd=function(s){i.userIsDragging||i.onControlMouseDown(s)},i.onClearIndicatorTouchEnd=function(s){i.userIsDragging||i.onClearIndicatorMouseDown(s)},i.onDropdownIndicatorTouchEnd=function(s){i.userIsDragging||i.onDropdownIndicatorMouseDown(s)},i.handleInputChange=function(s){var u=i.props.inputValue,c=s.currentTarget.value;i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange(c,{action:"input-change",prevInputValue:u}),i.props.menuIsOpen||i.onMenuOpen()},i.onInputFocus=function(s){i.props.onFocus&&i.props.onFocus(s),i.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(i.openAfterFocus||i.props.openMenuOnFocus)&&i.openMenu("first"),i.openAfterFocus=!1},i.onInputBlur=function(s){var u=i.props.inputValue;if(i.menuListRef&&i.menuListRef.contains(document.activeElement)){i.inputRef.focus();return}i.props.onBlur&&i.props.onBlur(s),i.onInputChange("",{action:"input-blur",prevInputValue:u}),i.onMenuClose(),i.setState({focusedValue:null,isFocused:!1})},i.onOptionHover=function(s){if(!(i.blockOptionHover||i.state.focusedOption===s)){var u=i.getFocusableOptions(),c=u.indexOf(s);i.setState({focusedOption:s,focusedOptionId:c>-1?i.getFocusedOptionId(s):null})}},i.shouldHideSelectedOptions=function(){return Of(i.props)},i.onValueInputFocus=function(s){s.preventDefault(),s.stopPropagation(),i.focus()},i.onKeyDown=function(s){var u=i.props,c=u.isMulti,f=u.backspaceRemovesValue,g=u.escapeClearsValue,h=u.inputValue,m=u.isClearable,p=u.isDisabled,v=u.menuIsOpen,w=u.onKeyDown,b=u.tabSelectsValue,M=u.openMenuOnFocus,O=i.state,S=O.focusedOption,R=O.focusedValue,L=O.selectValue;if(!p&&!(typeof w=="function"&&(w(s),s.defaultPrevented))){switch(i.blockOptionHover=!0,s.key){case"ArrowLeft":if(!c||h)return;i.focusValue("previous");break;case"ArrowRight":if(!c||h)return;i.focusValue("next");break;case"Delete":case"Backspace":if(h)return;if(R)i.removeValue(R);else{if(!f)return;c?i.popValue():m&&i.clearValue()}break;case"Tab":if(i.isComposing||s.shiftKey||!v||!b||!S||M&&i.isOptionSelected(S,L))return;i.selectOption(S);break;case"Enter":if(s.keyCode===229)break;if(v){if(!S||i.isComposing)return;i.selectOption(S);break}return;case"Escape":v?(i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange("",{action:"menu-close",prevInputValue:h}),i.onMenuClose()):m&&g&&i.clearValue();break;case" ":if(h)return;if(!v){i.openMenu("first");break}if(!S)return;i.selectOption(S);break;case"ArrowUp":v?i.focusOption("up"):i.openMenu("last");break;case"ArrowDown":v?i.focusOption("down"):i.openMenu("first");break;case"PageUp":if(!v)return;i.focusOption("pageup");break;case"PageDown":if(!v)return;i.focusOption("pagedown");break;case"Home":if(!v)return;i.focusOption("first");break;case"End":if(!v)return;i.focusOption("last");break;default:return}s.preventDefault()}},i.state.instancePrefix="react-select-"+(i.props.instanceId||++Ey),i.state.selectValue=oc(r.value),r.menuIsOpen&&i.state.selectValue.length){var o=i.getFocusableOptionsWithIds(),a=i.buildFocusableOptions(),l=a.indexOf(i.state.selectValue[0]);i.state.focusableOptionsWithIds=o,i.state.focusedOption=a[l],i.state.focusedOptionId=ys(o,a[l])}return i}return Cg(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&ac(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(i){var o=this.props,a=o.isDisabled,l=o.menuIsOpen,s=this.state.isFocused;(s&&!a&&i.isDisabled||s&&l&&!i.menuIsOpen)&&this.focusInput(),s&&a&&!i.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!s&&!a&&i.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(ac(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(i,o){this.props.onInputChange(i,o)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(i){var o=this,a=this.state,l=a.selectValue,s=a.isFocused,u=this.buildFocusableOptions(),c=i==="first"?0:u.length-1;if(!this.props.isMulti){var f=u.indexOf(l[0]);f>-1&&(c=f)}this.scrollToFocusedOptionOnUpdate=!(s&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:u[c],focusedOptionId:this.getFocusedOptionId(u[c])},function(){return o.onMenuOpen()})}},{key:"focusValue",value:function(i){var o=this.state,a=o.selectValue,l=o.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var s=a.indexOf(l);l||(s=-1);var u=a.length-1,c=-1;if(a.length){switch(i){case"previous":s===0?c=0:s===-1?c=u:c=s-1;break;case"next":s>-1&&s<u&&(c=s+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:a[c]})}}}},{key:"focusOption",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",o=this.props.pageSize,a=this.state.focusedOption,l=this.getFocusableOptions();if(l.length){var s=0,u=l.indexOf(a);a||(u=-1),i==="up"?s=u>0?u-1:l.length-1:i==="down"?s=(u+1)%l.length:i==="pageup"?(s=u-o,s<0&&(s=0)):i==="pagedown"?(s=u+o,s>l.length-1&&(s=l.length-1)):i==="last"&&(s=l.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:l[s],focusedValue:null,focusedOptionId:this.getFocusedOptionId(l[s])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(ws):Ye(Ye({},ws),this.props.theme):ws}},{key:"getCommonProps",value:function(){var i=this.clearValue,o=this.cx,a=this.getStyles,l=this.getClassNames,s=this.getValue,u=this.selectOption,c=this.setValue,f=this.props,g=f.isMulti,h=f.isRtl,m=f.options,p=this.hasValue();return{clearValue:i,cx:o,getStyles:a,getClassNames:l,getValue:s,hasValue:p,isMulti:g,isRtl:h,options:m,selectOption:u,selectProps:f,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var i=this.state.selectValue;return i.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var i=this.props,o=i.isClearable,a=i.isMulti;return o===void 0?a:o}},{key:"isOptionDisabled",value:function(i,o){return If(this.props,i,o)}},{key:"isOptionSelected",value:function(i,o){return Tf(this.props,i,o)}},{key:"filterOption",value:function(i,o){return Df(this.props,i,o)}},{key:"formatOptionLabel",value:function(i,o){if(typeof this.props.formatOptionLabel=="function"){var a=this.props.inputValue,l=this.state.selectValue;return this.props.formatOptionLabel(i,{context:o,inputValue:a,selectValue:l})}else return this.getOptionLabel(i)}},{key:"formatGroupLabel",value:function(i){return this.props.formatGroupLabel(i)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var i=this.props,o=i.isDisabled,a=i.isSearchable,l=i.inputId,s=i.inputValue,u=i.tabIndex,c=i.form,f=i.menuIsOpen,g=i.required,h=this.getComponents(),m=h.Input,p=this.state,v=p.inputIsHidden,w=p.ariaSelection,b=this.commonProps,M=l||this.getElementId("input"),O=Ye(Ye(Ye({"aria-autocomplete":"list","aria-expanded":f,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":g,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},f&&{"aria-controls":this.getElementId("listbox")}),!a&&{"aria-readonly":!0}),this.hasValue()?(w==null?void 0:w.action)==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return a?d.createElement(m,Ke({},b,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:M,innerRef:this.getInputRef,isDisabled:o,isHidden:v,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:s},O)):d.createElement(Qw,Ke({id:M,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:ba,onFocus:this.onInputFocus,disabled:o,tabIndex:u,inputMode:"none",form:c,value:""},O))}},{key:"renderPlaceholderOrValue",value:function(){var i=this,o=this.getComponents(),a=o.MultiValue,l=o.MultiValueContainer,s=o.MultiValueLabel,u=o.MultiValueRemove,c=o.SingleValue,f=o.Placeholder,g=this.commonProps,h=this.props,m=h.controlShouldRenderValue,p=h.isDisabled,v=h.isMulti,w=h.inputValue,b=h.placeholder,M=this.state,O=M.selectValue,S=M.focusedValue,R=M.isFocused;if(!this.hasValue()||!m)return w?null:d.createElement(f,Ke({},g,{key:"placeholder",isDisabled:p,isFocused:R,innerProps:{id:this.getElementId("placeholder")}}),b);if(v)return O.map(function(E,x){var _=E===S,D="".concat(i.getOptionLabel(E),"-").concat(i.getOptionValue(E));return d.createElement(a,Ke({},g,{components:{Container:l,Label:s,Remove:u},isFocused:_,isDisabled:p,key:D,index:x,removeProps:{onClick:function(){return i.removeValue(E)},onTouchEnd:function(){return i.removeValue(E)},onMouseDown:function(I){I.preventDefault()}},data:E}),i.formatOptionLabel(E,"value"))});if(w)return null;var L=O[0];return d.createElement(c,Ke({},g,{data:L,isDisabled:p}),this.formatOptionLabel(L,"value"))}},{key:"renderClearIndicator",value:function(){var i=this.getComponents(),o=i.ClearIndicator,a=this.commonProps,l=this.props,s=l.isDisabled,u=l.isLoading,c=this.state.isFocused;if(!this.isClearable()||!o||s||!this.hasValue()||u)return null;var f={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return d.createElement(o,Ke({},a,{innerProps:f,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var i=this.getComponents(),o=i.LoadingIndicator,a=this.commonProps,l=this.props,s=l.isDisabled,u=l.isLoading,c=this.state.isFocused;if(!o||!u)return null;var f={"aria-hidden":"true"};return d.createElement(o,Ke({},a,{innerProps:f,isDisabled:s,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator,a=i.IndicatorSeparator;if(!o||!a)return null;var l=this.commonProps,s=this.props.isDisabled,u=this.state.isFocused;return d.createElement(a,Ke({},l,{isDisabled:s,isFocused:u}))}},{key:"renderDropdownIndicator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator;if(!o)return null;var a=this.commonProps,l=this.props.isDisabled,s=this.state.isFocused,u={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return d.createElement(o,Ke({},a,{innerProps:u,isDisabled:l,isFocused:s}))}},{key:"renderMenu",value:function(){var i=this,o=this.getComponents(),a=o.Group,l=o.GroupHeading,s=o.Menu,u=o.MenuList,c=o.MenuPortal,f=o.LoadingMessage,g=o.NoOptionsMessage,h=o.Option,m=this.commonProps,p=this.state.focusedOption,v=this.props,w=v.captureMenuScroll,b=v.inputValue,M=v.isLoading,O=v.loadingMessage,S=v.minMenuHeight,R=v.maxMenuHeight,L=v.menuIsOpen,E=v.menuPlacement,x=v.menuPosition,_=v.menuPortalTarget,D=v.menuShouldBlockScroll,C=v.menuShouldScrollIntoView,I=v.noOptionsMessage,T=v.onMenuScrollToTop,k=v.onMenuScrollToBottom;if(!L)return null;var z=function(ie,le){var fe=ie.type,Q=ie.data,H=ie.isDisabled,P=ie.isSelected,W=ie.label,ce=ie.value,De=p===Q,He=H?void 0:function(){return i.onOptionHover(Q)},ye=H?void 0:function(){return i.selectOption(Q)},Re="".concat(i.getElementId("option"),"-").concat(le),Se={id:Re,onClick:ye,onMouseMove:He,onMouseOver:He,tabIndex:-1,role:"option","aria-selected":i.isAppleDevice?void 0:P};return d.createElement(h,Ke({},m,{innerProps:Se,data:Q,isDisabled:H,isSelected:P,key:Re,label:W,type:fe,value:ce,isFocused:De,innerRef:De?i.getFocusedOptionRef:void 0}),i.formatOptionLabel(ie.data,"menu"))},$;if(this.hasOptions())$=this.getCategorizedOptions().map(function(ae){if(ae.type==="group"){var ie=ae.data,le=ae.options,fe=ae.index,Q="".concat(i.getElementId("group"),"-").concat(fe),H="".concat(Q,"-heading");return d.createElement(a,Ke({},m,{key:Q,data:ie,options:le,Heading:l,headingProps:{id:H,data:ae.data},label:i.formatGroupLabel(ae.data)}),ae.options.map(function(P){return z(P,"".concat(fe,"-").concat(P.index))}))}else if(ae.type==="option")return z(ae,"".concat(ae.index))});else if(M){var X=O({inputValue:b});if(X===null)return null;$=d.createElement(f,m,X)}else{var re=I({inputValue:b});if(re===null)return null;$=d.createElement(g,m,re)}var j={minMenuHeight:S,maxMenuHeight:R,menuPlacement:E,menuPosition:x,menuShouldScrollIntoView:C},G=d.createElement(Pb,Ke({},m,j),function(ae){var ie=ae.ref,le=ae.placerProps,fe=le.placement,Q=le.maxHeight;return d.createElement(s,Ke({},m,j,{innerRef:ie,innerProps:{onMouseDown:i.onMenuMouseDown,onMouseMove:i.onMenuMouseMove},isLoading:M,placement:fe}),d.createElement(oy,{captureEnabled:w,onTopArrive:T,onBottomArrive:k,lockEnabled:D},function(H){return d.createElement(u,Ke({},m,{innerRef:function(W){i.getMenuListRef(W),H(W)},innerProps:{role:"listbox","aria-multiselectable":m.isMulti,id:i.getElementId("listbox")},isLoading:M,maxHeight:Q,focusedOption:p}),$)}))});return _||x==="fixed"?d.createElement(c,Ke({},m,{appendTo:_,controlElement:this.controlRef,menuPlacement:E,menuPosition:x}),G):G}},{key:"renderFormField",value:function(){var i=this,o=this.props,a=o.delimiter,l=o.isDisabled,s=o.isMulti,u=o.name,c=o.required,f=this.state.selectValue;if(c&&!this.hasValue()&&!l)return d.createElement(ly,{name:u,onFocus:this.onValueInputFocus});if(!(!u||l))if(s)if(a){var g=f.map(function(p){return i.getOptionValue(p)}).join(a);return d.createElement("input",{name:u,type:"hidden",value:g})}else{var h=f.length>0?f.map(function(p,v){return d.createElement("input",{key:"i-".concat(v),name:u,type:"hidden",value:i.getOptionValue(p)})}):d.createElement("input",{name:u,type:"hidden",value:""});return d.createElement("div",null,h)}else{var m=f[0]?this.getOptionValue(f[0]):"";return d.createElement("input",{name:u,type:"hidden",value:m})}}},{key:"renderLiveRegion",value:function(){var i=this.commonProps,o=this.state,a=o.ariaSelection,l=o.focusedOption,s=o.focusedValue,u=o.isFocused,c=o.selectValue,f=this.getFocusableOptions();return d.createElement(Xw,Ke({},i,{id:this.getElementId("live-region"),ariaSelection:a,focusedOption:l,focusedValue:s,isFocused:u,selectValue:c,focusableOptions:f,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var i=this.getComponents(),o=i.Control,a=i.IndicatorsContainer,l=i.SelectContainer,s=i.ValueContainer,u=this.props,c=u.className,f=u.id,g=u.isDisabled,h=u.menuIsOpen,m=this.state.isFocused,p=this.commonProps=this.getCommonProps();return d.createElement(l,Ke({},p,{className:c,innerProps:{id:f,onKeyDown:this.onKeyDown},isDisabled:g,isFocused:m}),this.renderLiveRegion(),d.createElement(o,Ke({},p,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:g,isFocused:m,menuIsOpen:h}),d.createElement(s,Ke({},p,{isDisabled:g}),this.renderPlaceholderOrValue(),this.renderInput()),d.createElement(a,Ke({},p,{isDisabled:g}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(i,o){var a=o.prevProps,l=o.clearFocusValueOnUpdate,s=o.inputIsHiddenAfterUpdate,u=o.ariaSelection,c=o.isFocused,f=o.prevWasFocused,g=o.instancePrefix,h=i.options,m=i.value,p=i.menuIsOpen,v=i.inputValue,w=i.isMulti,b=oc(m),M={};if(a&&(m!==a.value||h!==a.options||p!==a.menuIsOpen||v!==a.inputValue)){var O=p?ky(i,b):[],S=p?yc(ra(i,b),"".concat(g,"-option")):[],R=l?My(o,b):null,L=Ry(o,O),E=ys(S,L);M={selectValue:b,focusedOption:L,focusedOptionId:E,focusableOptionsWithIds:S,focusedValue:R,clearFocusValueOnUpdate:!1}}var x=s!=null&&i!==a?{inputIsHidden:s,inputIsHiddenAfterUpdate:void 0}:{},_=u,D=c&&f;return c&&!D&&(_={value:Jo(w,b,b[0]||null),options:b,action:"initial-input-focus"},D=!f),(u==null?void 0:u.action)==="initial-input-focus"&&(_=null),Ye(Ye(Ye({},M),x),{},{prevProps:i,ariaSelection:_,prevWasFocused:D})}}]),n}(d.Component);Pf.defaultProps=xy;var Iy=d.forwardRef(function(e,t){var n=Q1(e);return d.createElement(Pf,Ke({ref:t},n))}),Ty=Iy;const Dy=e=>{const{Menu:t}=Cf,{children:n,...r}=e;return d.createElement(t,{...r},n)},Oy=mn("div")({name:"Wrap",class:"gdg-wghi2zc",propsAsIs:!1}),Py=mn("div")({name:"PortalWrap",class:"gdg-p13nj8j0",propsAsIs:!1}),Ly=mn("div")({name:"ReadOnlyWrap",class:"gdg-r6sia3g",propsAsIs:!1}),_y=e=>{const{value:t,onFinishedEditing:n,initialValue:r}=e,{allowedValues:i,value:o}=t.data,[a,l]=d.useState(o),[s,u]=d.useState(r??""),c=Gm(),f=d.useMemo(()=>i.map(g=>typeof g=="string"||g===null||g===void 0?{value:g,label:(g==null?void 0:g.toString())??""}:g),[i]);return t.readonly?d.createElement(Ly,null,d.createElement(Zr,{highlight:!0,autoFocus:!1,disabled:!0,value:a??"",onChange:()=>{}})):d.createElement(Oy,null,d.createElement(Ty,{className:"glide-select",inputValue:s,onInputChange:u,menuPlacement:"auto",value:f.find(g=>g.value===a),styles:{control:g=>({...g,border:0,boxShadow:"none"}),option:(g,{isFocused:h})=>({...g,fontSize:c.editorFontSize,fontFamily:c.fontFamily,cursor:h?"pointer":void 0,paddingLeft:c.cellHorizontalPadding,paddingRight:c.cellHorizontalPadding,":active":{...g[":active"],color:c.accentFg},":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})},theme:g=>({...g,colors:{...g.colors,neutral0:c.bgCell,neutral5:c.bgCell,neutral10:c.bgCell,neutral20:c.bgCellMedium,neutral30:c.bgCellMedium,neutral40:c.bgCellMedium,neutral50:c.textLight,neutral60:c.textMedium,neutral70:c.textMedium,neutral80:c.textDark,neutral90:c.textDark,neutral100:c.textDark,primary:c.accentColor,primary75:c.accentColor,primary50:c.accentColor,primary25:c.accentLight}}),menuPortalTarget:document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:g=>d.createElement(Py,null,d.createElement(Dy,{className:"click-outside-ignore",...g}))},options:f,onChange:async g=>{g!==null&&(l(g.value),await new Promise(h=>window.requestAnimationFrame(h)),n({...t,data:{...t.data,value:g.value}}))}}))},Fy={kind:te.Custom,isMatch:e=>e.data.kind==="dropdown-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{value:o}=t.data,a=t.data.allowedValues.find(s=>typeof s=="string"||s===null||s===void 0?s===o:s.value===o),l=typeof a=="string"?a:(a==null?void 0:a.label)??"";return l&&(n.fillStyle=r.textDark,n.fillText(l,i.x+r.cellHorizontalPadding,i.y+i.height/2+dr(n,r))),!0},measure:(e,t,n)=>{const{value:r}=t.data;return(r?e.measureText(r).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:_y,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.includes(e)?e:t.value})},Ur=6,Ay={marginRight:8},Hy={display:"flex",alignItems:"center",flexGrow:1},zy={kind:te.Custom,isMatch:e=>e.data.kind==="range-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{min:o,max:a,value:l,label:s,measureLabel:u}=t.data,c=i.x+r.cellHorizontalPadding,f=i.y+i.height/2,g=a-o,h=(l-o)/g;n.save();let m=0;s!==void 0&&(n.font=`12px ${r.fontFamily}`,m=Qr(u??s,n,`12px ${r.fontFamily}`).width+r.cellHorizontalPadding);const p=i.width-r.cellHorizontalPadding*2-m;if(p>=Ur){const v=n.createLinearGradient(c,f,c+p,f);v.addColorStop(0,r.accentColor),v.addColorStop(h,r.accentColor),v.addColorStop(h,r.bgBubble),v.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=v,ec(n,c,f-Ur/2,p,Ur,Ur/2),n.fill(),n.beginPath(),ec(n,c+.5,f-Ur/2+.5,p-1,Ur-1,(Ur-1)/2),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return s!==void 0&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(s,i.x+i.width-r.cellHorizontalPadding,f+dr(n,`12px ${r.fontFamily}`))),n.restore(),!0},provideEditor:()=>e=>{const{data:t,readonly:n}=e.value,r=t.value.toString(),i=t.min.toString(),o=t.max.toString(),a=t.step.toString(),l=s=>{e.onChange({...e.value,data:{...t,value:Number(s.target.value)}})};return d.createElement("label",{style:Hy},d.createElement("input",{style:Ay,type:"range",value:r,min:i,max:o,step:a,onChange:l,disabled:n}),r)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}},Vy=mn("input")({name:"StyledInputBox",class:"gdg-s1wtovjx",propsAsIs:!1}),Cs=(e,t)=>{if(t==null)return"";const n=t.toISOString();switch(e){case"date":return n.split("T")[0];case"datetime-local":return n.replace("Z","");case"time":return n.split("T")[1].replace("Z","");default:throw new Error(`Unknown date kind ${e}`)}},Ny=e=>{const t=e.value.data,{format:n,displayDate:r}=t,i=t.step!==void 0&&!Number.isNaN(Number(t.step))?Number(t.step):void 0,o=t.min instanceof Date?Cs(n,t.min):t.min,a=t.max instanceof Date?Cs(n,t.max):t.max;let l=t.date;const s=t.timezoneOffset?t.timezoneOffset*60*1e3:0;s&&l&&(l=new Date(l.getTime()+s));const u=Cs(n,l);return e.value.readonly?At.createElement(Zr,{highlight:!0,autoFocus:!1,disabled:!0,value:r??"",onChange:()=>{}}):At.createElement(Vy,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:u,min:o,max:a,step:i,autoFocus:!0,onChange:c=>{isNaN(c.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(c.target.valueAsNumber-s)}})}})},$y={kind:te.Custom,isMatch:e=>e.data.kind==="date-picker-cell",draw:(e,t)=>{const{displayDate:n}=t.data;return qs(e,n,t.contentAlign),!0},measure:(e,t,n)=>{const{displayDate:r}=t.data;return e.measureText(r).width+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Ny}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),t.format==="time"&&Number.isNaN(n)&&(n=Date.parse(`1970-01-01T${e}Z`)))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}},By="None";function Sc(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-8,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+8),e.fillStyle=n.accentColor,e.fill(),e.restore()}const Wy=e=>{const{cell:t,theme:n,ctx:r}=e;qs({...e,theme:{...n,textDark:n.textLight,headerFontFull:`${n.headerFontStyle} ${n.fontFamily}`,baseFontFull:`${n.baseFontStyle} ${n.fontFamily}`,markerFontFull:`${n.markerFontStyle} ${n.fontFamily}`}},By,t.contentAlign),r.fillStyle=n.textDark};function Uy(e){const t=d.useCallback((r,i)=>{const{cell:o,theme:a,ctx:l,rect:s}=r,u=r.col;if(yi(o))Sc(l,s,a);else if(Ma(o)&&u<e.length){const c=e[u];["checkbox","line_chart","bar_chart","progress"].includes(c.kind)?i():Wy(r),c.isRequired&&c.isEditable&&Sc(l,s,a);return}i()},[e]),n=d.useMemo(()=>[Z1,Fy,zy,$y,...M1],[]);return{drawCell:t,customRenderers:n}}function qy(){const e=Gr();return d.useMemo(()=>{const n={editable:i=>`<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="${i.bgColor}"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>`};return{glideTheme:{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:gi(e.colors.primary,.9),borderColor:e.colors.dataframeBorderColor,horizontalBorderColor:e.colors.dataframeBorderColor,fontFamily:e.genericFonts.bodyFont,bgSearchResult:gi(e.colors.primary,.9),resizeIndicatorColor:e.colors.primary,bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.bgMix,bgHeaderHasFocus:gi(e.colors.darkenedBgMix100,.9),bgHeaderHovered:gi(e.colors.darkenedBgMix100,.9),textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerFontStyle:`${Wn(e.fontSizes.sm)}px`,baseFontStyle:`${Wn(e.fontSizes.sm)}px`,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:gi(e.colors.bodyText,.2),textLight:e.colors.fadedText40,textBubble:e.colors.fadedText60,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:Math.round(Wn(e.spacing.sm)),cellVerticalPadding:Math.round(Wn("0.1875rem")),bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,linkColor:e.colors.link,drilldownBorder:e.colors.darkenedBgMix25},tableBorderRadius:e.radii.default,tableBorderWidth:1,defaultTableHeight:Math.round(Wn("25rem")),minColumnWidth:Math.round(Wn("3.125rem")),maxColumnWidth:Math.round(Wn("62.5rem")),maxColumnAutoWidth:Math.round(Wn("31.25rem")),defaultRowHeight:Math.round(Wn("2.1875rem")),defaultHeaderHeight:Math.round(Wn("2.1875rem")),bgRowHovered:ng(e.colors.bgColor,e.colors.secondaryBg,.3),headerIcons:n}},[e])}const Yy=As.getLogger("useDataEditor");function Xy(e,t,n,r,i,o,a,l,s){const u=d.useCallback(([p,v],w)=>{const b=e[p];if(!b.isEditable)return;const M=b.indexNumber,O=n.current.getOriginalRowIndex(i(v)),S=r([p,v]),R=b.getCellValue(S),L=b.getCellValue(w);if(!yi(S)&&L===R)return;const E=b.getCell(L,!0);yi(E)?Yy.warn(`Not applying the cell edit since it causes this error:
 ${E.data}`):(n.current.setCell(M,O,{...E,lastUpdated:performance.now()}),l())},[e,n,i,r,l]),c=d.useCallback(()=>{if(t)return;const p=new Map;e.forEach(v=>{p.set(v.indexNumber,v.getCell(v.defaultValue))}),n.current.addRow(p),a()},[e,n,t,a]),f=d.useCallback(()=>{t||(c(),l())},[c,l,t]),g=d.useCallback(p=>{var v;if(p.rows.length>0){if(t)return!0;const w=p.rows.toArray().map(b=>n.current.getOriginalRowIndex(i(b)));return n.current.deleteRows(w),a(),s(),l(),!1}if((v=p.current)!=null&&v.range){const w=[],b=p.current.range;for(let M=b.y;M<b.y+b.height;M++)for(let O=b.x;O<b.x+b.width;O++){const S=e[O];S.isEditable&&!S.isRequired&&(w.push({cell:[O,M]}),u([O,M],S.getCell(null)))}return w.length>0&&(l(),o(w)),!1}return!0},[e,n,t,o,i,l,u,s,a]),h=d.useCallback((p,v)=>{const[w,b]=p,M=[];for(let O=0;O<v.length;O++){const S=v[O];if(O+b>=n.current.getNumRows()){if(t)break;c()}for(let R=0;R<S.length;R++){const L=S[R],E=O+b,x=R+w;if(x>=e.length)break;const _=e[x];if(_.isEditable){const D=_.getCell(L,!0);if(pt(D)&&!yi(D)){const C=_.indexNumber,I=n.current.getOriginalRowIndex(i(E)),T=_.getCellValue(r([x,E]));_.getCellValue(D)!==T&&(n.current.setCell(C,I,{...D,lastUpdated:performance.now()}),M.push({cell:[x,E]}))}}}M.length>0&&(l(),o(M))}return!1},[e,n,t,i,r,c,l,o]),m=d.useCallback((p,v)=>{const w=p[0];if(w>=e.length)return!0;const b=e[w];if(b.validateInput){const M=b.validateInput(b.getCellValue(v));return M===!0||M===!1?M:b.getCell(M)}return!0},[e]);return{onCellEdited:u,onPaste:h,onRowAppended:f,onDelete:g,validateCell:m}}const Lf=",",eo='"',jy='"',_f=`
`,Gy="\uFEFF",Ky=new RegExp(`[${[Lf,eo,_f].join("")}]`),xc=As.getLogger("useDataExporter");function kc(e){return e.map(t=>Zy(t)).join(Lf)+_f}function Zy(e){if($e(e))return"";const t=bt(e);return Ky.test(t)?`${eo}${t.replace(new RegExp(eo,"g"),jy+eo)}${eo}`:t}async function Mc(e,t,n,r){const i=new TextEncoder;await e.write(i.encode(Gy));const o=n.map(a=>a.name);await e.write(i.encode(kc(o)));for(let a=0;a<r;a++){const l=[];n.forEach((s,u,c)=>{l.push(s.getCellValue(t([u,a])))}),await e.write(i.encode(kc(l)))}await e.close()}function Jy(e,t,n,r){return{exportToCsv:d.useCallback(async()=>{const a=`${new Date().toISOString().slice(0,16).replace(":","-")}_export.csv`;try{const u=await(await(await Fs(()=>import("./es6.Dlcvh_r0.js").then(c=>c.a),__vite__mapDeps([15,1,2]),import.meta.url)).showSaveFilePicker({suggestedName:a,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1})).createWritable();await Mc(u,e,t,n)}catch(l){if(l instanceof Error&&l.name==="AbortError")return;try{xc.warn("Failed to export data as CSV with FileSystem API, trying fallback method",l);let s="";const u=new WritableStream({write:h=>{s+=new TextDecoder("utf-8").decode(h)},close:async()=>{}});await Mc(u.getWriter(),e,t,n);const c=new Blob([s],{type:"text/csv;charset=utf-8;"}),f=URL.createObjectURL(c),g=bg({enforceDownloadInNewTab:r,url:f,filename:a});g.style.display="none",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(f)}catch(s){xc.error("Failed to export data as CSV",s)}}},[t,n,e,r])}}function Qy(e,t,n,r){return{getCellContent:d.useCallback(([o,a])=>{var l;if(o>t.length-1)return Ht("Column index out of bounds","This error should never happen. Please report this bug.");if(a>n-1)return Ht("Row index out of bounds","This error should never happen. Please report this bug.");try{const s=t[o],u=s.indexNumber,c=r.current.getOriginalRowIndex(a),f=r.current.isAddedRow(c);if(s.isEditable||f){const m=r.current.getCell(u,c);if(pt(m))return{...s.getCell(s.getCellValue(m),!1),lastUpdated:m.lastUpdated};if(f)return Ht("Error during cell creation",`This error should never happen. Please report this bug. No cell found for an added row: col=${u}; row=${c}`)}const g=e.getCell(c,u),h=rg(e,c,u);return F1(s,g,h,(l=e.styler)==null?void 0:l.cssStyles)}catch(s){return Ht("Error during cell creation",`This error should never happen. Please report this bug. 
Error: ${s}`)}},[t,n,e,r])}}function eC(e){const[t,n]=d.useState(void 0),r=d.useCallback(o=>{if(o.kind!=="cell")n(void 0);else{const[,a]=o.location;n(a)}},[n]);return{getRowThemeOverride:d.useCallback(o=>{if(o===t)return{bgCell:e.bgRowHovered,bgCellMedium:e.bgRowHovered}},[e.bgRowHovered,t]),onItemHovered:r}}function tC(e,t,n,r,i){const[o,a]=d.useState({columns:mt.empty(),rows:mt.empty(),current:void 0}),l=!t&&!n&&(e.selectionMode.includes(Mn.SelectionMode.MULTI_ROW)||e.selectionMode.includes(Mn.SelectionMode.SINGLE_ROW)),s=l&&e.selectionMode.includes(Mn.SelectionMode.MULTI_ROW),u=!t&&!n&&(e.selectionMode.includes(Mn.SelectionMode.SINGLE_COLUMN)||e.selectionMode.includes(Mn.SelectionMode.MULTI_COLUMN)),c=u&&e.selectionMode.includes(Mn.SelectionMode.MULTI_COLUMN),f=o.rows.length>0,g=o.columns.length>0,h=o.current!==void 0,m=d.useCallback(v=>{const w=!ja(v.rows.toArray(),o.rows.toArray()),b=!ja(v.columns.toArray(),o.columns.toArray()),M=!ja(v.current,o.current);let O=l&&w||u&&b,S=v;if((l||u)&&v.current!==void 0&&M&&(S={...v,rows:o.rows,columns:o.columns},O=!1),w&&v.rows.length>0&&b&&v.columns.length===0&&(S={...S,columns:o.columns},O=!0),b&&v.columns.length>0&&w&&v.rows.length===0&&(S={...S,rows:o.rows},O=!0),b&&S.columns.length>=0){let R=S.columns;r.forEach((L,E)=>{L.isIndex&&(R=R.remove(E))}),R.length<S.columns.length&&(S={...S,columns:R})}a(S),O&&i(S)},[o,l,u,i,r]),p=d.useCallback((v=!1,w=!1)=>{const b={columns:w?o.columns:mt.empty(),rows:v?o.rows:mt.empty(),current:void 0};a(b),(!v&&l||!w&&u)&&i(b)},[o,l,u,i]);return{gridSelection:o,isRowSelectionActivated:l,isMultiRowSelectionActivated:s,isColumnSelectionActivated:u,isMultiColumnSelectionActivated:c,isRowSelected:f,isColumnSelected:g,isCellSelected:h,clearSelection:p,processSelectionChange:m}}function nC(e,t,n,r,i,o,a){const l=e.rowHeight??t.defaultRowHeight,s=t.defaultHeaderHeight+l+2*t.tableBorderWidth,u=r?2:1,c=e.editingMode===Mn.EditingMode.DYNAMIC?1:0,f=n+c;let g=Math.max(f*l+u*t.defaultHeaderHeight+2*t.tableBorderWidth,s),h=Math.min(g,t.defaultTableHeight);e.height&&(h=Math.max(e.height,s),g=Math.max(e.height,g)),o&&(h=Math.min(h,o),g=Math.min(g,o),e.height||(h=g));const m=t.minColumnWidth+2*t.tableBorderWidth,p=Math.max(i,m);let v,w=p;e.useContainerWidth?v=p:e.width&&(v=Math.min(Math.max(e.width,m),p),w=Math.min(Math.max(e.width,w),p));const[b,M]=d.useState({width:v||"100%",height:h});return d.useLayoutEffect(()=>{e.useContainerWidth&&b.width==="100%"&&M(O=>({...O,width:p}))},[p]),d.useLayoutEffect(()=>{M(O=>({...O,width:v||"100%"}))},[v]),d.useLayoutEffect(()=>{M(O=>({...O,height:h}))},[h,n]),d.useLayoutEffect(()=>{if(a){const O=e.useContainerWidth||pt(e.width)&&e.width>0;M({width:O?w:"100%",height:g})}else M({width:v||"100%",height:h})},[a]),{minHeight:s,maxHeight:g,minWidth:m,maxWidth:w,rowHeight:l,resizableSize:b,setResizableSize:M}}const rC=600,iC="⚠️ Please fill out this cell.";function oC(e,t,n=[]){const[r,i]=d.useState(),o=d.useRef(null),a=d.useCallback(s=>{if(clearTimeout(o.current),o.current=0,i(void 0),(s.kind==="header"||s.kind==="cell")&&s.location){const u=s.location[0],c=s.location[1];let f;if(u<0||u>=e.length||n.includes(c))return;const g=e[u];if(s.kind==="header"&&pt(g))f=g.help;else if(s.kind==="cell"){const h=t([u,c]);yi(h)?f=h.errorDetails:g.isRequired&&g.isEditable&&Ma(h)?f=iC:c1(h)&&(f=h.tooltip)}f&&(o.current=setTimeout(()=>{f&&i({content:f,left:s.bounds.x+s.bounds.width/2,top:s.bounds.y})},rC))}},[e,t,i,o,n]),l=d.useCallback(()=>{i(void 0)},[i]);return{tooltip:r,clearTooltip:l,onItemHovered:a}}function aC({top:e,left:t,content:n,clearTooltip:r}){const[i,o]=d.useState(!0),a=Gr(),{colors:l,fontSizes:s,radii:u,fontWeights:c}=a,f=d.useCallback(()=>{o(!1),r()},[r,o]);return lt(xa,{content:lt(ig,{"data-testid":"stDataFrameTooltipContent",children:lt(og,{style:{fontSize:s.sm},source:n,allowHTML:!1})}),placement:Ca.top,accessibilityType:_c.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:f,onEsc:f,overrides:{Body:{style:{borderTopLeftRadius:u.default,borderTopRightRadius:u.default,borderBottomLeftRadius:u.default,borderBottomRightRadius:u.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:Sa(a)?l.bgColor:l.secondaryBg,color:l.bodyText,fontSize:s.sm,fontWeight:c.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:i,children:lt("div",{"data-testid":"stDataFrameTooltipTarget",style:{position:"fixed",top:e,left:t}})})}const sC=d.memo(aC),Rc=xi("div",{target:"ew1n8yw0"})(({hasCustomizedScrollbars:e,theme:t})=>({position:"relative",display:"inline-block","& .stDataFrameGlideDataEditor":{height:"100%",minWidth:"100%",borderRadius:t.radii.default},"& .dvn-scroller":{...!e&&{scrollbarWidth:"thin"},overflowX:"auto !important",overflowY:"auto !important"},"& .gdg-seveqep":{maxWidth:"19rem",width:"80%",minWidth:"6rem",top:t.spacing.sm,right:t.spacing.sm,padding:t.spacing.sm,borderRadius:t.radii.default,"& .gdg-search-status":{paddingTop:t.spacing.twoXS,fontSize:t.fontSizes.twoSm},"& .gdg-search-progress":{display:"none"},"& input":{width:"100%"},"& button":{width:t.iconSizes.xl,height:t.iconSizes.xl,"& .button-icon":{width:t.iconSizes.base,height:t.iconSizes.base}}}})),Ec=150,lC=15e4,Ic=6;function uC({element:e,data:t,disabled:n,widgetMgr:r,disableFullscreenMode:i,fragmentId:o}){const{expanded:a,expand:l,collapse:s,width:u,height:c}=ag(dg),f=d.useRef(null),g=d.useRef(null),h=d.useRef(null),m=qy(),{getRowThemeOverride:p,onItemHovered:v}=eC(m),{libConfig:{enforceDownloadInNewTab:w=!1}}=d.useContext(sg),[b,M]=d.useState(!0),[O,S]=d.useState(!1),[R,L]=d.useState(!1),[E,x]=d.useState(!1),[_,D]=d.useState(),[C,I]=d.useState(!1),T=d.useMemo(()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches,[]),k=d.useMemo(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")||window.navigator.userAgent.includes("Chrome"),[]);$e(e.editingMode)&&(e.editingMode=Mn.EditingMode.READ_ONLY);const{READ_ONLY:z,DYNAMIC:$}=Mn.EditingMode,X=t.dimensions,re=Math.max(0,X.numDataRows),j=re===0&&!(e.editingMode===$&&X.numDataColumns>0),G=re>lC,ae=!G&&!j&&e.editingMode!==$,ie=!j&&e.editingMode===$&&!n,le=d.useRef(new jo(re)),[fe,Q]=d.useState(le.current.getNumRows());d.useEffect(()=>{le.current=new jo(re),Q(le.current.getNumRows())},[re]);const H=d.useCallback(()=>{le.current=new jo(re),Q(le.current.getNumRows())},[re]),[P,W]=d.useState(e.columnOrder);d.useEffect(()=>{W(e.columnOrder)},[e.columnOrder.join(",")]);const{columns:ce,allColumns:De,setColumnConfigMapping:He}=z1(e,t,n,P);d.useEffect(()=>{if(e.editingMode===z)return;const de=r.getStringValue({id:e.id,formId:e.formId});de&&(le.current.fromJson(de,ce),Q(le.current.getNumRows()))},[]);const{getCellContent:ye}=Qy(t,ce,fe,le),{columns:Re,sortColumn:Se,getOriginalIndex:Et,getCellContent:wt}=j1(re,ce,ye),rt=d.useCallback(de=>{const st={selection:{rows:[],columns:[]}};st.selection.rows=de.rows.toArray().map(Zt=>Et(Zt)),st.selection.columns=de.columns.toArray().map(Zt=>oo(Re[Zt]));const Wt=JSON.stringify(st),dn=r.getStringValue({id:e.id,formId:e.formId});(dn===void 0||dn!==Wt)&&r.setStringValue({id:e.id,formId:e.formId},Wt,{fromUi:!0},o)},[Re,e.id,e.formId,r,o,Et]),{debouncedCallback:se}=Vu(rt,Ec),{gridSelection:et,isRowSelectionActivated:me,isMultiRowSelectionActivated:he,isColumnSelectionActivated:pe,isMultiColumnSelectionActivated:ze,isRowSelected:Pe,isColumnSelected:tt,isCellSelected:ve,clearSelection:ue,processSelectionChange:it}=tC(e,j,n,Re,se);d.useEffect(()=>{ue(!0,!0)},[a]);const Ae=d.useCallback(de=>{var st;(st=g.current)==null||st.updateCells(de)},[]);d.useEffect(()=>{var st,Wt,dn,Zt;if(!me&&!pe)return;const de=r.getStringValue({id:e.id,formId:e.formId});if(de){const Gt=Re.map(Xn=>oo(Xn)),Sn=JSON.parse(de);let vn=mt.empty(),Cr=mt.empty();(Wt=(st=Sn.selection)==null?void 0:st.rows)==null||Wt.forEach(Xn=>{vn=vn.add(Xn)}),(Zt=(dn=Sn.selection)==null?void 0:dn.columns)==null||Zt.forEach(Xn=>{Cr=Cr.add(Gt.indexOf(Xn))}),(vn.length>0||Cr.length>0)&&it({rows:vn,columns:Cr,current:void 0})}},[]);const Qe=d.useCallback(()=>{fe!==le.current.getNumRows()&&Q(le.current.getNumRows())},[fe]),vt=d.useCallback(()=>{const de=le.current.toJson(Re);let st=r.getStringValue({id:e.id,formId:e.formId});st===void 0&&(st=new jo(0).toJson([])),de!==st&&r.setStringValue({id:e.id,formId:e.formId},de,{fromUi:!0},o)},[Re,e.id,e.formId,r,o]),{debouncedCallback:ht}=Vu(vt,Ec),{exportToCsv:Ve}=Jy(wt,Re,fe,w),{onCellEdited:Dt,onPaste:Yt,onRowAppended:xt,onDelete:Lt,validateCell:nn}=Xy(Re,e.editingMode!==$,le,wt,Et,Ae,Qe,ht,ue),zt=d.useMemo(()=>j?[0]:ie?[fe]:[],[j,ie,fe]),{tooltip:ln,clearTooltip:It,onItemHovered:yt}=oC(Re,wt,zt),{drawCell:Dn,customRenderers:yn}=Uy(Re),{provideEditor:Oe}=K1(),_t=d.useCallback(de=>({...de,hasMenu:!j}),[j]),fn=d.useMemo(()=>Re.map(de=>_t(Ds(de))),[Re,_t]),{columns:hn,onColumnResize:pn}=B1(fn),at=t.dimensions.numHeaderRows>1,{minHeight:Xt,maxHeight:Ot,minWidth:rn,maxWidth:Tt,rowHeight:Ft,resizableSize:Be,setResizableSize:Bt}=nC(e,m,fe,at,u||0,c,a),Pt=d.useCallback(([de,st])=>({...d1(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:m.glideTheme.textLight},span:[0,Math.max(Re.length-1,0)]}),[Re,m.glideTheme.textLight]),Vt=d.useCallback(()=>{H(),ue()},[H,ue]);ug({element:e,widgetMgr:r,onFormCleared:Vt});const{pinColumn:Nt,unpinColumn:Cn,freezeColumns:nr}=N1(Re,j,u||0,m.minColumnWidth,ue,He),{changeColumnFormat:Hn}=V1(He),{hideColumn:V,showColumn:Je}=G1(ue,He),{onColumnMoved:We}=$1(Re,nr,Nt,Cn,W);return d.useEffect(()=>{setTimeout(()=>{var de,st;if(h.current&&g.current){const Wt=(st=(de=h.current)==null?void 0:de.querySelector(".dvn-stack"))==null?void 0:st.getBoundingClientRect();Wt&&(L(Wt.height>h.current.clientHeight),x(Wt.width>h.current.clientWidth))}},1)},[Be,fe,hn]),d.useEffect(()=>{De.length==Re.length&&I(!1)},[De.length,Re.length]),_n(Rc,{className:"stDataFrame","data-testid":"stDataFrame",hasCustomizedScrollbars:k,ref:h,onMouseDown:de=>{if(h.current&&k){const st=h.current.getBoundingClientRect();E&&st.height-(Ic+1)<de.clientY-st.top&&de.stopPropagation(),R&&st.width-(Ic+1)<de.clientX-st.left&&de.stopPropagation()}},onBlur:de=>{!b&&!T&&!de.currentTarget.contains(de.relatedTarget)&&ue(!0,!0)},children:[_n(fg,{isFullScreen:a,disableFullscreenMode:i,locked:Pe&&!me||ve||T&&b||C,onExpand:l,onCollapse:s,target:Rc,children:[(me&&Pe||pe&&tt)&&lt(di,{label:"Clear selection",icon:wg,onClick:()=>{ue(),It()}}),ie&&Pe&&lt(di,{label:"Delete row(s)",icon:kg,onClick:()=>{Lt&&(Lt(et),It())}}),ie&&!Pe&&lt(di,{label:"Add row",icon:Bc,onClick:()=>{var de;xt&&(M(!0),xt(),It(),(de=g.current)==null||de.scrollTo(0,fe,"vertical"))}}),!j&&De.length>Re.length&&lt(r1,{columns:De,columnOrder:P,setColumnOrder:W,hideColumn:V,showColumn:Je,isOpen:C,onClose:()=>I(!1),children:lt(di,{label:"Show/hide columns",icon:Uc,onClick:()=>I(!0)})}),!G&&!j&&lt(di,{label:"Download as CSV",icon:Mg,onClick:()=>Ve()}),!j&&lt(di,{label:"Search",icon:Wc,onClick:()=>{O?S(!1):(M(!0),S(!0)),It()}})]}),lt(lg,{"data-testid":"stDataFrameResizable",ref:f,defaultSize:Be,style:{border:`${m.tableBorderWidth}px solid ${m.glideTheme.borderColor}`,borderRadius:`${m.tableBorderRadius}`},minHeight:Xt,maxHeight:Ot,minWidth:rn,maxWidth:Tt,size:Be,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!0,bottomLeft:!1,topLeft:!1},grid:[1,Ft],snapGap:Ft/3,onResizeStop:(de,st,Wt,dn)=>{if(f.current){const Zt=2*m.tableBorderWidth;Bt({width:f.current.size.width,height:Ot-f.current.size.height===Zt?f.current.size.height+Zt:f.current.size.height})}},children:lt(Xv,{className:"stDataFrameGlideDataEditor","data-testid":"stDataFrameGlideDataEditor",ref:g,columns:hn,rows:j?1:fe,minColumnWidth:m.minColumnWidth,maxColumnWidth:m.maxColumnWidth,maxColumnAutoWidth:m.maxColumnAutoWidth,rowHeight:Ft,headerHeight:m.defaultHeaderHeight,getCellContent:j?Pt:wt,onColumnResize:T?void 0:pn,resizeIndicator:"header",freezeColumns:nr,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:!0,getCellsForSelection:!0,rowMarkers:"none",rangeSelect:T?"cell":"rect",columnSelect:"none",rowSelect:"none",onColumnMoved:pe?void 0:We,onItemHovered:de=>{v==null||v(de),yt==null||yt(de)},keybindings:{downFill:!0},onKeyDown:de=>{(de.ctrlKey||de.metaKey)&&de.key==="f"&&(S(st=>!st),de.stopPropagation(),de.preventDefault())},showSearch:O,searchResults:O?void 0:[],onSearchClose:()=>{S(!1),It()},onHeaderClicked:(de,st)=>{!ae||pe||(O&&S(!1),me&&Pe?ue():ue(!0,!0),Se(de,"auto"))},gridSelection:et,onGridSelectionChange:de=>{(b||T)&&(it(de),ln!==void 0&&It())},theme:m.glideTheme,getRowThemeOverride:p,onMouseMove:de=>{de.kind==="out-of-bounds"&&b?M(!1):de.kind!=="out-of-bounds"&&!b&&M(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:0,...k&&{paddingBottom:E?-6:void 0,paddingRight:R?-6:void 0}},provideEditor:Oe,drawCell:Dn,customRenderers:yn,imageEditorOverride:k1,headerIcons:m.headerIcons,validateCell:nn,onHeaderMenuClick:(de,st)=>{D({columnIdx:de,headerBounds:st})},onPaste:!1,...me&&{rowMarkers:{kind:"checkbox-visible",checkboxStyle:"square",theme:{bgCell:m.glideTheme.bgHeader,bgCellMedium:m.glideTheme.bgHeader,textMedium:m.glideTheme.textLight}},rowSelectionMode:he?"multi":"auto",rowSelect:n?"none":he?"multi":"single",rowSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...pe&&{columnSelect:n?"none":ze?"multi":"single",columnSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...!j&&e.editingMode!==z&&!n&&{fillHandle:!T,onCellEdited:Dt,onPaste:Yt,onDelete:Lt},...!j&&e.editingMode===$&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkers:{kind:"checkbox",checkboxStyle:"square",theme:{bgCell:m.glideTheme.bgHeader,bgCellMedium:m.glideTheme.bgHeader}},rowSelectionMode:"multi",rowSelect:n?"none":"multi",onRowAppended:n?void 0:xt,onHeaderClicked:void 0}})}),ln&&ln.content&&lt(sC,{top:ln.top,left:ln.left,content:ln.content,clearTooltip:It}),_&&Nc.createPortal(lt(Qv,{top:_.headerBounds.y+_.headerBounds.height,left:_.headerBounds.x+_.headerBounds.width,columnKind:ce[_.columnIdx].kind,onCloseMenu:()=>D(void 0),onSortColumn:ae?de=>{O&&S(!1),ue(!(me&&Pe),!0),Se(_.columnIdx,de,!0)}:void 0,isColumnPinned:ce[_.columnIdx].isPinned,onUnpinColumn:()=>{Cn(ce[_.columnIdx].id)},onPinColumn:()=>{Nt(ce[_.columnIdx].id)},onHideColumn:()=>{V(ce[_.columnIdx].id)},onChangeFormat:de=>{Hn(ce[_.columnIdx].id,de),setTimeout(()=>{var st;(st=g.current)==null||st.remeasureColumns(mt.fromSingleSelection(_.columnIdx))},100)},onAutosize:()=>{var de;(de=g.current)==null||de.remeasureColumns(mt.fromSingleSelection(_.columnIdx))}}),document.querySelector("#portal"))]})}const cC=cg(uC),dC=d.memo(cC),MC=Object.freeze(Object.defineProperty({__proto__:null,default:dC},Symbol.toStringTag,{value:"Module"}));export{M0 as C,xd as T,vi as a,Og as b,MC as c,mi as i,jm as m,mn as s};
